<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <!--
      HACK: this permission should not be needed on android 12+ devices anymore,
      but in fact some manufacturers still need it for BLE to properly work :
      https://stackoverflow.com/a/72370969
    -->
    <uses-permission android:name="android.permission.BLUETOOTH" tools:remove="android:maxSdkVersion"/>
    <!--
      should normally only be needed on android < 12 if you want to:
      - activate bluetooth programmatically
      - discover local BLE devices
      see: https://developer.android.com/guide/topics/connectivity/bluetooth/permissions#discover-local-devices.
      Same as above, may still be wrongly needed by some manufacturers on android 12+.
     -->
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" tools:remove="android:maxSdkVersion"/>

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" android:maxSdkVersion="28"/>
    <uses-permission-sdk-23 android:name="android.permission.ACCESS_FINE_LOCATION" android:maxSdkVersion="30"/>
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Only when targeting Android 12 or higher -->
    <!--
      Please make sure you read the following documentation
      to have a better understanding of the new permissions.
      https://developer.android.com/guide/topics/connectivity/bluetooth/permissions#assert-never-for-location
    -->

    <!-- Needed if your app search for Bluetooth devices. -->
     <!--
      If your app doesn't use Bluetooth scan results to derive physical location information,
      you can strongly assert that your app doesn't derive physical location.
    -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"
                     android:usesPermissionFlags="neverForLocation"/>
    <!-- Needed if you want to interact with a BLE device. -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
    <!-- Needed if your app makes the current device discoverable to other Bluetooth devices. -->
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE"/>

    <!-- Add this line if your application always requires BLE. More info can be found on:
       https://developer.android.com/guide/topics/connectivity/bluetooth-le.html#permissions
     -->
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="true"/>
<!-- for react native camera -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>

    <!-- Required by react-native-in-app-notification -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>


  <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme">
       <!-- You will only need to add this meta-data tag, but make sure it's a child of application -->
      <meta-data android:name="com.google.android.geo.API_KEY" android:value="AIzaSyCng5NRelhUpUTXrKp9heKZ9Hk9yCmUkhk"/>
      <!-- react-native-background-geolocation licence -->
      <meta-data android:name="com.transistorsoft.locationmanager.license" android:value="f566a3f8b9df7d807c9569c895027055595f330696b3924be264963fd41f0707" />
      <!-- Nut SDK integration -->
        <service
            android:name="com.nutspace.nut.api.ble.BleService"
            android:enabled="true"
            android:exported="false"
            android:isolatedProcess="false"
            android:label="nutspace" />
            <!-- Nut SDK end -->
    <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>
      <!--for showing local notifications -->
    <meta-data
            android:name="com.dieam.reactnativepushnotification.notification_foreground"
            android:value="false" />
    <meta-data
            android:name="com.dieam.reactnativepushnotification.notification_color"
            android:resource="@color/white" />
            <!-- Nut SDK integration -->
     <meta-data
            android:name="com.nutspace.nut.api.openId"
            android:value="e613fd6470a79fd4147f041810dce52f" />
            <!-- Nut SDK end -->

    <receiver android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationActions" />
    <receiver android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationPublisher" />
    <receiver
            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationBootEventReceiver"
            android:exported="false">
      <intent-filter>
        <action android:name="android.intent.action.BOOT_COMPLETED" />
        <action android:name="android.intent.action.QUICKBOOT_POWERON" />
        <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
      </intent-filter>
    </receiver>
    <service
      android:name="com.marianhello.bgloc.service.LocationServiceImpl"
      android:enabled="true"
      android:exported="false" />
    <receiver
        android:name="com.marianhello.bgloc.BootReceiver"
        android:enabled="true"
        android:exported="false">
        <intent-filter>
            <action android:name="android.intent.action.BOOT_COMPLETED" />
        </intent-filter>
    </receiver>
    <service
            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationListenerService"
            android:exported="false">
      <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT" />
      </intent-filter>
    </service>
  </application>
</manifest>