package com.qeridoo;

import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.util.Log;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanCallback;
import android.bluetooth.le.ScanResult;
import android.os.Handler;
import android.os.Looper;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.content.BroadcastReceiver;
import android.content.IntentFilter;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableArray;

import com.nutspace.nut.api.model.BleDevice;
import com.nutspace.nut.api.BleDeviceConsumer;
import com.nutspace.nut.api.BleDeviceManager;
import com.nutspace.nut.api.callback.ScanResultCallback;
import com.nutspace.nut.api.callback.EventCallback;

import com.nutspace.nut.api.callback.ConnectStateChangedCallback;


import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.facebook.react.modules.core.DeviceEventManagerModule; // Add this import


import java.util.ArrayList;
import java.util.List;
import java.util.Set;


public class NutSdkModule extends ReactContextBaseJavaModule implements ScanResultCallback, EventCallback , ConnectStateChangedCallback {
    private BleDeviceManager mManager;
    private BleDeviceConsumer bleConsumer;
    private ScanResultCallback scanResultCallback;
    private ArrayList<BleDevice> scannedDevices = new ArrayList<>();
    private JSONArray scannedDevicesJsonArray = new JSONArray();
    private int btLvl;

    private BluetoothManager bluetoothManager;
    private BluetoothAdapter bluetoothAdapter;
     // Constants for event names
     private static final String BLUETOOTH_STATE_CHANGED_EVENT = "BluetoothStateChanged";

    ArrayList<BleDevice> mBleDeviceList = new ArrayList<>();

    ReactApplicationContext reactContext;

    NutSdkModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
        bluetoothManager = (BluetoothManager) reactContext.getSystemService(Context.BLUETOOTH_SERVICE);
        bluetoothAdapter = bluetoothManager.getAdapter();
    }

    @Override
    public String getName() {
        return "NutSdkModule";
    }


     // Method to register a BroadcastReceiver for Bluetooth state changes
     @ReactMethod
     public void registerBluetoothStateReceiver() {
         IntentFilter filter = new IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED);
         getReactApplicationContext().registerReceiver(bluetoothStateReceiver, filter);
     }
 
     // BroadcastReceiver for Bluetooth state changes
     private BroadcastReceiver bluetoothStateReceiver = new BroadcastReceiver() {
         @Override
         public void onReceive(Context context, Intent intent) {
             final String action = intent.getAction();
             if (action != null && action.equals(BluetoothAdapter.ACTION_STATE_CHANGED)) {
                 int state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR);
                 switch (state) {
                     case BluetoothAdapter.STATE_OFF:
                         sendBluetoothStateEvent(false);
                         break;
                     case BluetoothAdapter.STATE_ON:
                         sendBluetoothStateEvent(true);
                         break;
                     default:
                         // Do nothing for other states
                         break;
                 }
             }
         }
     };
 
     // Method to send Bluetooth state event to React Native component
     private void sendBluetoothStateEvent(boolean isEnabled) {
         WritableMap eventData = Arguments.createMap();
         eventData.putBoolean("isEnabled", isEnabled);
         getReactApplicationContext()
             .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
             .emit(BLUETOOTH_STATE_CHANGED_EVENT, eventData);
     }



     @ReactMethod
public void checkBluetoothStatus(Promise promise) {
    BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
    if (bluetoothAdapter == null) {
        // Bluetooth is not supported on this device
        promise.reject("Bluetooth not supported");
        return;
    }

    int state = bluetoothAdapter.getState();
    switch (state) {
        case BluetoothAdapter.STATE_OFF:
            promise.resolve(false);
            break;
        case BluetoothAdapter.STATE_ON:
            promise.resolve(true);
            break;
        default:
            // Unknown state
            promise.reject("Unknown Bluetooth state");
            break;
    }
}

    @ReactMethod
    public void initializeBleManager(Promise promise) {
        mManager = BleDeviceManager.getInstance(getReactApplicationContext());
        mManager.addScanResultCallback(this); // Register this class as the callback for scan results
        mManager.addEventCallback(this);
        mManager.addConnectStateChangedCallback(this);

        Log.d("NutSDKModule", "Initializing BleDeviceManager: " + mManager);
        if (mManager != null) {
            Log.d("NutSDKModule", "BleDeviceManager initialized successfully");
            promise.resolve("Initializing BleDeviceManager successfully.");
            bindBleService(promise);
        } else {
            Log.e("NutSDKModule", "Failed to initialize BleDeviceManager");
        }
    }

    @ReactMethod
    public void bindBleService(Promise promise) {
        try {
            if (mManager != null) {
                bleConsumer = new BleDeviceConsumer() {
                    @Override
                    public boolean bindService(Intent intent, ServiceConnection serviceConnection, int i) {
                        // Bind the Bluetooth service
                        return getReactApplicationContext().bindService(intent, serviceConnection, i);
                    }

                    @Override
                    public void unbindService(ServiceConnection serviceConnection) {
                        // Unbind the Bluetooth service
                        getReactApplicationContext().unbindService(serviceConnection);
                    }

                    @Override
                    public void onServiceBound() {
                        // Implement method if needed
                    }

                    @Override
                    public Context getApplicationContext() {
                        return getReactApplicationContext(); // Return the application context
                    }
                };
                mManager.bind(bleConsumer);
                Log.d("NutSDKModule", "BleService bound successfully");
                promise.resolve("Connection initiated successfully.");
            }
        } catch (Exception e) {
            Log.e("NutSDKModule", "Failed to bind BleDeviceManager: " + e.getMessage());
        }
    }

    @ReactMethod
    public void unbindBleService() {
        try {
            if (mManager != null) {
                mManager.unbind(bleConsumer);
                Log.d("NutSDKModule", "Unbinding BleDeviceManager: " + mManager);
            }
        } catch (Exception e) {
            Log.e("NutSDKModule", "Failed to unbind BleDeviceManager: " + e.getMessage());
        }
    }

    @ReactMethod
    public void startScanning() {
        try {
            if (mManager != null) {
                mManager.startScan();
                Log.d("NutSDKModule", "Starting scanning: " + mManager);
            }
        } catch (Exception e) {
            Log.e("NutSDKModule", "Failed to start scanning: " + e.getMessage());
        }
    }

    @ReactMethod
    public void stopScanning() {
        try {
            if (mManager != null) {
                mManager.stopScan();
                Log.d("NutSDKModule", "Stopping scanning: " + mManager);
            }
        } catch (Exception e) {
            Log.e("NutSDKModule", "Failed to stop scanning: " + e.getMessage());
        }
    }

    // This method will be called when a BLE device is scanned
    
    @Override
    public void onBleDeviceScanned(BleDevice device) {
        // Log the scanned device in the desired format
        Log.d("NutSDKModule", "name=" + device.name
                + "\naddress=" + device.address
                + "\nrssi=" + device.rssi
                + "\nid=" + device.id
                + "\nproductId=" + device.productId
                + "\nhardware=" + device.hardware);
    
        // Check if the device with the same address already exists in the array
        boolean deviceExists = false;
        for (int i = 0; i < scannedDevicesJsonArray.length(); i++) {
            try {
                JSONObject storedDevice = scannedDevicesJsonArray.getJSONObject(i);
                if (storedDevice.getString("address").equals(device.address)) {
                    deviceExists = true;
                    break;
                }
            } catch (JSONException e) {
                Log.e("NutSDKModule", "Error accessing stored device: " + e.getMessage());
            }
        }
    
        // If the device doesn't already exist, add it to the JSON array
        if (!deviceExists) {
            try {
                JSONObject deviceJson = new JSONObject();
                deviceJson.put("name", device.name);
                deviceJson.put("address", device.address);
                deviceJson.put("rssi", device.rssi);
                deviceJson.put("id", device.id);
                deviceJson.put("productId", device.productId);
                deviceJson.put("hardware", device.hardware);
    
                scannedDevicesJsonArray.put(deviceJson);
            } catch (JSONException e) {
                Log.e("NutSDKModule", "Error converting device to JSON: " + e.getMessage());
            }
        }

        // Emit the updated scanned devices array
        sendScannedDevicesEvent(scannedDevicesJsonArray);

         // Check if the device with the same address already exists in the list
         boolean deviceExisting = false;
         for (BleDevice storedDevice : scannedDevices) {
         if (storedDevice.address.equals(device.address)) {
            deviceExisting = true;
            break;
         }
         }

             // If the device doesn't already exist, add it to the list
            if (!deviceExisting) {
           scannedDevices.add(device);
        }
    }
    
    
    @ReactMethod
    public void getScannedDevices(Promise promise) {
        try {
            // Convert the list of BleDevice objects to a list of plain Java objects
            Log.d("NutSDKModule", " device Tada Tada Tada...--->: " + scannedDevicesJsonArray);
            promise.resolve(scannedDevicesJsonArray.toString());
        } catch (Exception e) {
            // Reject the promise if there's an error
            promise.reject("ERROR", e.getMessage());
        }
    }
   

 // Modify the connectToDevice method to use the scannedDevices list
 @ReactMethod
public void connectToDevice(String deviceAddress , Promise promise) {
    try {
        if (mManager == null) {
            Log.e("NutSDKModule", "BleDeviceManager is not initialized.");
            promise.reject("BleDeviceManager is not initialized.");
            return;
        }

        // Check if scannedDevices list is empty
        if (scannedDevices.isEmpty()) {
            Log.e("NutSDKModule", "No devices scanned. Please start scanning first.");
            promise.reject("No devices scanned. Please start scanning first.");
            return;
        }

        // Find the device in the scanned devices list using its address
        for (BleDevice device : scannedDevices) {
            // Check if the device's address matches the given address
            if (device.address.equals(deviceAddress)) {
                // Connect to the device
                ReactApplicationContext reactContext = getReactApplicationContext();
                mManager.connect(reactContext, device, true); // Assuming isAuto is set to true
                // Log the name of the device
                Log.d("NutSDKModule", "Connecting to device: " + device.name);
                 // Create a JSON object with device information
                 JSONObject deviceJson = new JSONObject();
                 deviceJson.put("name", device.name);
                 deviceJson.put("address", device.address);
                 deviceJson.put("rssi", device.rssi);
                 deviceJson.put("id", device.id);
                 deviceJson.put("productId", device.productId);
                 deviceJson.put("hardware", device.hardware);
                 
                 // Resolve the Promise with the device information JSON object
                 promise.resolve(deviceJson.toString());
                 return; // Exit the loop once the dp once the device is found and connection process initiated
  
            }
        }

        // If the device with the given address is not found
        Log.e("NutSDKModule", "Device not found with address: " + deviceAddress);
        promise.reject("Device not found with address: " + deviceAddress);
    } catch (Exception e) {
        Log.e("NutSDKModule", "Error connecting to device: " + e.getMessage());
        promise.reject("Error connecting to device: " + e.getMessage());
    }
}



@ReactMethod
public void ringDevice(String deviceAddress, Promise promise) {
    try {
        if (mManager == null) {
            Log.e("NutSDKModule", "BleDeviceManager is not initialized.");
            promise.reject("BleDeviceManager is not initialized.");
            return;
        }

        // Find the device in the scanned devices list using its address
        for (BleDevice device : scannedDevices) {
            // Check if the device's address matches the given address
            if (device.address.equals(deviceAddress)) {
                // Ring the device
                mManager.changeRingState(device, BleDevice.STATE_RING);
                promise.resolve(null); // Resolve the Promise without any value
                return;
            }
        }

        // If the device with the given address is not found
        Log.e("NutSDKModule", "Device not found with address: " + deviceAddress);
        promise.reject("Device not found with address: " + deviceAddress);
    } catch (Exception e) {
        Log.e("NutSDKModule", "Error ringing device: " + e.getMessage());
        promise.reject("Error ringing device: " + e.getMessage());
    }
}

@ReactMethod
public void stopRingingDevice(String deviceAddress, Promise promise) {
    try {
        if (mManager == null) {
            Log.e("NutSDKModule", "BleDeviceManager is not initialized.");
            promise.reject("BleDeviceManager is not initialized.");
            return;
        }

        // Find the device in the scanned devices list using its address
        for (BleDevice device : scannedDevices) {
            // Check if the device's address matches the given address
            if (device.address.equals(deviceAddress)) {
                // Stop ringing the device
                mManager.changeRingState(device, BleDevice.STATE_QUIT);
                promise.resolve(null); // Resolve the Promise without any value
                return;
            }
        }

        // If the device with the given address is not found
        Log.e("NutSDKModule", "Device not found with address: " + deviceAddress);
        promise.reject("Device not found with address: " + deviceAddress);
    } catch (Exception e) {
        Log.e("NutSDKModule", "Error stopping ringing device: " + e.getMessage());
        promise.reject("Error stopping ringing device: " + e.getMessage());
    }
}

@ReactMethod
    public void isConnected(String deviceAddress, Promise promise) {
        if (bluetoothAdapter == null) {
            Log.d("NutSDKModule", "Bluetooth not supported  JAVA");
            promise.reject("Bluetooth not supported");
            return;
        }

        BluetoothDevice device = bluetoothAdapter.getRemoteDevice(deviceAddress);
        if (device == null) {
            Log.d("NutSDKModule", "Device not found with address  JAVA: " + deviceAddress);
            promise.reject("Device not found with address: " + deviceAddress);
            return;
        }

        int connectionState = bluetoothManager.getConnectionState(device, BluetoothProfile.GATT);
        boolean isConnected = connectionState == BluetoothProfile.STATE_CONNECTED;
        Log.d("NutSDKModule", "Device is connected JAVA: " + isConnected);
        promise.resolve(isConnected);
    }


@ReactMethod
public void disconnectFromDevice(String deviceAddress, Promise promise) {
    try {
        if (mManager == null) {
            Log.e("NutSDKModule", "BleDeviceManager is not initialized.");
            promise.reject("BleDeviceManager is not initialized.");
            return;
        }

        // Find the device in the scanned devices list using its address
        for (BleDevice device : scannedDevices) {
            // Check if the device's address matches the given address
            if (device.address.equals(deviceAddress)) {
                // Disconnect from the device
                mManager.disconnect(getReactApplicationContext(), device);
                // Log the name of the device
                Log.d("NutSDKModule", "Disconnecting from device: " + device.name);
                promise.resolve("Disconnected from device"); // Resolve the Promise without any value
                return;
            }
        }

        // If the device with the given address is not found
        Log.e("NutSDKModule", "Device not found with address: " + deviceAddress);
        promise.reject("Device not found with address: " + deviceAddress);
    } catch (Exception e) {
        Log.e("NutSDKModule", "Error disconnecting from device: " + e.getMessage());
        promise.reject("Error disconnecting from device: " + e.getMessage());
    }
}

// Implement the onConnectStateChanged method
// @Override
// public void onConnectStateChanged(BleDevice device, int state) {
//     // Check if the state indicates that the device is connected
//     if (state == BleDevice.STATE_CONNECTED) {
//         Log.d("NutSDKModule", "Device connected: " + device.name);
//         // You can perform any actions here when the device is connected
//     } else {
//         Log.d("NutSDKModule", "Device disconnected: " + device.name);
//         // You can perform any actions here when the device is disconnected
//     }
// }

// Read the battery level
@ReactMethod
public void readBattery(String deviceAddress, Promise promise) {
    try {
        if (mManager == null) {
            Log.e("NutSDKModule", "BleDeviceManager is not initialized.");
            promise.reject("BleDeviceManager is not initialized.");
            return;
        }

        // Find the device in the scanned devices list using its address
        for (BleDevice device : scannedDevices) {
            if (device.address.equals(deviceAddress)) {
                // Read the battery level
                mManager.readBattery(device);
                return;
            }
        }

        Log.e("NutSDKModule", "Device not found with address: " + deviceAddress);
        promise.reject("Device not found with address: " + deviceAddress);
    } catch (Exception e) {
        Log.e("NutSDKModule", "Error reading battery: " + e.getMessage());
        promise.reject("Error reading battery: " + e.getMessage());
    }
}

// Handle the battery level event
@Override
public void onBatteryChangedEvent(BleDevice device, int battery) {
    // You can send the battery level to your React Native code here
    WritableMap params = Arguments.createMap();
    Log.d("NutSDKModule", "Battery level: " + battery);
    params.putInt("battery", battery);
    reactContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
            .emit("onBatteryChanged", params);
}

// Handle the RSSI event
@Override
public void onRssiChangedEvent(BleDevice device, int rssi) {
    // You can send the RSSI to your React Native code here
}


@ReactMethod
public void enableAntiLost(String deviceAddress, boolean enable, Promise promise) {
    try {
        if (mManager == null) {
            Log.e("NutSdkModule", "BleDeviceManager is not initialized.");
            promise.reject("BleDeviceManager is not initialized.");
            return;
        }

        // Find the device in the scanned devices list using its address
        for (BleDevice device : scannedDevices) {
            // Check if the device's address matches the given address
            if (device.address.equals(deviceAddress)) {
                // Enable or disable the disconnect alert feature
             // To enable the device disconnect alert feature
                mManager.setDeviceAlert(device, enable);
                Log.d("NutSdkModule", "Device Antilost is: " + (enable ? "enabled" : "disabled"));
                promise.resolve("Device Antilost is: " + (enable ? "enabled" : "disabled")); // Resolve the Promise without any value
                return;
            }
        }

        // If the device with the given address is not found
        Log.e("NutSdkModule", "Device not found with address: " + deviceAddress);
        promise.reject("Device not found with address: " + deviceAddress);
    } catch (Exception e) {
        Log.e("NutSdkModule", "Error enabling/disabling disconnect alert: " + e.getMessage());
        promise.reject("Error enabling/disabling disconnect alert: " + e.getMessage());
    }
}

    // @ReactMethod
    // public void isConnected(String deviceAddress, Promise promise) {
    //     if (bluetoothAdapter == null) {
    //         promise.reject("Bluetooth not supported");
    //         return;
    //     }

    //     BluetoothDevice device = bluetoothAdapter.getRemoteDevice(deviceAddress);
    //     if (device == null) {
    //         promise.reject("Device not found with address: " + deviceAddress);
    //         return;
    //     }

    //     int connectionState = bluetoothManager.getConnectionState(device, BluetoothProfile.GATT);
    //     boolean isConnected = connectionState == BluetoothProfile.STATE_CONNECTED;
    //     promise.resolve(isConnected);
    // }

@Override
public void onSwitchDFUMode(BleDevice device, boolean enable) {
    // Empty implementation
    Log.d("NutSDKModule", "onSwitchDFUMode: " + device.name + " - " + device.address + " - " + enable);
}

@Override
public void onDeviceAlert(BleDevice device, boolean enable) {
    // Empty implementation
}

@Override
public void onDeviceRingStateChangedEvent(BleDevice device, int previousState, int newState) {
    // Empty implementation
}

@Override
public void onClickEvent(BleDevice device, int type) {
    // Empty implementation
}

@Override
public void onConnect(BleDevice device) {
    if (device == null) {
        Log.e("NutSDKModule", "Device is null. Something's wrong.");
        return;
    }

    // Device connected successfully
    try {
        Log.d("NutSDKModule", "Device connected successfully JAVA OnCONNECT: " + device.name);

        // Emit a device connected event to React Native
        WritableMap eventData = Arguments.createMap();
        eventData.putString("name", device.name);
        eventData.putString("address", device.address);
        eventData.putInt("rssi", device.rssi);
        eventData.putString("id", device.id);
        eventData.putString("productId", String.valueOf(device.productId));
        eventData.putString("hardware", device.hardware);

        reactContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
            .emit("DeviceConnected", eventData);
    } catch (NullPointerException e) {
        Log.e("NutSDKModule", "Device name is null, something's wrong.");
    }
}


// Define an interface for sending events to React component
interface EventSender {
    void sendDeviceDisconnectedEvent(String deviceName);
}

// Existing methods...

@Override
    public void onDisconnect(BleDevice device, int error) {
        if (device == null) {
            Log.e("NutSDKModule", "Device is null. Something's wrong.");
            return;
        }

        // Device disconnected
        try {
            Log.d("NutSDKModule", "Device disconnected successfully FROM JAVA: " + device.name);

            // Notify your React Native component about the disconnection
            sendDeviceDisconnectedEvent(device.name);
        } catch (NullPointerException e) {
            Log.e("NutSDKModule", "Device name is null, something's wrong.");
        }
    }

    // Method to send device disconnected event to React component
    private void sendDeviceDisconnectedEvent(String deviceName) {
        Log.d("NutSDKModule", "Sending DeviceDisconnected event for device: " + deviceName);
        WritableMap eventData = Arguments.createMap();
        eventData.putString("deviceName", deviceName);
        reactContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
            .emit("DeviceDisconnected", eventData);
    }


    // Method to emit the scanned devices array to React component
    private void sendScannedDevicesEvent(JSONArray scannedDevicesJsonArray) {
        WritableArray scannedDevicesArray = Arguments.createArray();
        for (int i = 0; i < scannedDevicesJsonArray.length(); i++) {
            try {
                JSONObject device = scannedDevicesJsonArray.getJSONObject(i);
                WritableMap deviceMap = Arguments.createMap();
                deviceMap.putString("name", device.getString("name"));
                deviceMap.putString("address", device.getString("address"));
                deviceMap.putInt("rssi", device.getInt("rssi"));
                deviceMap.putString("id", device.getString("id"));
                deviceMap.putString("productId", device.getString("productId"));
                
                // Check if the "hardware" field exists before trying to retrieve its value
                if (device.has("hardware")) {
                    deviceMap.putString("hardware", device.getString("hardware"));
                } else {
                    deviceMap.putString("hardware", "Unknown");
                }
                
                scannedDevicesArray.pushMap(deviceMap);
            } catch (JSONException e) {
                Log.e("NutSDKModule", "Error converting device to JSON: " + e.getMessage());
            }
        }
        Log.d("NutSDKModule", "Emitting ScannedDevices event with " + scannedDevicesArray.size() + " devices");
        reactContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
            .emit("ScannedDevices", scannedDevicesArray);
    }
    



}


