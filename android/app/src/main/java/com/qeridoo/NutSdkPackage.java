// AndroidBridgeReactPackage.java

package com.qeridoo;

import com.facebook.react.ReactPackage;
import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.uimanager.ViewManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

class NutSdkPackage implements ReactPackage {

    public NutSdkPackage() {
    }

    /**
     * Create NativeModules for React Native
     *
     * @param reactContext ReactApplicationContext
     * @return List of NativeModules
     */
    @Override
    public List<NativeModule> createNativeModules(ReactApplicationContext reactContext) {
        List<NativeModule> modules = new ArrayList<>();

        // Add the NutSdkModule to the modules list
        modules.add(new NutSdkModule(reactContext));

        return modules;
    }


    /**
     * Create ViewManagers for React Native
     *
     * @param reactContext ReactApplicationContext
     * @return List of ViewManagers
     */
    @Override
    public List<ViewManager> createViewManagers(ReactApplicationContext reactContext) {
        // Return an empty list as we don't have any native views to register
        // See https://reactnative.dev/docs/native-components-android#2-register-your-native-component-in-java
        return Collections.emptyList();
    }


}