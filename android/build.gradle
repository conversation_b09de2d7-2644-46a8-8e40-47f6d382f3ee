buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.22"

        googlePlayServicesVersion = "+"// default: "+" FOR react-native-geolocation-service
        firebaseMessagingVersion = "21.1.0" // default: "21.1.0"


    }
    repositories {
        google()
        mavenCentral()

          // ADD THIS FOR REACT NATIVE IMAGE PICKER
        maven { url 'https://maven.google.com' }

        // ADD THIS FOR REACT NATIVE IMAGE PICKER
        maven { url "https://www.jitpack.io" }
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath('com.google.gms:google-services:4.3.3')

    }
}
allprojects {   // <-- NOTE:  allprojects container -- If you don't see this, create it.
    repositories {

       // Required for react-native-background-geolocation
       maven { url("${project(':react-native-background-geolocation').projectDir}/libs") }
       maven { url 'https://developer.huawei.com/repo/' }
       // Required for react-native-background-fetch
       maven { url("${project(':react-native-background-fetch').projectDir}/libs") }
    }
}

apply plugin: "com.facebook.react.rootproject"
