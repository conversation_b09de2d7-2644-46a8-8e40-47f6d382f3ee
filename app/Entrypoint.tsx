/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import 'react-native-gesture-handler';
import React, {useEffect, useState} from 'react';
import {
  Platform,
  StatusBar,
  Text,
  TextInput,
  View,
  ActivityIndicator,
} from 'react-native';
// import codePush from 'react-native-code-push';
import {Provider} from 'react-redux';
// import SplashScreen from "react-native-splash-screen";
import {PersistGate} from 'redux-persist/es/integration/react';
import {persistor, store} from './redux/store/configureStore';
import {LogBox} from 'react-native';
import {ThemeProvider} from '@react-navigation/native';
import CTopNotify from './components/CTopNotify';
import {BaseStyles} from './config/colors';
import Navigator from './navigation';
import {initTranslate} from './lang/Translate';
import Toast from 'react-native-simple-toast';
import {PERMISSIONS, RESULTS, check, request} from 'react-native-permissions';

LogBox.ignoreLogs(['Warning: ...', '`flexWrap: `wrap``', 'VirtualizedLists']); // Ignore log notification by message
LogBox.ignoreAllLogs();

// const codePushOptions = {
//   installMode: codePush.InstallMode.IMMEDIATE,
//   checkFrequency: codePush.CheckFrequency.ON_APP_START,

//   updateDialog: {
//     appendReleaseDescription: true,
//     descriptionPrefix: "\n\nWhat's New:",
//     mandatoryContinueButtonLabel: 'install',
//   },
// };

const IOS = Platform.OS === 'ios';

/**
 *
 * Main entryfile for app loding
 * @export indexExport
 * @module EntryPoint
 * @return {React node showing navigator}
 * <AUTHOR>
 *
 */

const EntryPoint = (): any => {
  const [loading, setLoading] = useState<any>(true);
  const [processing, setProcessing] = useState<any>(false);

  // /* Codepush Events */
  // const codePushStatusDidChange = (status: any) => {
  //   console.log('Codepush status change ==> ', status);
  //   const codepushStatus = status;
  //   switch (codepushStatus) {
  //     case codePush.SyncStatus.CHECKING_FOR_UPDATE:
  //       console.log('Codepush: Checking for updates.');
  //       break;
  //     case codePush.SyncStatus.DOWNLOADING_PACKAGE:
  //       setProcessing(true);
  //       Toast.show(
  //         'New app update is available and being downloaded.',
  //         Toast.LONG,
  //       );
  //       console.log('Codepush: Downloading package.');
  //       break;
  //     case codePush.SyncStatus.INSTALLING_UPDATE:
  //       Toast.show(
  //         'New app update is available and being installed.',
  //         Toast.LONG,
  //       );
  //       console.log('Codepush: Installing update.');
  //       break;
  //     case codePush.SyncStatus.UP_TO_DATE:
  //       console.log('Codepush: Up-to-date.');
  //       break;
  //     case codePush.SyncStatus.UPDATE_INSTALLED:
  //       console.log('Codepush: Update installed.');
  //       break;
  //   }
  // };

  const onBeforeLift = () => {
    if (store) {
      initTranslate(store);
      setLoading(false);
    }
  };

  /**
   * @function requestNotificationPermission to ask for notifiction permissions
   * <AUTHOR>
   */
  const requestNotificationPermission = async () => {
    const result = await request(PERMISSIONS.ANDROID.POST_NOTIFICATIONS);
    return result;
  };

  /**
   * @function checkNotificationPermission to check for notifiction permissions
   * <AUTHOR>
   */
  const checkNotificationPermission = async () => {
    const result = await check(PERMISSIONS.ANDROID.POST_NOTIFICATIONS);
    return result;
  };

  /**
   * @function askNotifictionPermission to ask for notifiction permissions
   * <AUTHOR>
   */
  const askNotifictionPermission = async () => {
    if (!IOS) {
      try {
        const checkPermission = await checkNotificationPermission();
        if (checkPermission !== RESULTS.GRANTED) {
          const request = await requestNotificationPermission();
          console.log(
            '🚀 ~ file: Entrypoint.tsx:254 ~ askNotifictionPermission ~ request:',
            request,
          );
          if (request !== RESULTS.GRANTED) {
            // Toast.show(
            //   'Notifiction permission revoked by user. Please allow from settings.',
            // );
          }
        }
      } catch (error) {
        console.log('askNotifictionPermission ~ error:', error);
      }
    }
  };

  useEffect(() => {
    // codePush.notifyAppReady();
    askNotifictionPermission();
    // SplashScreen.hide();

    if (!IOS) {
      StatusBar.setBackgroundColor('#0000', true);
    }
  }, []);

  return (
    <ThemeProvider
      value={{
        dark: false,
        colors: {
          primary: '',
          background: '',
          card: '',
          text: '',
          border: '',
          notification: '',
        },
      }}>
      <Provider store={store}>
        <PersistGate
          loading={
            <View style={BaseStyles.flexCenter}>
              <ActivityIndicator />
            </View>
          }
          persistor={persistor}
          onBeforeLift={onBeforeLift}>
          <StatusBar backgroundColor={'#0000'} barStyle={'light-content'} />
          {loading ? (
            <View style={BaseStyles.flexCenter}>
              <ActivityIndicator />
            </View>
          ) : (
            <>
              <Navigator />
            </>
          )}
          {processing && <CTopNotify title={'Installing Updates...'} />}
        </PersistGate>
      </Provider>
    </ThemeProvider>
  );
};

// eslint-disable-next-line curly
if ((Text as any).defaultProps == null) (Text as any).defaultProps = {};
(Text as any).defaultProps.allowFontScaling = false;
(Text as any).defaultProps = (Text as any).defaultProps || {};
(Text as any).defaultProps.allowFontScaling = false;
(TextInput as any).defaultProps = (TextInput as any).defaultProps || {};
(TextInput as any).defaultProps.allowFontScaling = false;

let indexExport = EntryPoint;
// if (!__DEV__) {
//   indexExport = codePush(codePushOptions)(EntryPoint);
// }

export default indexExport;
