import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Platform,
  Text,
  FlatList,
  Image,
  Alert,
} from 'react-native';
import {FontFamily} from '../../config/typography';
import BaseColor from '../../config/colors';
import {translate} from '../../lang/Translate';
import CButton from '../CButton';
import {Images} from '../../config/Images';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {sendErrorReport} from '../../utils/commonFunction';
import {useIsFocused} from '@react-navigation/native';
import {isEmpty} from 'lodash';
import moment from 'moment';

const IOS = Platform.OS === 'ios';
const {width, height} = Dimensions.get('window');
const Advertiesment = ({}) => {
  const isFocused = useIsFocused();
  const [advertiesArray, setAdvertiesArray] = useState([]);

  // Magazine List API Integration
  const MagazineList = async () => {
    try {
      const response = await getApiData(
        BaseSetting.endpoints.getFeedPost,
        'POST',
      );
      if (response?.success) {
        setAdvertiesArray(response?.data ? response?.data : []);
      } else {
        setAdvertiesArray([]);
      }
    } catch (error) {
      console.log('error for device list ===', error);
      sendErrorReport(error, 'saving_trip_name');
    }
  };

  useEffect(() => {
    MagazineList();
  }, [isFocused]);

  //render item for Advertisement list..
  const renderItem = ({item}) => {
    return (
      <View style={styles.itemContainer}>
        <View style={styles.shadowEffectContainer}>
          <View style={styles.imageContainer}>
            <Image
              source={
                item?.post_file ? {uri: item?.post_file} : Images.magazineImg
              }
              style={styles.imageStyle}
            />
          </View>
          <View style={{padding: 8}}>
            <Text style={styles.subtitle} numberOfLines={2}>
              {item?.post_subtitle}
            </Text>
            <Text style={styles.title} numberOfLines={1}>
              {item?.post_title}
            </Text>
            <Text style={styles.description} numberOfLines={5}>
              {item?.description}
            </Text>
            <View style={styles.dateContainer}>
              <Text style={styles.dateText}>
                {moment(item?.createdAt).format('DD MMM, YYYY')}
              </Text>
              <Text style={styles.dateText}>3 min</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {!isEmpty(advertiesArray) && (
        <Text style={styles.titleColor}>{translate('TheQeridooMagazine')}</Text>
      )}
      <FlatList
        horizontal
        data={advertiesArray}
        renderItem={renderItem}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 20,
  },
  titleColor: {
    fontSize: 22,
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
    marginBottom: 20,
  },
  subtitle: {
    fontSize: 9,
    fontFamily: FontFamily.regular,
    color: BaseColor.primary,
    lineHeight: 10,
    height: 20,
  },
  title: {
    fontSize: 10,
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    paddingVertical: 10,
  },
  description: {
    flexWrap: 'wrap',
    fontSize: 9,
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
    lineHeight: 12,
  },
  itemContainer: {
    width: width / 2.15,
    paddingRight: 25,
  },
  buttonStyle: {
    marginTop: 20,
    width: '50%',
    height: 25,
    alignSelf: 'flex-start',
  },
  imageStyle: {
    width: '100%',
    height: '100%',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  imageContainer: {
    width: width / 2.46,
    height: 110,
    borderRadius: 10,
    marginBottom: 10,
  },
  shadowEffectContainer: {
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.1,
    shadowRadius: 1.84,
    backgroundColor: '#ffffff',
    borderRadius: 10,
  },
  dateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 30,
  },
  dateText: {
    fontSize: 10,
    fontFamily: FontFamily.regular,
  },
});

export default Advertiesment;
