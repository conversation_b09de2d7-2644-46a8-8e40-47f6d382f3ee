import React from "react";
import BaseColor from "../../config/colors";
import { Image, Text, View } from "react-native";
import { FontFamily, FontWeight } from "../../config/typography";

function Avatar({ userData }) {
  // console.log(JSON.stringify(userData, null, 2));
  return (
    <View
      style={{
        width: 58,
        height: 58,
        borderWidth: 4,
        borderColor: BaseColor.whiteColor,
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 50,
      }}
    >
      {userData.user_profile ? (
        <Image
          source={{
            uri: userData.user_profile
              ? userData.user_profile
              : "https://s3-alpha-sig.figma.com/img/7a3b/ec62/a90f491d2260b7875fba4b930869dcd7?Expires=1707091200&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4&Signature=ZwgOVbfGSoZmAwAhQlSbDFPyawVVlR7J2EuIwgLqVvRi04Xmt0XXNnJILkuJlYcK93WyKSx7yN31CTXuVv6lMuKeHnh~vBcLnoYfBODB0ZYWgn3O6xB6M2QDu5mQFSL8IYjGrynQ9E0~YNKH9Q0Gko6GJCZKwtRYh5fu6iVqjE98qLfB8p7-cMrP4w~pv1hpqPsdf8QyA11SuhrbjTdKHV1GGbkLlR8fg-XEMopdT57Sor6bSNltYTSxTmbhXql6qXw9ze3Iw9wReF9apa46P-ox~Y1BHY86ulUC4QeVnqCUvpgLGbU~vg3ytfOIcLAOWWJNK4It5mnR8N87uA8P7w__",
          }}
          style={{
            width: 50,
            height: 50,
            backgroundColor: BaseColor.whiteColor,
            borderRadius: 50,
          }}
        />
      ) : (
        <View
          style={{
            backgroundColor: "white",
            width: 50,
            height: 50,
            borderRadius: 25,
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Text
            style={{
              fontSize: 24,
              color: BaseColor.primary,
              fontFamily: FontFamily.regular,
            }}
          >
            {userData && userData?.full_name?.slice(0, 1)}
          </Text>
        </View>
      )}
    </View>
  );
}

export default Avatar;
