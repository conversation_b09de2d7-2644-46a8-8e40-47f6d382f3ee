/* eslint-disable no-nested-ternary */
import React, {useRef, useEffect} from 'react';
import {ActivityIndicator, Text, TouchableOpacity, View} from 'react-native';
// import Animated, {
//   useSharedValue,
//   useAnimatedStyle,
//   withTiming,
// } from "react-native-reanimated";
import FAIcon from 'react-native-vector-icons/FontAwesome';
import {useTheme} from '@react-navigation/native';
import styles from './styles';
import {CustomIcon} from '../../config/LoadIcons';

const CButton = props => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    loader,
    title = 'Title',
    onPress = () => {},
    style,
    paddingVertical,
    titleStyle,
    smallBtn,
    done,
    anim = false,
    playAnimation,
    iconname,
    iconsize = 24,
    iconColor = BaseColor.blueLight,
    iconBg,
    rightIcon,
    rightIconColor,
    rightCustomIcon,
    rightIconSize,
    backAnim = false,
    disable = false,
    customIcon,
  } = props;

  // const animation = useSharedValue({ width: "100%", borderRadius: 30 });

  // const animationStyle = useAnimatedStyle(() => ({
  //   width: withTiming(animation.value.width, {
  //     duration: 1000,
  //   }),

  //   borderRadius: withTiming(animation.value.borderRadius, {
  //     duration: 1000,
  //   }),
  // }));

  // useEffect(() => {
  //   if (playAnimation) animation.value = { width: "16%", borderRadius: 18 };
  // }, [playAnimation]);

  // useEffect(() => {
  //   if (backAnim) animation.value = { width: "100%", borderRadius: 30 };
  // }, [backAnim]);

  const koibhi = () => (
    <>
      {iconname ? (
        <TouchableOpacity
          activeOpacity={0.7}
          disabled={disable}
          onPress={onPress}
          style={{
            height: '100%',
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <CustomIcon name={iconname} size={iconsize} color={iconColor} />
        </TouchableOpacity>
      ) : loader ? (
        <View>
          <ActivityIndicator size={22} color={iconBg || BaseColor.whiteColor} />
        </View>
      ) : done ? (
        <View>
          <CustomIcon name="check" size={18} color={BaseColor.blueDark} />
        </View>
      ) : (
        <TouchableOpacity
          activeOpacity={0.7}
          disabled={disable}
          style={{
            height: '100%',
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
          }}
          onPress={() => {
            onPress();
          }}>
          <Text style={[styles.txtStyle, titleStyle]}>{title}</Text>
          {rightCustomIcon ? (
            <CustomIcon
              name={rightIcon}
              color={rightIconColor || BaseColor.whiteColor}
              size={rightIconSize}
            />
          ) : null}
        </TouchableOpacity>
      )}
    </>
  );

  return (
    <>
      {anim ? (
        <View
          style={[
            styles.btnStyle,
            {
              backgroundColor: BaseColor.whiteColor,
              borderWidth: 1,
              borderColor: BaseColor.primary,
            },
            style,
            {paddingVertical: paddingVertical ? paddingVertical : 8},
          ]}>
          {koibhi()}
        </View>
      ) : (
        <View
          style={[
            styles.btnStyle,
            {
              backgroundColor: disable
                ? BaseColor.lightPrimary
                : BaseColor.primary,
            },
            style,
          ]}>
          {koibhi()}
        </View>
      )}
    </>
  );
};

export default CButton;
