import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily, FontWeight} from '../../config/typography';

const styles = StyleSheet.create({
  btnStyle: {
    backgroundColor: BaseColor.whiteColor,
    height: 40,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    width: '100%',
  },
  txtStyle: {
    fontSize: 16,
    color: BaseColor.whiteColor,
    letterSpacing: 0.75,
    fontFamily: FontFamily.regular,
  },
});

export default styles;
