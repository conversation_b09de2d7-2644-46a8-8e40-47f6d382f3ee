import React, { useState } from "react";
import { View, Text, TouchableOpacity, FlatList } from "react-native";
import FIcon from "react-native-vector-icons/Feather";
import BaseColor from "../../config/colors";

const CustomDropdown = ({ options, onSelect, selectedDeviceName }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const handleSelect = (option) => {
    onSelect(option);
    setShowDropdown(false);
  };

  return (
    <View>
      <TouchableOpacity onPress={() => setShowDropdown(!showDropdown)}>
        <View style={styles.inputWrapper}>
          <Text>{selectedDeviceName || "Select Device"}</Text>
          <FIcon name="down" size={24} color={BaseColor.blackColor} />
        </View>
      </TouchableOpacity>
      {showDropdown && (
        <FlatList
          data={options}
          renderItem={({ item }) => (
            <TouchableOpacity onPress={() => handleSelect(item)}>
              <Text style={styles.option}>{item.name}</Text>
            </TouchableOpacity>
          )}
          keyExtractor={(item) => item.address}
        />
      )}
    </View>
  );
};

const styles = {
  inputWrapper: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#F6F6F6",
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: "gray",
    marginBottom: 10,
  },
  option: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#ccc",
  },
};

export default CustomDropdown;
