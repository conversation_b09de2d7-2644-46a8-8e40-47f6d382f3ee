/* eslint-disable no-nested-ternary */
import React from 'react';
import {Image, Text, TouchableOpacity, View} from 'react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import {useTheme} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import {CustomIcon} from '../../config/LoadIcons';
import {FontFamily} from '../../config/typography';

const CHeader = props => {
  const {
    image,
    title,
    customLeftIcon,
    customRightIcon,
    leftIconName,
    rightIconName,
    onLeftPress,
    onRightPress,
    backBtn,
    gallery,
  } = props;

  const colors = useTheme();
  const BaseColor = colors.colors;

  const {notificationCount} = useSelector(state => state.auth);
  return (
    <View
      style={{
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
        height: 110,
        alignContent: 'center',
        paddingTop: 48,
        paddingHorizontal: 16,
        marginBottom: 16,
        backgroundColor: gallery ? BaseColor.whiteColor : '#2D2D2C',
      }}>
      <View
        style={{
          height: 40,
          width: 40,
          borderRadius: 20,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {leftIconName || backBtn ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={onLeftPress}
            style={{
              // backgroundColor: BaseColor.whiteColor,
              height: 40,
              width: 40,
              borderRadius: 20,
              justifyContent: 'center',
              alignItems: 'center',
              // borderWidth: leftIconName === "left-arrow" ? 0 : 0.5,
              borderColor:
                leftIconName === 'left-arrow' ? null : BaseColor.textGrey,
            }}>
            {backBtn ? (
              <FAIcon
                name="angle-left"
                size={24}
                color={BaseColor.whiteColor}
              />
            ) : leftIconName ? (
              <>
                {leftIconName === 'settings-2' &&
                notificationCount.chat_count > 0 ? (
                  <View
                    style={{
                      position: 'absolute',
                      backgroundColor: 'red',
                      width: 18,
                      height: 18,
                      top: -3,
                      right: -3,
                      borderRadius: 10,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        color: '#fff',
                        fontSize: 12,
                      }}
                      numberOfLines={1}>
                      {notificationCount.chat_count}
                    </Text>
                  </View>
                ) : null}
                <CustomIcon
                  name={leftIconName}
                  size={leftIconName === 'left-arrow' || gallery ? 22 : 18}
                  color={gallery ? BaseColor.blackColor : BaseColor.whiteColor}
                />
              </>
            ) : null}
            {/* {leftIconName } */}
          </TouchableOpacity>
        ) : null}
      </View>
      <View style={{flex: 1, alignItems: 'center'}}>
        {image ? (
          <Image style={{height: 29, width: 131}} source={image} />
        ) : (
          <Text
            style={{
              fontFamily: FontFamily.default,
              color: gallery ? BaseColor.blackColor : BaseColor.whiteColor,
              fontSize: 18,
              fontWeight: 'bold',
              letterSpacing: 2,
              paddingHorizontal: 24,
            }}
            numberOfLines={1}>
            {title}
          </Text>
        )}
      </View>

      <View
        style={{
          height: 40,
          width: 40,
          borderRadius: 20,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {rightIconName ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={onRightPress}
            style={{
              backgroundColor:
                rightIconName === 'check'
                  ? BaseColor.whiteColor
                  : BaseColor.witeColor,
              height: 40,
              width: 40,
              borderRadius: 20,
              justifyContent: 'center',
              alignItems: 'center',
              // borderWidth: 0.5,
              // borderColor: BaseColor.textGrey,
            }}>
            {rightIconName ? (
              <>
                {rightIconName === 'notifications-bell-button' &&
                notificationCount.notification_count > 0 ? (
                  <View
                    style={{
                      position: 'absolute',
                      backgroundColor: 'green',
                      width: 18,
                      height: 18,
                      top: -3,
                      right: -3,
                      borderRadius: 10,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        color: '#fff',
                        fontSize: 12,
                      }}
                      numberOfLines={1}>
                      {notificationCount.notification_count}
                    </Text>
                  </View>
                ) : null}
                <CustomIcon
                  name={rightIconName}
                  size={rightIconName === 'clear' ? 24 : 18}
                  color={
                    rightIconName === 'check'
                      ? BaseColor.blueDark
                      : BaseColor.whiteColor
                  }
                />
              </>
            ) : null}
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

export default CHeader;
