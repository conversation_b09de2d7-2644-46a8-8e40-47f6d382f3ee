/* eslint-disable react/jsx-props-no-spreading */
import React, {useEffect, useRef, useState} from 'react';
import {Text, TextInput, TouchableOpacity, View} from 'react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import LinearGradient from 'react-native-linear-gradient';
import moment from 'moment';
import {useTheme} from '@react-navigation/native';
import styles from './styles';
import {CustomIcon} from '../../config/LoadIcons';
import {FontFamily} from '../../config/typography';
import BaseColors from '../../config/colors';
import {translate} from '../../lang/Translate';
import {isEmpty, isString} from 'lodash';
import Toast from 'react-native-simple-toast';
import CountryPicker, {DEFAULT_THEME} from 'react-native-country-picker-modal';
import DatePicker from 'react-native-date-picker';

const CInput = React.forwardRef((props, ref) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    onSubmitEditing = () => {},
    placeholder = 'Default placeholder',
    onChangeText = () => {},
    onFocus = () => {},
    onBlur = () => {},
    isLastInput,
    returnKeyType,
    textInputWrapper,
    secureTextEntry,
    icon,
    rightIcon,
    rightIconStyle,
    onRightIconPress = () => {},
    editable = true,
    value,
    colorStyle,
    keyboardType,
    placeholderTextColor = BaseColor.placeHolderColor,
    disabled = false,
    error,
    maxLength = 50,
    showCountryPicker = false,
    onCountrySelect,
    countryCode,
    containterStyle,
    inputStyle,
    textInputStyle,
    iconStyle,
    viewStyle,
    txtColor = BaseColor.blackColor,
    countryLabelColor = BaseColor.blackColor,
    multiline = false,
    disableTxtColor = 'rgba(0, 0, 0, 0.4)',
    iconColor,
    iconSize,
    iconName,
    keyboardAppearance,
    hideLeftIcon = false,
    showError,
    errorMsg,
    datePicker,
    selectDate,
    onDateChange = () => {},
    setstate,
    state,
    title,
    titleStyle,
    phoneNumber,
    callingCode = '',
    isSuffix,
    onSelect = () => {},
    suffixStyle,
    minDate,
    maxDate,
    primaryColor = BaseColors.whiteColor,
    secondaryColor = BaseColor.white10,
    mode,
    customContent,
    ...rest
  } = props;

  const [show, setShow] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  const myTheme = {
    ...DEFAULT_THEME,
    primaryColor: '#ccc',
    primaryColorVariant: '#eee',
    onBackgroundTextColor: !isVisible ? 'white' : BaseColors.black,
    backgroundColor: BaseColors.white,
    filterPlaceholderTextColor: '#AAAAAA',
    flagSize: 50,
    fontSize: 14,
    fontFamily: FontFamily.medium,
  };

  const isIOS = Platform.OS === 'ios';

  return (
    <View>
      {!isEmpty(title) && (
        <View style={[styles.titleContainer]}>
          <Text style={[styles.title, titleStyle]}>{title}</Text>
          {/* {isRequired && <Text style={styles.astrick}>*</Text>} */}
        </View>
      )}
      <LinearGradient
        start={{x: 0, y: 0}}
        end={{x: 0, y: 1}}
        colors={[primaryColor, secondaryColor]}
        style={[
          styles.inputWrapper,
          {
            backgroundColor: '#FFFFFF',
            borderColor: showError
              ? BaseColors.error
              : 'rgba(135, 181, 179, 0.2)',
            borderWidth: showError ? 1 : 1.5,
          },
          textInputWrapper,
        ]}>
        {datePicker ? (
          <TouchableOpacity
            activeOpacity={0.5}
            onPress={() => setShow(true)}
            style={[
              styles.input,
              {
                marginStart: hideLeftIcon ? 12 : 0,
                color: BaseColor.blackColor,
                fontFamily: FontFamily.regular,
              },
              inputStyle,
              editable ? styles.colorInput : colorStyle,
            ]}>
            {show ? (
              <DatePicker
                ref={ref}
                modal
                open={show}
                androidVariant="iosClone"
                onConfirm={date => {
                  onDateChange(date);
                  setShow(false);
                }}
                onCancel={() => {
                  setShow(false);
                }}
                minimumDate={minDate}
                maximumDate={maxDate}
                date={value ? new Date(value) : null}
                title={title}
                mode={mode}
                theme="light"
                confirmText="Confirm"
                cancelText="Cancel"
              />
            ) : (
              <TouchableOpacity
                style={{}}
                activeOpacity={0.5}
                onPress={() => {
                  setShow(true);
                }}>
                <Text
                  style={{
                    color: BaseColor.blackColor,
                    fontSize: 16,
                  }}>
                  {value ? moment(value).format('DD-MM-YYYY') : 'Date of birth'}
                </Text>
              </TouchableOpacity>
            )}
          </TouchableOpacity>
        ) : (
          <View
            style={{
              height: 46,
              flexDirection: 'row',
              alignItems: 'center',
              paddingStart: 10,
            }}>
            {isSuffix && (
              <View
                style={{
                  position: 'absolute',
                  // height: 50,
                  zIndex: 1,
                  borderRadius: 5,
                  alignItems: 'center',
                  paddingHorizontal: 5,
                  flexDirection: 'row',
                  ...suffixStyle,
                }}>
                <CountryPicker
                  theme={myTheme}
                  {...{
                    countryCode: countryCode || 'ES',
                    withFilter: true,
                    withFlag: true,
                    renderFlagButton: false,
                    // withCountryNameButton: true,
                    withAlphaFilter: true,
                    withCallingCode: true,
                    withEmoji: true,
                    disabled: true,
                    onSelect: onSelect,
                    theme: {
                      fontSize: 16,
                      onBackgroundTextColor: BaseColor.blackColor,
                      primaryColor: BaseColor.alertRed,
                      backgroundColor: BaseColor.whiteColor,
                      filterPlaceholderTextColor: BaseColor.blackColor,
                    },
                  }}
                  visible={countryCode}
                />
                {/* <Icon name="angle-down" color="#6D7177" size={12} /> */}
                <Text
                  style={{
                    fontSize: 20,
                    fontWeight: '200',
                    color: '#6D7177',
                  }}>
                  |{' '}
                </Text>
              </View>
            )}
            {hideLeftIcon ? (
              <CustomIcon
                name={iconName || 'rocket'}
                size={iconSize || 18}
                color={iconColor || BaseColor.blackColor}
              />
            ) : null}
            <TextInput
              {...rest}
              ref={ref}
              selectionColor={BaseColor.black50}
              placeholder={placeholder}
              placeholderTextColor={placeholderTextColor}
              style={[
                styles.input,
                {
                  marginStart: hideLeftIcon ? 12 : 0,
                  color: BaseColor.blackColor,
                  fontFamily: FontFamily.regular,
                  // color: showError ? BaseColor.orange : BaseColor.whiteColor,
                },
                inputStyle,
                editable ? styles.colorInput : colorStyle,
              ]}
              onChangeText={onChangeText}
              blurOnSubmit={false}
              onSubmitEditing={onSubmitEditing}
              returnKeyType={returnKeyType || (isLastInput ? 'go' : 'next')}
              secureTextEntry={secureTextEntry}
              editable={editable}
              value={value}
              maxLength={maxLength}
              keyboardType={keyboardType}
              multiline={multiline}
              onFocus={onFocus}
              onBlur={onBlur}
            />
            {rightIcon
              ? customContent || (
                  <TouchableOpacity
                    style={{marginHorizontal: 10}}
                    onPress={onRightIconPress}
                    activeOpacity={0.8}>
                    <CustomIcon
                      name={iconName || 'rocket'}
                      size={iconSize || 18}
                      color={iconColor || BaseColor.blackColor}
                    />
                  </TouchableOpacity>
                )
              : null}
          </View>
        )}
      </LinearGradient>
      {showError ? (
        <View style={{flexDirection: 'row'}}>
          <View style={{paddingRight: 5}}>
            <CustomIcon name="info" size={14} color={BaseColor.error} />
          </View>
          <Text
            style={{
              color: BaseColors.error,
              fontFamily: FontFamily.regular,
              marginBottom: 5,
              fontSize: 14,
              marginTop: isIOS ? 2 : 0,
            }}>
            {errorMsg}
          </Text>
        </View>
      ) : null}
    </View>
  );
});

export default CInput;
