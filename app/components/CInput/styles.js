import {StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const styles = StyleSheet.create({
  countryPickerView: {
    // backgroundColor: 'red'
  },
  inputWrapper: {
    // padding: ,
    borderRadius: 8,
    marginVertical: 10,
    width: '100%',
  },
  input: {
    flex: 1,
    marginEnd: 24,
    color: BaseColor.whiteColor,
    height: 50,
  },
  title: {
    color: BaseColor.blackColor,
    fontFamily: FontFamily.regular,
    fontSize: 14,
  },
  titleContainer: {
    alignSelf: 'flex-start',
  },
});

export default styles;
