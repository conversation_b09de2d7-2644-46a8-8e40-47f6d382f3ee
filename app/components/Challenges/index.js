import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Platform,
  Text,
  ActivityIndicator,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import Toast from 'react-native-simple-toast';
import AIcon from 'react-native-vector-icons/AntDesign';
import {translate} from '../../lang/Translate';
import {FontFamily} from '../../config/typography';
import BaseColor from '../../config/colors';
import {CustomIcon} from '../../config/LoadIcons';
import LinearGradient from 'react-native-linear-gradient';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import NewAlert from '../NewAlertModal';
import {downloadFile} from '../../utils/commonFunction';

const IOS = Platform.OS === 'ios';
const {width, height} = Dimensions.get('window');

const Challenges = props => {
  const [loader, setLoader] = useState(false);
  const [challengesList, setChallengesList] = useState([]);
  const [visible, setVisible] = useState({visible: false, selectId: ''});
  const [btnLoader, setBtnLoader] = useState(false);

  /** this function for get chalenges list
   * @function getChallengesList
   */
  async function getChallengesList() {
    try {
      const response = await getApiData(
        BaseSetting.endpoints.challengesList,
        'GET',
      );
      if (response.success) {
        setChallengesList(response?.data);
      } else {
        setChallengesList([]);
        Toast.show(response.message);
      }
    } catch (error) {
      setChallengesList([]);
      console.log('error for device list ===', error);
    }
  }

  /** this function for logout
   * @function deleteChallenges
   * @param {object} data id
   */
  async function deleteChallenges(id) {
    setBtnLoader(true);
    const data = {
      challenge_id: id,
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.deleteChallenges,
        'POST',
        data,
        '',
        true,
      );
      if (response?.success) {
        setVisible({visible: false, selectId: ''});
        getChallengesList();
      }
      setBtnLoader(false);
    } catch (err) {
      console.log('ERRR==', err);
    }
  }

  useEffect(() => {
    getChallengesList();
  }, []);

  const rederList = ({item, index}) => {
    const url = item?.image_url;
    const urlType = url && url.substring(url?.lastIndexOf('.') + 1);
    return (
      <View style={styles.challengeContainer}>
        <LinearGradient
          colors={['#478F8C', '#87B5B3']}
          start={{x: 0, y: 0.5}}
          end={{x: 0, y: 0.03}}
          style={styles.gradientCircle}>
          <CustomIcon name="challange-location" color={'#fff'} size={25} />
        </LinearGradient>
        <View style={{marginLeft: 20}}>
          <Text style={styles.daysTextStyle} numberOfLines={2}>
            {item?.activities}
          </Text>
          <Text style={styles.contentText}>
            {`${item?.challenge_metric} ${item?.metric_type} | ${item?.highlight_type} `}
          </Text>
          <View style={styles.progressContainer}>
            <View style={[styles.unfilled, {width: width}]}>
              <LinearGradient
                start={{x: 0, y: 1}}
                end={{x: 1, y: 0}}
                colors={['#87B5B3', '#C5EBE9']} // Your desired gradient colors
                style={[
                  styles.gradient,
                  {width: 2 * Number(item?.user_challenge_percentage)},
                ]} // Set width according to progress
              />
            </View>
          </View>
          {item?.user_challenge_status === 'completed' ? (
            <View
              style={{
                marginVertical: 5,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <View style={styles.awardTextContaner}>
                <Text
                  style={{
                    fontFamily: FontFamily.regular,
                    fontSize: 12,
                    lineHeight: 14,
                    color: '#444444',
                  }}
                  numberOfLines={2}>
                  {item?.award_text}{' '}
                </Text>
              </View>
              <Text
                onPress={() =>
                  downloadFile(
                    item?.image_url,
                    `${item?.activities}.${urlType}`,
                  )
                }
                style={{
                  fontFamily: FontFamily.default,
                  textDecorationLine: 'underline',
                  color: BaseColor.primary,
                }}>
                Download Link
              </Text>
            </View>
          ) : null}
        </View>
        {item?.user_challenge_status === 'completed' ? (
          <TouchableOpacity
            style={{position: 'absolute', right: 5, top: 5}}
            onPress={() => setVisible({visible: true, selectId: item?.id})}
            activeOpacity={0.8}>
            <AIcon name={'closecircleo'} size={17} color={BaseColor.primary} />
          </TouchableOpacity>
        ) : null}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginBottom: 10,
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <Text style={styles.titleColor}>{translate('Challenges')}</Text>
          <CustomIcon name="staraward" size={20} color={'#1F2222'} />
        </View>
      </View>
      {loader ? (
        <ActivityIndicator />
      ) : (
        <FlatList
          data={challengesList}
          renderItem={rederList}
          horizontal
          showsHorizontalScrollIndicator={false}
        />
      )}
      <NewAlert
        visible={visible?.visible}
        onRequestClose={() => setVisible({visible: false, selectId: ''})}
        onCancelPress={() => setVisible({visible: false, selectId: ''})}
        loader={btnLoader}
        onOkPress={() => {
          deleteChallenges(visible?.selectId);
        }}
        alertTitle={translate('Delete Challenges')}
        alertMessage={translate('Are you sure you want to delete it?')}
        agreeTxt={translate('agree')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
  },
  challengeContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginRight: 20,
    shadowColor: '#000',
    borderWidth: 1,
    borderRadius: 8,
    backgroundColor: '#ffffff',
    borderColor: '#fff',
    marginBottom: 1,
    elevation: 1,
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.1,
    shadowRadius: 1.84,
  },
  titleColor: {
    fontSize: 16,
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    paddingRight: 10,
    letterSpacing: 1,
  },
  gradientCircle: {
    width: 50,
    height: 50,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    background: 'linear-gradient(130.4deg, #87B5B3 9.08%, #478F8C 56.93%)',
  },
  progressContainer: {
    position: 'relative',
    width: 200,
    height: 7,
    borderRadius: 10,
    overflow: 'hidden', // Ensures the gradient is clipped to the rounded corners
  },
  unfilled: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    backgroundColor: '#F7F8F8', // Color for unfilled part
    borderRadius: 10,
  },
  gradient: {width: 200, height: 7, borderRadius: 10, justifyContent: 'center'},
  daysTextStyle: {
    fontSize: 14,
    fontFamily: FontFamily.bold,
    color: '#1c1c1c',
    maxWidth: width / 2,
    flexWrap: 'wrap',
  },
  contentText: {
    fontSize: 12,
    fontFamily: FontFamily.robotoLight,
    color: '#444444',
    paddingVertical: 5,
  },
  awardTextContaner: {
    maxWidth: width / 3.2,
    paddingRight: 5,
  },
});

export default Challenges;
