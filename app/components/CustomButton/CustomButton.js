/* eslint-disable no-nested-ternary */
import React, { useRef, useEffect } from "react";
import {
  ActivityIndicator,
  Image,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
// import Animated, {
//   useSharedValue,
//   useAnimatedStyle,
//   withTiming,
// } from "react-native-reanimated";
import FAIcon from "react-native-vector-icons/FontAwesome";
import { useTheme } from "@react-navigation/native";
import styles from "../CButton/styles";
import { CustomIcon } from "../../config/LoadIcons";

const CustomButton = (props) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    loader,
    title = "Title",
    onPress = () => {},
    style,
    paddingVertical,
    titleStyle,
    smallBtn,
    done,
    anim = false,
    playAnimation,
    iconname,
    leftIcon,
    leftIconStyle,
    iconsize = 24,
    leftIconImage,

    iconColor = BaseColor.blueLight,
    iconBg,
    backAnim = false,
  } = props;

  const koibhi = () => (
    <>
      <TouchableOpacity
        activeOpacity={0.7}
        style={{
          height: "100%",
          width: "100%",
          justifyContent: "center",
          alignItems: "center",
          flexDirection: "row",
        }}
        onPress={() => {
          onPress();
        }}
      >
        {leftIcon && (
          <FAIcon
            style={{ marginRight: 15, ...leftIconStyle }}
            name={leftIcon}
            size={iconsize}
            color={iconColor}
          />
        )}

        {leftIconImage && (
          <Image source={leftIconImage} style={{ marginRight: 15 }} />
        )}
        <Text style={[styles.txtStyle, titleStyle]}>{title}</Text>
      </TouchableOpacity>
    </>
  );

  return (
    <>
      {anim ? (
        <View
          style={[
            styles.btnStyle,
            { backgroundColor: BaseColor.whiteColor },
            // animationStyle,
            style,
            { paddingVertical: paddingVertical ? paddingVertical : 8 },
          ]}
        >
          {koibhi()}
        </View>
      ) : (
        <View
          style={[
            styles.btnStyle,
            {
              borderRadius: loader ? 18 : 40,
            },
            style,
          ]}
        >
          {koibhi()}
        </View>
      )}
    </>
  );
};

export default CustomButton;
