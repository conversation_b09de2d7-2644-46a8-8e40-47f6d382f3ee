/* eslint-disable no-nested-ternary */
import React from 'react';
import {Image, Text, TouchableOpacity, View} from 'react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import {useTheme} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import {CustomIcon} from '../../config/LoadIcons';
import {FontFamily, FontWeight} from '../../config/typography';

const CustomHeader = props => {
  const {
    image,
    title,
    leftIconName,
    rightIconName,
    onLeftPress,
    onRightPress,
    backgroundColor,
    backBtn,
    borderCircular,
    transparentView,
    rightIconSize,
    rightColour,
    secondRighIconName,
    secondRightIconColor,
    onSecondRightPress,
    headerContainer,
    leftIconColor,
  } = props;

  const colors = useTheme();
  const BaseColor = colors.colors;

  const {notificationCount} = useSelector(state => state.auth);
  return (
    <View
      style={{
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
        alignContent: 'center',
        paddingTop: '12%',
        paddingHorizontal: borderCircular ? 18 : 10,
        marginBottom: 10,
        backgroundColor: transparentView ? '' : BaseColor.whiteColor,
        borderBottomWidth: transparentView ? 0 : 1,
        borderColor: 'rgba(0, 0, 0, 0.10)',
        ...headerContainer,
      }}>
      <View
        style={{
          height: 40,
          width: 40,
          borderRadius: 20,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {leftIconName || backBtn ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={onLeftPress}
            style={{
              backgroundColor: borderCircular
                ? backgroundColor
                  ? backgroundColor
                  : '#F1F5F9'
                : '',
              width: 36,
              height: 36,
              borderRadius: 50,
              justifyContent: 'center',
              alignItems: 'center',
              borderColor:
                leftIconName === 'left-arrow' ? null : BaseColor.textGrey,
              shadowColor: BaseColor.primary,
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: borderCircular ? 15 : 0,
            }}>
            {backBtn ? (
              <CustomIcon
                name={leftIconName}
                size={leftIconName === 'left-arrow' ? 15 : 18}
                color={leftIconColor || BaseColor.primary}
              />
            ) : leftIconName ? (
              <>
                {leftIconName === 'settings-2' &&
                notificationCount.chat_count > 0 ? (
                  <View
                    style={{
                      position: 'absolute',
                      backgroundColor: 'red',
                      width: 18,
                      height: 18,
                      top: -3,
                      right: -3,
                      borderRadius: 10,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        color: '#fff',
                        fontSize: 12,
                      }}
                      numberOfLines={1}>
                      {notificationCount.chat_count}
                    </Text>
                  </View>
                ) : null}
                <CustomIcon
                  name={leftIconName}
                  size={leftIconName === 'left-arrow' ? 22 : 18}
                  color={BaseColor.whiteColor}
                />
              </>
            ) : null}
            {/* {leftIconName } */}
          </TouchableOpacity>
        ) : null}
      </View>
      <View style={{alignItems: 'center'}}>
        {image ? (
          <Image style={{height: 29, width: 131}} source={image} />
        ) : (
          <Text
            style={{
              fontFamily: FontFamily.bold,
              color: '#000000',
              fontSize: 20,
              letterSpacing: 0.7,
            }}
            numberOfLines={1}>
            {title}
          </Text>
        )}
      </View>

      <View
        style={{
          height: 40,
          borderRadius: 20,
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
        {secondRighIconName && (
          <TouchableOpacity
            onPress={onSecondRightPress}
            style={{
              backgroundColor: borderCircular ? BaseColor.whiteColor : '',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: borderCircular ? 15 : 0,
              width: 36,
              height: 36,
              borderRadius: 50,
              marginRight: 10,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <CustomIcon
              name={secondRighIconName}
              size={rightIconSize ? 15 : 18}
              color={
                secondRightIconColor ? secondRightIconColor : BaseColor.primary
              }
            />
          </TouchableOpacity>
        )}
        {rightIconName ? (
          <TouchableOpacity
            // activeOpacity={0.7}
            onPress={onRightPress}
            style={{
              backgroundColor: borderCircular ? BaseColor.whiteColor : '',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: borderCircular ? 15 : 0,
              width: 36,
              height: 36,
              borderRadius: 50,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            {rightIconName ? (
              <>
                {rightIconName === 'notifications-bell-button' &&
                notificationCount.notification_count > 0 ? (
                  <View
                    style={{
                      position: 'absolute',
                      backgroundColor: 'green',
                      width: 18,
                      height: 18,
                      top: -3,
                      right: -3,
                      borderRadius: 10,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        color: '#fff',
                        fontSize: 12,
                      }}
                      numberOfLines={1}>
                      {notificationCount.notification_count}
                    </Text>
                  </View>
                ) : null}
                <CustomIcon
                  name={rightIconName}
                  size={rightIconSize ? 15 : 18}
                  color={rightColour ? rightColour : BaseColor.primary}
                />
              </>
            ) : null}
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

export default CustomHeader;
