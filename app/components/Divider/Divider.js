import React from "react";
import { StyleSheet, View } from "react-native";
import BaseColor from "../../config/colors";

function Divider({ width, backgroundColor, divStyle }) {
  return (
    <View
      style={[
        styles.divider,
        { backgroundColor: backgroundColor || BaseColor.black40, width: width },
        divStyle,
      ]}
    />
  );
}

const styles = StyleSheet.create({
  divider: {
    height: 1,
    backgroundColor: BaseColor.black40,
    // marginVertical: 12,
  },
});

export default Divider;
