/* eslint-disable react-native/no-inline-styles */
import React, {useState} from 'react';
import PropTypes from 'prop-types';
import {View, Text, TouchableOpacity, Platform} from 'react-native';
import {Dropdown, MultiSelect} from 'react-native-element-dropdown';
import {createStyles} from './styles';
import {CustomIcon} from '../../config/LoadIcons';
import {useTheme} from '@react-navigation/native';
import {isArray} from 'lodash';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

/**
 *
 * Component for dropdown selction list
 * @module DropDownList
 * @param Props to handle component props
 */

export default function DropDownList(props) {
  const {
    containerSty,
    data,
    value,
    onChange,
    onClick,
    valueProp,
    labelProp,
    titleText,
    placeholderTxt,
    titleSty,
    mandatory = false,
    dropDownSty,
    showError,
    multiSelection,
    errorText,
    search,
    hidetitle,
    disable = false,
    inputSearchStyle,
  } = props;
  const IOS = Platform.OS === 'ios';
  const [isFocus, setIsFocus] = useState(false);
  const colors = useTheme();
  const styles = createStyles(colors);

  const renderLabel = () => {
    return (
      !multiSelection && (
        <Text
          style={[
            styles.titleTxt,
            titleSty,
            {
              color: BaseColor.card1,
              fontSize: 12,
            },
          ]}>
          {titleText}
        </Text>
      )
    );
  };

  const renderIcon = () => (
    <View style={{marginRight: 10}}>
      <CustomIcon
        name="expand-button"
        size={16}
        style={{
          color: BaseColor.textGrey,
        }}
      />
    </View>
  );

  const renderMultiIcon = () => {
    return (
      <View>
        <CustomIcon
          name="expand-button"
          size={16}
          style={{
            color: BaseColor.textGrey,
          }}
        />
      </View>
    );
  };
  const renderItem = (item, selected) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        style={{backgroundColor: BaseColor.whiteColor}}
        disabled={
          item.value === 'label-My Ports' || item?.value === 'label-All Ports'
            ? false
            : true
        }>
        <View style={styles.listItem}>
          <Text
            style={[
              styles.listItemTxt,
              {
                color: disable
                  ? BaseColor.inactive
                  : selected
                  ? colors.colors.primary
                  : colors.colors.textColor,
                fontSize: 16,
              },
            ]}>
            {item[labelProp]}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderMultiItem = item => {
    return (
      <View style={styles.listItem}>
        <Text
          style={[
            styles.listItemTxt,
            {
              color: disable
                ? BaseColor.inactive
                : colors.colors.dropdownTextColor,
              fontSize: 16,
            },
          ]}>
          {item[labelProp]}
        </Text>
      </View>
    );
  };

  return (
    <>
      {hidetitle ? null : renderLabel()}
      <View
        style={{
          backgroundColor: '#0000',
          marginBottom: showError ? 5 : 0,
          borderColor: 'rgba(135, 181, 179, 0.2)',
          borderRadius: 8,
          borderWidth: multiSelection ? 0 : 1,
          ...containerSty,
        }}>
        {multiSelection ? (
          <MultiSelect
            statusBarIsTranslucent={true}
            selectedItemIconColor="#ffffff"
            style={[
              styles.dropdown,
              dropDownSty,
              {
                backgroundColor: 'transparent',
                borderBottomColor: disable
                  ? BaseColor.inactive
                  : colors.colors.inputBorder,
                borderBottomWidth: 0.5,
                paddingTop: 10,
              },
            ]}
            placeholderStyle={[
              {
                color: disable
                  ? colors.colors.inactive
                  : colors.colors.textColor,
                fontSize: 14,
                fontWeight: '500',
              },
            ]}
            selectedTextStyle={[styles.showTextSty]}
            renderRightIcon={renderMultiIcon}
            data={data}
            value={isArray(value) ? value : []}
            itemTextStyle={{color: 'black'}}
            activeColor={colors.colors.whiteSmoke}
            renderItem={(item, sel) => renderMultiItem(item)}
            search={search}
            inputSearchStyle={inputSearchStyle}
            maxHeight={220}
            labelField={labelProp}
            valueField={valueProp}
            placeholder={
              mandatory ? `${placeholderTxt} * ` : `${placeholderTxt}`
            }
            onChange={onChange}
            onChangeValue={e => {}}
            onFocus={() => setIsFocus(true)}
            onBlur={() => setIsFocus(false)}
            disable={disable}
            selectedStyle={[
              styles.multipleSelectItem,
              {
                backgroundColor: disable
                  ? BaseColor.inactiveIndex
                  : BaseColor.white,
              },
            ]}
          />
        ) : (
          <Dropdown
            statusBarIsTranslucent={true}
            autoScroll={false}
            dropdownPosition="bottom"
            style={[styles.dropdown, dropDownSty]}
            placeholderStyle={[
              styles.showTextSty,
              {
                color: disable ? BaseColor.inactive : '#c2c2c2',
                fontSize: 14,
              },
            ]}
            selectedTextStyle={[
              styles.showTextSty,
              {
                color: disable ? BaseColor.inactive : colors.colors.textColor,
                fontSize: 14,
              },
            ]}
            fontFamily={FontFamily.regular}
            renderRightIcon={renderIcon}
            data={data}
            disable={disable}
            renderItem={(item, sel) => renderItem(item, sel, labelProp)}
            search={search}
            maxHeight={200}
            labelField={labelProp}
            valueField={valueProp}
            placeholder={placeholderTxt}
            value={value}
            onFocus={() => {
              onClick && onClick();
              setIsFocus(true);
            }}
            onBlur={() => setIsFocus(false)}
            onChange={item => {
              onChange(item[valueProp]);
              setIsFocus(false);
            }}
            activeColor={BaseColor.white}
            inputSearchStyle={inputSearchStyle}
            containerStyle={{backgroundColor: colors.colors.white}}
          />
        )}
      </View>
      {showError && errorText && (
        <View style={{flexDirection: 'row'}}>
          <View style={{paddingRight: 5}}>
            <CustomIcon name="info" size={14} color={BaseColor.error} />
          </View>
          <Text
            style={{
              color: BaseColor.error,
              fontFamily: FontFamily.regular,
              marginBottom: 5,
              fontSize: 14,
              marginTop: IOS ? 2 : 0,
            }}>
            {errorText}
          </Text>
        </View>
      )}
    </>
  );
}

DropDownList.propTypes = {
  data: PropTypes.array,
  onChange: PropTypes.func,
  labelProp: PropTypes.string,
  valueProp: PropTypes.string,
  value: PropTypes.string,
  titleText: PropTypes.string,
  placeholderTxt: PropTypes.string,
  rIcon: PropTypes.string,
  dropDownSty: PropTypes.object,
  errorText: PropTypes.string,
  showError: PropTypes.bool,
  multiSelection: PropTypes.bool,
  search: PropTypes.bool,
};

DropDownList.defaultProps = {
  data: [
    {lable: 'name1', value: 12, id: 1},
    {lable: 'name2', value: 34, id: 2},
    {lable: 'name3', value: 56, id: 3},
    {lable: 'name4', value: 78, id: 4},
    {lable: 'name5', value: 90, id: 5},
  ],
  onChange: () => {},
  labelProp: 'lable',
  valueProp: 'value',
  value: '',
  titleText: 'Name',
  placeholderTxt: 'Select Name',
  rIcon: 'dropdown',
  dropDownSty: {},
  errorText: '',
  showError: false,
  search: false,
  multiSelection: false,
};
