import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Platform,
  Text,
  FlatList,
  Image,
  Alert,
} from 'react-native';
import {FontFamily} from '../../config/typography';
import BaseColor from '../../config/colors';
import {translate} from '../../lang/Translate';
import CButton from '../CButton';
import {Images} from '../../config/Images';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {sendErrorReport} from '../../utils/commonFunction';
import {useIsFocused} from '@react-navigation/native';
import {isEmpty, flattenDeep} from 'lodash';

const IOS = Platform.OS === 'ios';
const {width, height} = Dimensions.get('window');
const FeatureFavourite = ({}) => {
  const isFocused = useIsFocused();
  const [advertiesArray, setAdvertiesArray] = useState([]);

  // Magazine List API Integration
  const MagazineList = async () => {
    try {
      const response = await getApiData(
        BaseSetting.endpoints.getFeedPost,
        'POST',
      );
      if (response?.success) {
        if (response?.data) {
          setAdvertiesArray(response?.data);
        }
      } else {
        setAdvertiesArray([]);
      }
    } catch (error) {
      console.log('error for device list ===', error);
      sendErrorReport(error, 'saving_trip_name');
    }
  };

  useEffect(() => {
    MagazineList();
  }, [isFocused]);

  //render item for Advertisement list..
  const renderItem = ({item}) => {
    return (
      <View style={styles.itemContainer}>
        <View style={styles.imageContainer}>
          <Image
            source={
              item?.post_file ? {uri: item?.post_file} : Images.magazineImg
            }
            style={styles.imageStyle}
          />
        </View>
        <Text style={styles.subtitle} numberOfLines={1}>
          {item?.post_subtitle}
        </Text>
        <Text style={styles.title} numberOfLines={1}>
          {item?.post_title}
        </Text>
        <CButton
          title={translate('ViewTestReport')}
          style={styles.buttonStyle}
          titleStyle={{fontSize: 7}}
          onPress={() => {
            Alert.alert('New Feature Coming Soon');
          }}
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {!isEmpty(advertiesArray) && (
        <Text style={styles.titleColor}>{translate('QeridooFeatureFav')}</Text>
      )}
      <FlatList
        data={advertiesArray}
        numColumns={3}
        renderItem={renderItem}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 20,
  },
  titleColor: {
    fontSize: 16,
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    marginBottom: 20,
  },
  subtitle: {
    fontSize: 9,
    fontFamily: FontFamily.regular,
    color: BaseColor.primary,
  },
  title: {
    fontSize: 10,
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
    paddingVertical: 10,
  },
  itemContainer: {
    width: width / 3.4,
    marginRight: 10,
    backgroundColor: '#ffff',
    marginRight: 5,
    alignItems: 'center',
    paddingBottom: 10,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 1.84,
    paddingHorizontal: 10,
    marginBottom: 10,
  },
  buttonStyle: {
    width: '95%',
    marginTop: 20,
    height: 20,
  },
  imageStyle: {
    width: '100%',
    height: '100%',
  },
  imageContainer: {
    width: 100,
    height: 60,
    marginBottom: 10,
  },
});

export default FeatureFavourite;
