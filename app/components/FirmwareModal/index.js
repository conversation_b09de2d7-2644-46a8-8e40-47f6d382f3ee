/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
/* eslint-disable quotes */
import {isArray, isEmpty, isObject, isString} from 'lodash';
import React from 'react';
import {
  Image,
  Modal,
  Text,
  TouchableOpacity,
  View,
  Linking,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import RNFetchBlob from 'react-native-blob-util';
import Toast from 'react-native-simple-toast';
import {check, PERMISSIONS} from 'react-native-permissions';
import BaseColor from '../../config/colors';

export default function FirmwareModal(props) {
  const {
    visible,
    button,

    position,
    onClose = () => {},
    detail,
  } = props;

  console.log('nnnnnnnnnn', detail);

  function handleUrl(item) {
    const url = item?.button_url ? item?.button_url : '';
    if (url !== '') {
      Linking.canOpenURL(url).then(supported => {
        if (!supported) {
          console.error('No handler for URL:', url);
        } else {
          Linking.openURL(url);
        }
      });
    }
    onClose();
  }
  const checkPermission = () => {
    if (Platform.OS === 'android' && Platform.Version >= 23) {
      PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      ).then(result => {
        if (result) {
          console.log('Permission is OK');
        } else {
          PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          ).then(res => {
            if (res) {
              console.log('User accept');
            } else {
              console.log('User refuse');
            }
          });
        }
      });
    } else {
      check(PERMISSIONS.IOS.WRITE_EXTERNAL_STORAGE).then(res => {
        if (res !== 'granted') {
          console.log('permission granted === ');
        }
      });
    }
  };
  return (
    <Modal
      visible={visible}
      style={{flex: 1, padding: 16}}
      transparent
      animationType="fade">
      <TouchableOpacity
        activeOpacity={1}
        style={{
          flex: 1,
          backgroundColor: '#00000050',
          justifyContent:
            position === 'bottom'
              ? 'flex-end'
              : position === 'top'
              ? 'flex-start'
              : 'center',
          alignItems: 'center',
          padding: 16,
          paddingTop: position === 'top' ? 55 : 44,
        }}>
        <View
          style={{
            backgroundColor: '#fafafa',
            // padding: 12,
            borderRadius: 22,
            width: '100%',
            height: position === 'full' ? '100%' : '20%',
            justifyContent: 'space-around',
          }}>
          <TouchableOpacity
            style={{
              height: 35,
              width: 35,
              borderRadius: 18,
              backgroundColor: BaseColor.blueDark,
              position: 'absolute',
              right: -10,
              top: -15,
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 100,
            }}
            onPress={onClose}>
            <Icon
              name="md-close"
              style={{color: BaseColor.whiteColor, fontSize: 20}}
            />
          </TouchableOpacity>

          <Text
            style={{
              marginLeft: 10,
              fontSize: 27,
              fontWeight: '500',
              color: BaseColor.orange,
              alignItems: 'center',
              alignSelf: 'center',
            }}>
            {detail?.title || ''}
          </Text>
          <TouchableOpacity
            style={{
              padding: 10,
              borderRadius: 10,
              borderWidth: 1,
              marginHorizontal: 40,
              alignItems: 'center',
            }}
            onPress={async () => {
              console.log('on file press', detail.file);
              console.log('path====================', `${detail.file}`);

              // RNFetchBlob.fs
              //   .readFile(
              //     `https://chillbaby-space.ams3.digitaloceanspaces.com/brand_devices/cb80b749-8b44-46c2-b8c6-0d99876667c7.bin`,
              //     "octet-stream"
              //   )
              //   .then((data) => console.log("data--------------", data.length));

              // RNFetchBlob.config({
              //   // add this option that makes response data to be stored as a file,
              //   // this is much more performant.
              //   fileCache: true,
              // })
              //   .fetch(
              //     "GET",
              //     "https://chillbaby-space.ams3.digitaloceanspaces.com/brand_devices/cb80b749-8b44-46c2-b8c6-0d99876667c7.bin",
              //     {}
              //   )
              //   .then((res) => {
              //     // the temp file path
              //     console.log("The file saved to ", res.path());
              //   });
              checkPermission();
              // console.log("dddddddddddddd file", item?.device_file);
              // if (item?.device_file) {
              // openInAppBrowser(item?.device_file);
              try {
                const {fs} = RNFetchBlob;
                const {DownloadDir, DocumentDir} = fs.dirs;
                const saveFilePath =
                  Platform.OS === 'ios' ? DocumentDir : DownloadDir;
                const configfb = {
                  fileCache: true,
                  useDownloadManager: true,
                  notification: true,
                  mediaScannable: true,
                  title: 'updateOTA-update.bin',
                  path: `${saveFilePath}/${'updateOTA'}-update.bin`,
                };
                RNFetchBlob.config({
                  fileCache: true,
                  addAndroidDownloads: {
                    useDownloadManager: true,
                    notification: true,
                    path: `${saveFilePath}/${'updateOTA'}-update.bin`,
                    description: 'Downloading.',
                  },
                  path: `${saveFilePath}/${'updateOTA'}-update.bin`,
                })
                  .fetch(
                    'GET',
                    'https://chillbaby-space.ams3.digitaloceanspaces.com/brand_devices/cb80b749-8b44-46c2-b8c6-0d99876667c7.bin',
                    {},
                  )
                  .then(res => {
                    // the temp file path
                    if (Platform.OS === 'ios') {
                      RNFetchBlob.fs.writeFile(
                        configfb.path,
                        res.data,
                        'base64',
                      );
                      RNFetchBlob.ios.previewDocument(configfb.path);

                      RNFetchBlob.ios.openDocument(res.path());
                      Toast.show('Downloded to files');
                      console.log(
                        '🚀 ~ file: index.js ~ ~ .then ~ saveFilePath',
                        res.path(),
                      );
                    } else {
                      Toast.show('Downloded to files');
                      console.log(
                        '🚀 ~ file: index.js ~ line 138 ~ .then ~ saveFilePath',
                        saveFilePath,
                      );
                    }
                  });
              } catch (error) {
                console.log(
                  '🚀 ~ file: index.js ~ line 145 ~ MyQRcode ~ error',
                  error,
                );
              }
              // }
            }}>
            <Text>Download file</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );
}
