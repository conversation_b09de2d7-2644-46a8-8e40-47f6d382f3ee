import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Platform,
  Text,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {translate} from '../../lang/Translate';
import {FontFamily} from '../../config/typography';
import BaseColor from '../../config/colors';
import {CustomIcon} from '../../config/LoadIcons';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import {useIsFocused} from '@react-navigation/native';

const IOS = Platform.OS === 'ios';
const {width, height} = Dimensions.get('window');

const Distance = [
  {
    id: 1,
    name: 'Distance',
    val: '29.87 mi',
    diff: '0.11',
    type: 'up',
    icon: 'Distance',
  },
  {
    id: 1,
    name: 'Time',
    val: '5h 27m',
    diff: '54m',
    type: 'down',
    icon: 'Stopwatch',
  },
  {
    id: 1,
    name: 'Activities',
    val: '8',
    diff: '0',
    type: 'down',
    icon: 'bicycle',
  },
];
const HighLights = props => {
  const {metrix, detanceVal = () => {}} = props;
  const isFocused = useIsFocused();
  const [loader, setLoader] = useState(false);
  const [tripHistory, setTripHistory] = useState([Distance]);
  const [distance, setDistance] = useState(0);

  function convertMinutesToHoursMinutes(totalSeconds) {
    const hours = Math.floor(totalSeconds / 3600); // Get whole hours
    const remainingSecondsAfterHours = totalSeconds % 3600;
    const minutes = Math.floor(remainingSecondsAfterHours / 60); // Get remaining minutes
    return {hours, minutes};
  }

  const getHeightLight = async () => {
    try {
      const response = await getApiData(
        BaseSetting.endpoints.hightLightDetails,
        'GET',
      );
      if (response.success) {
        // Current Week Data....
        const currentVal = response?.data?.currentWeek;
        // Diff Week Data.
        const diffData = response?.data?.differences;
        const distanceDiff = diffData?.totalDistance; // Distannce Diff.
        const timeDiff = diffData?.totalDuration; // Time Diff.
        const activityDiff = diffData?.tripCount; // Activity Diff.
        const time = convertMinutesToHoursMinutes(timeDiff?.difference);
        const diffDuration = convertMinutesToHoursMinutes(
          currentVal?.totalDuration,
        );
        setDistance(Number(currentVal?.totalDistance));
        detanceVal(Number(currentVal?.totalDistance));
        const diffArray = [
          {
            id: 1,
            name: 'Distance',
            val: currentVal?.totalDistance,
            diff: distanceDiff?.difference,
            type: distanceDiff?.status,
            icon: 'Distance',
            color: '#979797',
          },
          {
            id: 2,
            name: 'Time',
            val:
              diffDuration?.hours === 0
                ? `${diffDuration?.minutes}m`
                : `${diffDuration?.hours}h ${diffDuration?.minutes}m`,
            diff:
              time?.hours === 0
                ? `${time?.minutes}m`
                : `${time?.hours}h ${time?.minutes}m`,
            type: timeDiff?.status,
            icon: 'Stopwatch',
            color: '#D17979',
          },
          {
            id: 3,
            name: 'Activities',
            val: currentVal?.tripCount,
            diff: activityDiff?.difference,
            type: activityDiff?.status,
            icon: 'bicycle',
            color: '#87B5B3',
          },
        ];
        setTripHistory(diffArray);
      } else {
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      console.log('error in trip end ===', error);
      // sendErrorReport(error, 'saving_trip_end');
    }
  };

  useEffect(() => {
    getHeightLight();
  }, [isFocused]);

  return (
    <View style={styles.container}>
      {metrix ? null : (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 10,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <Text style={styles.titleColor}>{translate('Highlights')}</Text>
            <CustomIcon name="staraward" size={20} color={'#1F2222'} />
          </View>
          <Text
            onPress={() => {
              Alert.alert('New Feature Coming Soon');
            }}
            style={{
              color: '#174F4D',
              textDecorationLine: 'underline',
              fontFamily: FontFamily.robotoLight,
            }}>
            View More
          </Text>
        </View>
      )}
      {metrix ? null : (
        <Text style={{marginBottom: 10, fontFamily: FontFamily.regular}}>
          {distance > 2 ? `Amazing! ${distance} KM of trails this week!` : null}
        </Text>
      )}
      {loader ? (
        <ActivityIndicator />
      ) : (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginHorizontal: 20,
            marginVertical: 20,
          }}>
          {tripHistory &&
            tripHistory.map(li => {
              return (
                <View style={{alignItems: 'center'}}>
                  <View
                    style={{
                      width: 42,
                      height: 42,
                      borderRadius: 40,
                      backgroundColor: '#F7F8F8',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <CustomIcon name={li.icon} color={li.color} size={21} />
                  </View>
                  <Text
                    style={{
                      fontSize: 14,
                      fontFamily: FontFamily.regular,
                      marginVertical: 8,
                      letterSpacing: 1,
                    }}>
                    {li.name}
                  </Text>
                  <Text
                    style={{
                      fontSize: 16,
                      fontFamily: FontFamily.bold,
                      color: li.color,
                    }}>
                    {li.val}
                  </Text>
                </View>
              );
            })}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
  },
  titleColor: {
    fontSize: 16,
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    paddingRight: 10,
    letterSpacing: 1,
  },
});

export default HighLights;
