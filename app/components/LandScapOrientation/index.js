import {useEffect, useState} from 'react';
import {Dimensions} from 'react-native';
import {useDeviceOrientationChange} from 'react-native-orientation-locker';

export const useIsLandscape = () => {
  const [isLandscape, setIsLandscape] = useState(() => {
    const {width, height} = Dimensions.get('window');
    return width > height;
  });

  // Fallback: detect based on screen size change
  useEffect(() => {
    const onChange = ({window}) => {
      setIsLandscape(window.width > window.height);
    };
    const sub = Dimensions.addEventListener('change', onChange);
    return () => sub?.remove?.();
  }, []);

  useDeviceOrientationChange(ori => {
    if (ori === 'LANDSCAPE-LEFT' || ori === 'LANDSCAPE-RIGHT') {
      setIsLandscape(true);
    } else if (ori === 'PORTRAIT' || ori === 'PORTRAIT-UPSIDEDOWN') {
      setIsLandscape(false);
    } else {
      // Fallback using screen dimensions
      const {width, height} = Dimensions.get('window');
      setIsLandscape(width > height);
    }
  });

  return isLandscape;
};
