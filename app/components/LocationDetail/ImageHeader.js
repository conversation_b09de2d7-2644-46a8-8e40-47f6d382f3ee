import React, {useRef, useState} from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import Iconin from 'react-native-vector-icons/Ionicons';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import BaseColor from '../../config/colors';
import RatingStar from '../RatingStart';
import {FontFamily, FontWeight} from '../../config/typography';
import CButton from '../CButton';
import ViewShot, {captureRef} from 'react-native-view-shot';
import Share from 'react-native-share';
import {CustomIcon} from '../../config/LoadIcons';
import LinearGradient from 'react-native-linear-gradient';
import CustomHeader from '../CustomHeader/CustomHeader';

function ImageHeader({
  imgSource,
  isLike,
  onLeftPress,
  onRightPress,
  onOptionPress,
  title,
  reviewCount,
  rating,
  photoCount,
  loader,
  setLoader,
}) {
  const ref = useRef();

  const shareImage = async () => {
    try {
      const uri = await captureRef(ref, {
        format: 'png',
        quality: 0.7,
      });
      // console.log("uri", uri);
      await Share.open({url: uri});
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <View style={styles.container}>
      {loader ? (
        <ActivityIndicator
          style={{
            height: '100%',
            width: '100%',
          }}
          size={54}
          color={BaseColor.primary}
        />
      ) : (
        <ViewShot ref={ref}>
          <Image
            source={
              imgSource
                ? {uri: imgSource}
                : require('../../assets/images/NoImageFound.jpg')
            }
            style={{
              height: '100%',
              width: '100%',
            }}
            resizeMode="cover"
            // onLoadEnd={() => setLoader(true)}
          />
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.8)']}
            style={{
              position: 'absolute',
              left: 0,
              right: 0,
              bottom: 0,
              height: '50%', // adjust this value to control the height of the gradient
            }}
          />
        </ViewShot>
      )}

      <View
        style={{
          zIndex: 1,
          position: 'absolute',
          width: '100%',
          top: 0,
        }}>
        <CustomHeader
          transparentView
          leftIconName="left-arrow"
          borderCircular
          onLeftPress={() => {
            onLeftPress();
          }}
          backBtn
          secondRighIconName={'share-outline'}
          onSecondRightPress={shareImage}
          rightIconName={isLike ? 'heart-filled' : 'like'}
          rightIconSize={15}
          rightColour={isLike ? BaseColor.red : BaseColor.primary}
          onRightPress={() => {
            onRightPress();
          }}
        />
      </View>

      <View style={[styles.infoContainer]}>
        <Text numberOfLines={2} style={styles.title}>
          {title?.split(' ')?.slice(0, 4)?.join(' ')}
        </Text>
        <View style={styles.reviewContainer}>
          <RatingStar editable={false} totalRating={rating} gap={4} />
          <Text style={styles.review}>
            {reviewCount
              ? reviewCount > 1
                ? `${reviewCount} reviews`
                : '1 review'
              : 'No Reviews'}
          </Text>
        </View>
      </View>
      <View style={styles.photoCountBtn}>
        <CButton
          title={`${photoCount > 1 ? '+' : ''}${photoCount} Photos`}
          style={[styles.btn]}
          titleStyle={{fontSize: 12}}
          onPress={() => {
            setTimeout(() => {
              setLoader(true);
            }, 1000);
            setTimeout(() => {
              setLoader(false);
              // setdone(true);
            }, 3000);
            // Alert.alert(`Route to ${item.suggestionName}`);
          }}
          loader={loader}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 428,
    position: 'relative',
    backgroundColor: BaseColor.whiteColor,
    flex: 1,
  },
  header: {
    position: 'absolute',
    top: 40,
    marginHorizontal: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '90%',
  },
  iconConatiner: {
    backgroundColor: BaseColor.whiteColor,
    justifyContent: 'center',
    alignItems: 'center',
    width: 36,
    height: 36,
    borderRadius: 50,
  },

  infoContainer: {
    position: 'absolute',
    bottom: 35,
    left: 26,
    width: '65%',
  },
  title: {
    color: BaseColor.whiteColor,
    fontSize: 20,
    fontFamily: FontFamily.regular,
    lineHeight: 26,
  },
  reviewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  review: {
    color: BaseColor.whiteColor,
    fontFamily: 'Roboto',
    paddingLeft: 10,
  },

  btn: {
    backgroundColor: 'rgba(82, 82, 82, 0.80)',
    width: '70%',
    borderRadius: 6,
  },
  btnText: {
    fontSize: 12,
    fontFamily: 'Roboto',
    fontWeight: '300',
  },

  photoCountBtn: {
    position: 'absolute',
    bottom: 14,
    right: 0,
  },
});

export default ImageHeader;
