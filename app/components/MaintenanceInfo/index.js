import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Platform,
  Text,
  TouchableOpacity,
} from 'react-native';
import AIcon from 'react-native-vector-icons/AntDesign';
import {FontFamily} from '../../config/typography';
import BaseColor from '../../config/colors';
import {translate} from '../../lang/Translate';
import {SvgXml} from 'react-native-svg';
import commonSvg from '../../assets/commonSvg/commonnSvg';
import {CustomIcon} from '../../config/LoadIcons';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {downloadFile, sendErrorReport} from '../../utils/commonFunction';
import {useIsFocused} from '@react-navigation/native';
import {isEmpty} from 'lodash';
import NewAlert from '../NewAlertModal';

const IOS = Platform.OS === 'ios';
const {width, height} = Dimensions.get('window');
const MaintenanceInfo = ({}) => {
  const isFocused = useIsFocused();
  const [loader, setLoader] = useState(false);
  const [maintenanceInfo, setMaintenanceInfo] = useState({});
  const [visible, setVisible] = useState(false);
  const [btnLoader, setBtnLoader] = useState(false);

  // Save Trip Name API Integration
  const getMaintenanceInfo = async () => {
    try {
      setLoader(true);
      const response = await getApiData(
        BaseSetting.endpoints.maintananceinfo,
        'GET',
      );
      if (response?.success) {
        if (response?.data) {
          setMaintenanceInfo(response?.data[0]);
        }
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      sendErrorReport(error, 'saving_trip_name');
    }
  };
  // End Save Trip Name API Integration

  useEffect(() => {
    getMaintenanceInfo();
  }, [isFocused]);

  const removeMaintanance = async id => {
    try {
      const data = {
        maintenance_id: id || '',
      };
      setBtnLoader(true);
      const response = await getApiData(
        BaseSetting.endpoints.removeMaintenance,
        'POST',
        data,
        '',
        true,
      );
      if (response?.success) {
        getMaintenanceInfo();
        setBtnLoader(false);
      } else {
        setBtnLoader(false);
      }
    } catch (error) {
      setBtnLoader(false);
      sendErrorReport(error, 'saving_trip_name');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.titleColor}>{translate('MaintenanceInfo')}</Text>
      {isEmpty(maintenanceInfo) ? (
        <View style={styles.containerStyle}>
          <View style={{width: '80%', padding: 20}}>
            <Text
              style={
                styles.headerStyle
              }>{`Learn more about maintenance here`}</Text>
            <Text style={styles.contentTxtStyle}>
              {`Tips and tricks for your devices and stroller or any promotional content can be put here in a more interesting format.`}
            </Text>
          </View>
          <View style={styles.imgeContainerStyle}>
            <SvgXml xml={commonSvg.cycleSvg} />
          </View>
        </View>
      ) : (
        <View style={{borderWidth: 1, padding: 20, borderColor: '#E0E5E5'}}>
          <View style={styles.headerContent}>
            <Text
              style={styles.textColor}>{`Task #00${maintenanceInfo?.id}`}</Text>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <View style={styles.labelStyle}>
                <CustomIcon
                  name="smartphone"
                  size={11}
                  style={{marginHorizontal: 4, color: '#6B7580'}}
                />
                <Text style={styles.labelTxt}>
                  {maintenanceInfo?.metric_value} {maintenanceInfo?.metric_type}
                </Text>
              </View>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => setVisible(true)}
                style={{marginLeft: 10}}>
                <AIcon
                  name={'closecircleo'}
                  size={20}
                  color={BaseColor.primary}
                />
              </TouchableOpacity>
            </View>
          </View>
          <Text
            style={{
              fontSize: 25,
              fontFamily: FontFamily.default,
              marginVertical: 20,
            }}>
            {maintenanceInfo?.title}
          </Text>
          <Text style={styles.servicetxt}>
            {maintenanceInfo?.description || ''}
          </Text>
          <Text
            onPress={() =>
              downloadFile(
                maintenanceInfo?.attachment,
                `${maintenanceInfo?.title}.pdf`,
              )
            }
            style={{
              fontFamily: FontFamily.regular,
              textDecorationLine: 'underline',
              color: BaseColor.primary,
            }}>
            {'Download Instruction'}
          </Text>
          <NewAlert
            visible={visible}
            onRequestClose={() => setVisible(false)}
            onCancelPress={() => setVisible(false)}
            loader={btnLoader}
            onOkPress={async () => removeMaintanance(maintenanceInfo?.id)}
            alertTitle={'Delete Maintenance Info'}
            alertMessage={'Are you sure you want to delete it?'}
            agreeTxt={translate('delete')}
          />
        </View>
      )}
      {/* End */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 20,
  },
  titleColor: {
    fontSize: 16,
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    marginBottom: 20,
  },
  containerStyle: {
    flexDirection: 'row',
    borderWidth: 1,
    justifyContent: 'center',
    borderColor: '#BCDEDF',
    borderWidth: 0.5,
    borderRadius: 14,
  },
  headerStyle: {
    fontSize: 16,
    fontFamily: FontFamily.regular,
    color: '#486264',
    paddingBottom: 5,
  },
  contentTxtStyle: {
    fontSize: 12,
    color: '#808080',
    lineHeight: 15,
    fontFamily: FontFamily.regular,
  },
  imgeContainerStyle: {
    width: '23%',
    paddingTop: 15,
  },
  labelStyle: {
    flexDirection: 'row',
    borderWidth: 1,
    paddingHorizontal: 10,
    paddingVertical: 7,
    backgroundColor: '#F5F6F7',
    marginHorizontal: 3,
    borderColor: '#DCDFE3',
    alignItems: 'center',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 5,
    alignItems: 'center',
  },
  textColor: {
    fontSize: 18,
    color: '#9BA1A8',
    fontFamily: FontFamily.default,
  },
  servicetxt: {
    fontSize: 16,
    fontFamily: FontFamily.default,
    color: BaseColor.error,
    marginBottom: 20,
  },
  labelTxt: {
    fontSize: 12,
    color: '#6B7580',
    fontFamily: FontFamily.regular,
  },
});

export default MaintenanceInfo;
