import React, {useEffect, useState, useRef, useCallback} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  AppState,
  ActivityIndicator,
  Platform,
} from 'react-native';
import MapView, {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE} from 'react-native-maps';
import {debounce} from 'lodash';
import {parseString} from 'react-native-xml2js';
import Iconin from 'react-native-vector-icons/Ionicons';
import haversine from 'haversine-distance';
import BackgroundGeolocation from 'react-native-background-geolocation';
import mapStyle from '../../config/mapCustomStyle';
import FIcon from 'react-native-vector-icons/Feather';
import {sendErrorReport} from '../../utils/commonFunction';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';
import {useSelector} from 'react-redux';
import {MMKV} from 'react-native-mmkv';

const mmkv = new MMKV();
const TRIP_DATA_KEY = 'trip_location_data';
const COMPLETED_TRIP_KEY = 'completed_trip_data';
const REMAINING_TRIP_KEY = 'remaining_trip_data';
const CURRENT_TRIP_KEY = 'currrent_trip_data';
const COORDINATE_TRIP_KEY = 'coordinate_trip_data';

const {width, height} = Dimensions.get('window');

const GPXMapScreen = props => {
  const {
    uri,
    type,
    mapContaierStyle,
    searchedTrip,
    isPause,
    mapLoader,
    getCurrentPositions = () => {},
  } = props;
  const {isTimerStart, tripDistance} = useSelector(state => state.place);
  const [coordinates, setCoordinates] = useState(
    mmkv.contains(COORDINATE_TRIP_KEY)
      ? JSON.parse(mmkv.getString(COORDINATE_TRIP_KEY))
      : [],
  );
  const [region, setRegion] = useState({
    latitude: 22.6911338,
    longitude: 72.8617105,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });
  const [completedPath, setCompletedPath] = useState([]);
  const [remainingPath, setRemainingPath] = useState([]);
  const [currentPosition, setCurrentPosition] = useState(null);
  const [routeCoordinates, setRouteCoordinates] = useState([]);
  const [currentToStartPath, setCurrentToStartPath] = useState([]); // Dotted path to start
  const [tripCompleted, setTripCompleted] = useState(false);
  const [userHeading, setUserHeading] = useState(0);
  const [pathHeading, setPathHeading] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [distance, setDistance] = useState(tripDistance);
  const [averageSpeed, setAverageSpeed] = useState(0);
  const [appState, setAppState] = useState(AppState.currentState);

  const mapRef = useRef(null);
  const lastUpdatedPosition = useRef(null);
  const lastUpdateTime = useRef(null);
  const isInitializingRef = useRef(false); // Track initialization status
  const isInBackgroundRef = useRef(false); // Add this ref to reliably track background state

  const config = {
    desiredAccuracy: BackgroundGeolocation.DESIRED_ACCURACY_HIGH,
    distanceFilter: 2, // Smaller distance filter (was 5)
    stationaryRadius: 10, // Radius for stationary detection (meters)
    locationUpdateInterval: 500, // More frequent updates (was 1000)
    fastestLocationUpdateInterval: 250, // More frequent updates (was 500)
    activityRecognitionInterval: 5000,
    disableMotionActivityUpdates: false,
    stopOnStationary: false, // Don't stop when stationary
    preventSuspend: true,
    locationAuthorizationRequest: 'Always',
    allowsBackgroundLocationUpdates: true,
    pausesLocationUpdatesAutomatically: false,
    showsBackgroundLocationIndicator: true,
    debug: false, // Enable logging during development
    stopOnTerminate: false,
    startOnBoot: true,
    foregroundService: true,
    enableHeadless: true,
    disableElasticity: true,
    heartbeatInterval: 30, // Reduced to 30 seconds to get more frequent updates
    activityType: BackgroundGeolocation.ACTIVITY_TYPE_OTHER_NAVIGATION,
    // Add these for better background performance:
    persistMode: BackgroundGeolocation.PERSIST_MODE_ALL,
    maxRecordsToPersist: 1000,
    stopTimeout: 1, // Reduce CPU usage when possible
  };

  const stopLocation = () => {
    setDistance(0);
    setAverageSpeed(0);
    setCoordinates([]);
    setCompletedPath([]);
    setRemainingPath([]);
    setRouteCoordinates([]);
    setCurrentToStartPath([]);
    mmkv.delete(COMPLETED_TRIP_KEY);
    mmkv.delete(REMAINING_TRIP_KEY);
    mmkv.delete(TRIP_DATA_KEY);
    mmkv.delete(CURRENT_TRIP_KEY);
    mmkv.delete(COORDINATE_TRIP_KEY);
    setCurrentPosition(null);
    setTripCompleted(false);
  };
  // Get Current Location if uri lik is not there.
  const fetchCurrentLocation = async () => {
    try {
      const position = await BackgroundGeolocation.getCurrentPosition({
        persist: false,
        samples: 1,
        timeout: 30,
        maximumAge: 5000,
        desiredAccuracy: 10,
      });

      const {latitude, longitude} = position.coords;
      const defaultLocation = {
        latitude,
        longitude,
        latitudeDelta: 0.005,
        longitudeDelta: 0.005,
      };
      setRegion(defaultLocation);
      if (!isTimerStart && searchedTrip === undefined) {
        stopLocation();
      }
    } catch (bgError) {
      console.warn('[getCurrentPosition] BG ERROR', bgError);
    }
  };

  // Initialize BackgroundGeolocation
  const initializeBackgroundGeolocation = async () => {
    if (isInitializingRef.current) return;
    isInitializingRef.current = true;

    try {
      // Check if service is already running
      const state = await BackgroundGeolocation.getState();
      if (state.enabled) {
        console.log(
          '[MapRendering] Service already running, just attaching listeners',
        );

        // Just attach our listeners without reconfiguring
        BackgroundGeolocation.onLocation(
          location => {
            console.log(
              '[onLocation] in Map Rendering Screen------>>>>',
              location,
              isTimerStart,
              isPause,
            );
            updatePosition(location.coords, location.odometer);
            sendErrorReport(location, 'onLocation_trip_tracking');
          },
          error => {
            console.error('[onLocation] ERROR:', error);
            sendErrorReport(error, 'onLocation_error_trip_tracking');
          },
        );

        isInitializingRef.current = false;
        return;
      }

      // Register listeners once
      BackgroundGeolocation.onLocation(
        location => {
          console.log(
            '[onLocation] in Map Rednering Screen------>>>>',
            location,
          );
          updatePosition(location.coords, location.odometer);
          sendErrorReport(location, 'onLocation_trip_tracking');
        },
        error => {
          console.error('[onLocation] ERROR:', error);
          sendErrorReport(error, 'onLocation_error_trip_tracking');
        },
      );

      // Configure
      await BackgroundGeolocation.ready(config);
      isInitializingRef.current = false;
    } catch (error) {
      console.error('Error initializing background geolocation:', error);
      sendErrorReport(error, 'init_bg_geolocation_error');
      isInitializingRef.current = false;
    }
  };

  useEffect(() => {
    if (
      !isPause &&
      searchedTrip === undefined &&
      !tripCompleted &&
      isTimerStart
    ) {
      // Start tracking
      initializeBackgroundGeolocation().then(async () => {
        console.log('BackgroundGeolocation started');
        await BackgroundGeolocation.start();
        await BackgroundGeolocation.changePace(true); // Start tracking immediately
      });
    }
  }, [isPause, searchedTrip, tripCompleted, isTimerStart]);

  // Reset Map After Stop Trip..
  // Update the useEffect that handles timer state
  useEffect(() => {
    if (!isTimerStart) {
      // Reset all states
      console.log('BackgroundGeolocation stoppedn333333');
      BackgroundGeolocation.stop();
      stopLocation();
    }
  }, [isTimerStart]);

  useEffect(() => {
    if (uri) {
      const fetchGPX = async () => {
        try {
          const response = await fetch(uri);
          const gpxData = await response.text();

          parseString(gpxData, (err, result) => {
            if (err || !result?.gpx?.trk?.[0]?.trkseg?.[0]?.trkpt) {
              console.error('Error parsing GPX data:', err || 'Invalid format');
              return;
            }

            // Extract track points from all segments
            const trackPoints = result.gpx.trk[0].trkseg.map(segment =>
              segment.trkpt.map(point => ({
                latitude: parseFloat(point.$.lat),
                longitude: parseFloat(point.$.lon),
              })),
            );
            if (trackPoints.length > 0) {
              // Store in MMKV
              mmkv.set(COORDINATE_TRIP_KEY, JSON.stringify(trackPoints));
              setCoordinates(trackPoints);
              saveRemainingPath(trackPoints);
              sendErrorReport(trackPoints, 'trackPoints_coordinates');
              // Set the region based on the first coordinate or startCordinate
              const rawTrackPoints = trackPoints.flat();
              // Calculate center point of the route
              const centerLat =
                rawTrackPoints.reduce((sum, point) => sum + point.latitude, 0) /
                rawTrackPoints.length;
              const centerLng =
                rawTrackPoints.reduce(
                  (sum, point) => sum + point.longitude,
                  0,
                ) / rawTrackPoints.length;
              // Calculate dynamic delta values
              const deltas = calculateDynamicDelta(rawTrackPoints);
              setRegion({
                latitude: centerLat,
                longitude: centerLng,
                latitudeDelta: deltas?.latitudeDelta,
                longitudeDelta: deltas?.longitudeDelta,
              });
            } else {
              console.error('No valid track points found in the GPX data.');
            }
          });
        } catch (error) {
          console.error('Error fetching GPX:', error);
        }
      };

      fetchGPX();
    } else {
      fetchCurrentLocation();
    }
  }, [uri]);

  useEffect(() => {
    if (routeCoordinates) {
      getCurrentPositions({
        route: routeCoordinates,
        distance: distance,
        speed: averageSpeed,
      });
    }
  }, [routeCoordinates, distance, averageSpeed]);

  // Calculate dynamic delta for map region
  const calculateDynamicDelta = useCallback(coordinate => {
    if (!coordinate || coordinate.length === 0) {
      return {latitudeDelta: 0.005, longitudeDelta: 0.005};
    }
    let minLat = coordinate[0].latitude;
    let maxLat = coordinate[0].latitude;
    let minLng = coordinate[0].longitude;
    let maxLng = coordinate[0].longitude;
    coordinate.forEach(coord => {
      minLat = Math.min(minLat, coord.latitude);
      maxLat = Math.max(maxLat, coord.latitude);
      minLng = Math.min(minLng, coord.longitude);
      maxLng = Math.max(maxLng, coord.longitude);
    });
    const latDelta = (maxLat - minLat) * 1.2;
    const lngDelta = (maxLng - minLng) * 1.2;
    return {
      latitudeDelta: Math.max(latDelta, 0.001),
      longitudeDelta: Math.max(lngDelta, 0.001),
    };
  }, []);

  // Calculate bearing
  const calculateBearing = (startPoint, endPoint) => {
    const startLat = (startPoint.latitude * Math.PI) / 180;
    const startLng = (startPoint.longitude * Math.PI) / 180;
    const endLat = (endPoint.latitude * Math.PI) / 180;
    const endLng = (endPoint.longitude * Math.PI) / 180;
    const dLng = endLng - startLng;
    const y = Math.sin(dLng) * Math.cos(endLat);
    const x =
      Math.cos(startLat) * Math.sin(endLat) -
      Math.sin(startLat) * Math.cos(endLat) * Math.cos(dLng);
    let bearing = (Math.atan2(y, x) * 180) / Math.PI;
    if (bearing < 0) {
      bearing += 360;
    }
    return bearing;
  };

  // Saved Trip Data...
  const saveTripData = newCoordinate => {
    const tripDataString = mmkv.getString(TRIP_DATA_KEY);
    const tripData = tripDataString ? JSON.parse(tripDataString) : [];
    tripData.push(newCoordinate);
    sendErrorReport(tripDataString, 'newCoordinate_tracking');
    setRouteCoordinates(tripData);
    mmkv.set(TRIP_DATA_KEY, JSON.stringify(tripData));
  };
  // Saved Completed Path Data...
  const saveCompletedPath = newCoordinate => {
    sendErrorReport(newCoordinate, 'completed_path_coordinate');
    setCompletedPath(newCoordinate);
    mmkv.set(COMPLETED_TRIP_KEY, JSON.stringify(newCoordinate));
  };
  // Saved Remaining Path Data...
  const saveRemainingPath = newCoordinate => {
    sendErrorReport(newCoordinate, 'remaining_path_coordinate');
    setRemainingPath(newCoordinate);
    mmkv.set(REMAINING_TRIP_KEY, JSON.stringify(newCoordinate));
  };
  // Saved Current Path Data...
  const saveCurrentToStartPath = newCoordinate => {
    sendErrorReport(newCoordinate, 'current_path_coordinate');
    setCurrentToStartPath(newCoordinate);
    mmkv.set(CURRENT_TRIP_KEY, JSON.stringify(newCoordinate));
  };

  //get current positionn to pathline ...
  const fetchOSRMDirections = async (start, end) => {
    const url = `https://router.project-osrm.org/route/v1/driving/${start.longitude},${start.latitude};${end.longitude},${end.latitude}?overview=full&geometries=geojson`;
    const response = await fetch(url);
    const data = await response.json();
    if (data.routes && data.routes.length > 0) {
      const route = data.routes[0].geometry.coordinates.map(
        ([longitude, latitude]) => ({
          latitude,
          longitude,
        }),
      );
      return [start, ...route];
    }
    return [];
  };

  const getCalculationdistance = (point1, point2) => {
    const R = 6371e3;
    const φ1 = (point1.latitude * Math.PI) / 180;
    const φ2 = (point2.latitude * Math.PI) / 180;
    const Δφ = ((point2.latitude - point1.latitude) * Math.PI) / 180;
    const Δλ = ((point2.longitude - point1.longitude) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  };

  const updatePositionWithDebounce = useCallback(
    debounce((position, dist) => {
      const {latitude, longitude, heading, speed, accuracy} = position;
      sendErrorReport(position, 'already_trip_update_position');
      const newCoordinate = {latitude, longitude};
      const currentTime = Date.now(); // Get Current Time.
      sendErrorReport(heading, 'heading_coordinate');
      sendErrorReport(speed, 'speed_coordinate');

      // Update user heading if available
      if (heading >= 0) {
        setUserHeading(heading);
      } else if (lastUpdatedPosition.current) {
        const calculatedHeading = calculateBearing(
          lastUpdatedPosition.current,
          newCoordinate,
        );
        setUserHeading(calculatedHeading);
      }

      // Handle speed from GPS - ensure it's properly set to 0 when not moving
      if (speed === -1) {
        setAverageSpeed(0);
      } else {
        const speedKmh = speed * 3.6;
        setAverageSpeed(speedKmh);
      }

      if (coordinates.length > 0) {
        const pathPoints =
          searchedTrip || coordinates.length > 1 ? coordinates : coordinates[0];

        // Find the closest point on the path
        let closestIndex = 0;
        let minDistance = Infinity;

        pathPoints &&
          pathPoints?.forEach((point, index) => {
            const distance = haversine(newCoordinate, point);
            if (distance < minDistance) {
              minDistance = distance;
              closestIndex = index;
            }
          });

        // Calculate direction to the next point on the path
        if (closestIndex < pathPoints.length - 1) {
          const nextPoint = pathPoints[closestIndex + 1];
          const bearing = calculateBearing(newCoordinate, nextPoint);
          setPathHeading(bearing);
        }

        sendErrorReport(pathPoints, 'pathPoints_coordinates');
        if (!lastUpdatedPosition.current) {
          lastUpdatedPosition.current = newCoordinate;
          setStartTime(currentTime);
          saveTripData(newCoordinate);
          return;
        }
        const movementDistance = getCalculationdistance(
          lastUpdatedPosition.current,
          newCoordinate,
        );

        // Only update if moved more than 1 meters to account for GPS fluctuations
        if (dist === 0) {
          const manualdistance = tripDistance + movementDistance;
          setDistance(manualdistance);
          sendErrorReport(manualdistance, 'manualdistance_update');
        } else {
          setDistance(dist);
        }
        lastUpdatedPosition.current = newCoordinate;
        saveTripData(newCoordinate);
        setCurrentPosition(newCoordinate);
        setRegion(prev => ({
          latitude: newCoordinate.latitude,
          longitude: newCoordinate.longitude,
          latitudeDelta: prev?.latitudeDelta || 0.0009,
          longitudeDelta: prev?.longitudeDelta || 0.0009,
        }));
        setRegion({
          latitude: newCoordinate?.latitude || 0,
          longitude: newCoordinate?.longitude || 0,
          latitudeDelta: 0.0005,
          longitudeDelta: 0.0005,
        });

        // Find the closest point on the path
        let closestIndexPath = 0;
        let minDistancePath = Infinity;

        pathPoints &&
          pathPoints?.forEach((point, index) => {
            const distance = haversine(newCoordinate, point);
            if (distance < minDistancePath) {
              minDistancePath = distance;
              closestIndexPath = index;
            }
          });

        // If the user hasn't moved along the path, completed path is empty
        if (searchedTrip) {
          setCompletedPath([]);
          setCurrentToStartPath([]);
          mmkv.delete(COMPLETED_TRIP_KEY);
          mmkv.delete(CURRENT_TRIP_KEY);
          saveRemainingPath(coordinates);
          setCurrentPosition(newCoordinate);
          return;
        }

        // Get the end point of the route
        const endPoint =
          coordinates.length > 1
            ? coordinates[coordinates.length - 1][
                coordinates[coordinates.length - 1].length - 1
              ]
            : coordinates[0][coordinates[0].length - 1];

        // Check if user has reached the end point (within 10 meters)
        const distanceToEnd = haversine(newCoordinate, endPoint);
        if (distanceToEnd <= 2 && !tripCompleted) {
          // Mark trip as completed
          setTripCompleted(true);
          const completedPath =
            coordinates.length > 1 ? coordinates : [coordinates[0]];
          // Mark the entire path as completed
          saveCompletedPath(completedPath);
          setRemainingPath([]);
          mmkv.delete(REMAINING_TRIP_KEY);

          // Set current position
          setCurrentPosition(newCoordinate);
          return;
        }

        // Check if the user is on the route
        const onRoute = isOnRoute(newCoordinate, pathPoints);

        if (onRoute) {
          console.log('User is on the route.');
          // When on route, show completed path and remaining path
          // Split the path into completed and remaining
          const newCompletedPath = pathPoints.slice(0, closestIndexPath + 1);
          const newRemainingPath = pathPoints.slice(closestIndexPath + 1);
          const onRouteCompletedPath =
            coordinates.length > 1 ? newCompletedPath : [newCompletedPath];
          saveCompletedPath(onRouteCompletedPath);
          const onRouteRemainingPath =
            coordinates.length > 1 ? newRemainingPath : [newRemainingPath];
          saveRemainingPath(onRouteRemainingPath);

          // Clear the path to start when on route
          setCurrentToStartPath([]);
          mmkv.delete(CURRENT_TRIP_KEY);
        } else {
          console.log('Warning: Current position is off-route!');

          // // When off route, don't mark any part of the path as completed
          // setCompletedPath([]);
          // mmkv.delete(COMPLETED_TRIP_KEY);
          const remainingPath =
            coordinates.length > 1 ? coordinates : [coordinates[0]];
          saveRemainingPath(remainingPath);

          // Get the nearest point on the route
          const pathArray =
            pathPoints.length > 0 ? pathPoints.flat() : pathPoints;
          const nearestPoint = pathArray[closestIndexPath];

          // Recalculate route to nearest point
          fetchOSRMDirections(newCoordinate, nearestPoint).then(newRoute => {
            if (newRoute.length > 0) {
              saveCurrentToStartPath(newRoute);
            }
          });

          // Adjust map region to guide user
          setRegion({
            latitude: (newCoordinate.latitude + nearestPoint.latitude) / 2,
            longitude: (newCoordinate.longitude + nearestPoint.longitude) / 2,
            latitudeDelta: 0.0009,
            longitudeDelta: 0.0009,
          });
        }

        setCurrentPosition(newCoordinate);
      }
    }, 100),
    [routeCoordinates, tripCompleted, startTime, appState],
  );

  const updatePositionDirect = useCallback(
    (position, dist) => {
      const {latitude, longitude, heading, speed, accuracy} = position;
      sendErrorReport(position, 'already_trip_update_position');
      const newCoordinate = {latitude, longitude};
      const currentTime = Date.now(); // Get Current Time.
      sendErrorReport(heading, 'heading_coordinate');
      sendErrorReport(speed, 'speed_coordinate');

      // Update user heading if available
      if (heading >= 0) {
        setUserHeading(heading);
      } else if (lastUpdatedPosition.current) {
        const calculatedHeading = calculateBearing(
          lastUpdatedPosition.current,
          newCoordinate,
        );
        setUserHeading(calculatedHeading);
      }

      // Handle speed from GPS - ensure it's properly set to 0 when not moving
      if (speed === -1) {
        setAverageSpeed(0);
      } else {
        const speedKmh = speed * 3.6;
        setAverageSpeed(speedKmh);
      }

      if (coordinates.length > 0) {
        const pathPoints =
          searchedTrip || coordinates.length > 1 ? coordinates : coordinates[0];

        // Find the closest point on the path
        let closestIndex = 0;
        let minDistance = Infinity;

        pathPoints &&
          pathPoints?.forEach((point, index) => {
            const distance = haversine(newCoordinate, point);
            if (distance < minDistance) {
              minDistance = distance;
              closestIndex = index;
            }
          });

        // Calculate direction to the next point on the path
        if (closestIndex < pathPoints.length - 1) {
          const nextPoint = pathPoints[closestIndex + 1];
          const bearing = calculateBearing(newCoordinate, nextPoint);
          setPathHeading(bearing);
        }

        sendErrorReport(pathPoints, 'pathPoints_coordinates');
        if (!lastUpdatedPosition.current) {
          lastUpdatedPosition.current = newCoordinate;
          setStartTime(currentTime);
          saveTripData(newCoordinate);
          return;
        }
        const movementDistance = getCalculationdistance(
          lastUpdatedPosition.current,
          newCoordinate,
        );

        // Only update if moved more than 1 meters to account for GPS fluctuations
        if (dist === 0) {
          const manualdistance = tripDistance + movementDistance;
          setDistance(manualdistance);
          sendErrorReport(manualdistance, 'manualdistance_update');
        } else {
          setDistance(dist);
        }
        lastUpdatedPosition.current = newCoordinate;
        saveTripData(newCoordinate);
        setCurrentPosition(newCoordinate);
        setRegion(prev => ({
          latitude: newCoordinate.latitude,
          longitude: newCoordinate.longitude,
          latitudeDelta: prev?.latitudeDelta || 0.0009,
          longitudeDelta: prev?.longitudeDelta || 0.0009,
        }));
        setRegion({
          latitude: newCoordinate?.latitude || 0,
          longitude: newCoordinate?.longitude || 0,
          latitudeDelta: 0.0005,
          longitudeDelta: 0.0005,
        });

        // Find the closest point on the path
        let closestIndexPath = 0;
        let minDistancePath = Infinity;

        pathPoints &&
          pathPoints?.forEach((point, index) => {
            const distance = haversine(newCoordinate, point);
            if (distance < minDistancePath) {
              minDistancePath = distance;
              closestIndexPath = index;
            }
          });

        // If the user hasn't moved along the path, completed path is empty
        if (searchedTrip) {
          setCompletedPath([]);
          setCurrentToStartPath([]);
          mmkv.delete(COMPLETED_TRIP_KEY);
          mmkv.delete(CURRENT_TRIP_KEY);
          saveRemainingPath(coordinates);
          setCurrentPosition(newCoordinate);
          return;
        }

        // Get the end point of the route
        const endPoint =
          coordinates.length > 1
            ? coordinates[coordinates.length - 1][
                coordinates[coordinates.length - 1].length - 1
              ]
            : coordinates[0][coordinates[0].length - 1];

        // Check if user has reached the end point (within 10 meters)
        const distanceToEnd = haversine(newCoordinate, endPoint);
        if (distanceToEnd <= 2 && !tripCompleted) {
          // Mark trip as completed
          setTripCompleted(true);
          const completedPath =
            coordinates.length > 1 ? coordinates : [coordinates[0]];
          // Mark the entire path as completed
          saveCompletedPath(completedPath);
          setRemainingPath([]);
          mmkv.delete(REMAINING_TRIP_KEY);

          // Set current position
          setCurrentPosition(newCoordinate);
          return;
        }

        // Check if the user is on the route
        const onRoute = isOnRoute(newCoordinate, pathPoints);

        if (onRoute) {
          console.log('User is on the route.');
          // When on route, show completed path and remaining path
          // Split the path into completed and remaining
          const newCompletedPath = pathPoints.slice(0, closestIndexPath + 1);
          const newRemainingPath = pathPoints.slice(closestIndexPath + 1);
          const onRouteCompletedPath =
            coordinates.length > 1 ? newCompletedPath : [newCompletedPath];
          saveCompletedPath(onRouteCompletedPath);
          const onRouteRemainingPath =
            coordinates.length > 1 ? newRemainingPath : [newRemainingPath];
          saveRemainingPath(onRouteRemainingPath);

          // Clear the path to start when on route
          setCurrentToStartPath([]);
          mmkv.delete(CURRENT_TRIP_KEY);
        } else {
          console.log('Warning: Current position is off-route!');

          // // When off route, don't mark any part of the path as completed
          // setCompletedPath([]);
          // mmkv.delete(COMPLETED_TRIP_KEY);
          const remainingPath =
            coordinates.length > 1 ? coordinates : [coordinates[0]];
          saveRemainingPath(remainingPath);

          // Get the nearest point on the route
          const pathArray =
            pathPoints.length > 0 ? pathPoints.flat() : pathPoints;
          const nearestPoint = pathArray[closestIndexPath];

          // Recalculate route to nearest point
          fetchOSRMDirections(newCoordinate, nearestPoint).then(newRoute => {
            if (newRoute.length > 0) {
              saveCurrentToStartPath(newRoute);
            }
          });

          // Adjust map region to guide user
          setRegion({
            latitude: (newCoordinate.latitude + nearestPoint.latitude) / 2,
            longitude: (newCoordinate.longitude + nearestPoint.longitude) / 2,
            latitudeDelta: 0.0009,
            longitudeDelta: 0.0009,
          });
        }

        setCurrentPosition(newCoordinate);
      }
    },
    [routeCoordinates, tripCompleted, startTime, appState],
  );

  // Use this function as the main entry point that decides which implementation to use
  const updatePosition = useCallback(
    (position, distance) => {
      console.log('🚀 ~ position----------------------:', position);
      if (isInBackgroundRef.current && Platform.OS === 'android') {
        // In background, use direct version to ensure all points are captured
        updatePositionDirect(position, distance);
      } else {
        // In foreground, use debounced version for performance
        updatePositionWithDebounce(position, distance);
      }
    },
    [updatePositionDirect, updatePositionWithDebounce],
  );

  const isOnRoute = (currentPosition, route, threshold = 10) => {
    for (let i = 0; i < route.length - 1; i++) {
      const segmentStart = route[i];
      const segmentEnd = route[i + 1];
      const distance = shortestDistanceToSegment(
        currentPosition,
        segmentStart,
        segmentEnd,
      );
      if (distance <= threshold) {
        return true;
      }
    }
    return false;
  };

  const shortestDistanceToSegment = (point, segmentStart, segmentEnd) => {
    const A = point.latitude - segmentStart.latitude;
    const B = point.longitude - segmentStart.longitude;
    const C = segmentEnd.latitude - segmentStart.latitude;
    const D = segmentEnd.longitude - segmentStart.longitude;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    const param = lenSq !== 0 ? dot / lenSq : -1;

    let nearestLat, nearestLng;

    if (param < 0) {
      nearestLat = segmentStart.latitude;
      nearestLng = segmentStart.longitude;
    } else if (param > 1) {
      nearestLat = segmentEnd.latitude;
      nearestLng = segmentEnd.longitude;
    } else {
      nearestLat = segmentStart.latitude + param * C;
      nearestLng = segmentStart.longitude + param * D;
    }

    return haversine(point, {latitude: nearestLat, longitude: nearestLng});
  };

  // Add AppState listener to detect background/foreground transitions
  useEffect(() => {
    const handleAppStateChange = nextAppState => {
      console.log(`[MapRendering] App State changed to: ${nextAppState}`);

      if (nextAppState === 'active') {
        setAppState('active');
        isInBackgroundRef.current = false;
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        setAppState(nextAppState);
        isInBackgroundRef.current = true;

        // Log this transition to verify it's happening
        sendErrorReport(
          {
            event: 'map_to_background',
            timestamp: new Date().toISOString(),
          },
          'map_state_transition',
        );
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => subscription.remove();
  }, []);

  // Add this effect to improve background geolocation handling
  useEffect(() => {
    // When the app comes back to foreground, sync any missed locations
    if (appState === 'active' && isTimerStart && !isPause) {
      BackgroundGeolocation.getLocations().then(locations => {
        if (locations.length > 0) {
          console.log(`Processing ${locations.length} stored locations`);
          // Process locations in order
          locations.sort((a, b) => a.timestamp - b.timestamp);
          locations.forEach(location => {
            updatePositionDirect(location.coords, location.odometer);
          });
          BackgroundGeolocation.destroyLocations();
        }
      });
    }
  }, [appState, isTimerStart, isPause]);

  return (
    <View style={[styles.container]}>
      {mapLoader ? (
        <ActivityIndicator
          style={{justifyContent: 'center', alignItems: 'center', flex: 1}}
        />
      ) : (
        <MapView
          ref={mapRef}
          provider={PROVIDER_GOOGLE}
          style={[styles.map, mapContaierStyle]}
          customMapStyle={mapStyle}
          region={region}
          pitchEnabled={false}
          rotateEnabled={false}
          scrollEnabled={true}
          zoomEnabled={true}>
          {coordinates.length > 0 && type !== 'saveTrip' && (
            <Marker
              coordinate={coordinates[0][0]}
              title="Start Point"
              description="This is the starting location.">
              <View style={styles.startMarker}>
                <Iconin name="location" size={25} color="#5255F1" />
              </View>
            </Marker>
          )}
          {coordinates.length === 1 && type !== 'saveTrip' && (
            <Marker
              coordinate={coordinates[0][coordinates[0].length - 1]}
              title="End Point">
              <View style={[styles.startMarker, {backgroundColor: '#ffcccc'}]}>
                <Iconin name="location" size={25} color="#FF0000" />
              </View>
            </Marker>
          )}
          {coordinates.length > 1 && type !== 'saveTrip' && (
            <Marker
              coordinate={coordinates[coordinates.length - 1][0]}
              title="End Point">
              <View style={[styles.startMarker, {backgroundColor: '#ffcccc'}]}>
                <Iconin name="location" size={25} color="#FF0000" />
              </View>
            </Marker>
          )}

          {completedPath?.map((segment, index) => (
            <Polyline
              key={index}
              coordinates={segment}
              strokeWidth={4}
              strokeColor={'#ff9393'}
            />
          ))}
          {remainingPath?.map((segment, index) => (
            <Polyline
              key={index}
              coordinates={segment}
              strokeWidth={2}
              strokeColor={BaseColor.textGrey1}
            />
          ))}
          {currentToStartPath.length > 0 && type !== 'saveTrip' && (
            <Polyline
              coordinates={currentToStartPath}
              strokeWidth={3}
              strokeColor="#FA6912"
            />
          )}
          {currentPosition &&
            type !== 'saveTrip' &&
            searchedTrip === undefined && (
              <Marker coordinate={currentPosition} rotation={userHeading}>
                <View style={styles.markerImage}>
                  <FIcon name={'navigation-2'} color={'#6a8889'} size={16} />
                </View>
              </Marker>
            )}
        </MapView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {borderRadius: 10},
  map: {height: height / 2.8, width: '100%'},
  markerImage: {
    width: 30,
    height: 30,
    borderRadius: 30,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
  startMarker: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 30,
    height: 30,
    borderRadius: 30,
    backgroundColor: '#bfbdfc',
  },
  saveTripPoint: {
    width: 12,
    height: 12,
    borderRadius: 10,
    backgroundColor: '#87B5B3',
  },
  loaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  iconContainer: {
    marginBottom: 10,
    backgroundColor: 'rgba(82, 85, 241, 0.1)',
    borderRadius: 50,
    padding: 10,
  },
  modalTitle: {
    fontSize: 22,
    fontFamily: FontFamily.bold,
    marginBottom: 10,
    color: BaseColor.primary,
    textAlign: 'center',
  },
  modalMessage: {
    fontFamily: FontFamily.regular,
    marginBottom: 20,
    textAlign: 'center',
    color: '#666',
    fontSize: 16,
    lineHeight: 22,
  },
  modalButton: {
    backgroundColor: BaseColor.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    minWidth: 100,
    alignItems: 'center',
  },
  modalButtonText: {
    fontFamily: FontFamily.semibold,
    color: 'white',
    fontWeight: '600',
  },
});

export default GPXMapScreen;
