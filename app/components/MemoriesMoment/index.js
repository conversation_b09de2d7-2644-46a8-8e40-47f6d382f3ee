import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Platform,
  Text,
  Switch,
  FlatList,
  ScrollView,
  Image,
} from 'react-native';
import {FontFamily} from '../../config/typography';
import BaseColor from '../../config/colors';
import {CustomIcon} from '../../config/LoadIcons';
import {Images} from '../../config/Images';
import {translate} from '../../lang/Translate';
import SwitchComponent from '../Switch';
import {useSelector} from 'react-redux';
import {openInAppBrowser} from '../../utils/commonFunction';

const IOS = Platform.OS === 'ios';
const {width, height} = Dimensions.get('window');
const MemoriesMoment = ({}) => {
  const [isEnabled, setIsEnabled] = useState(false);
  const {connectedDeviceList} = useSelector(sta => sta.modal);
  const toggleSwitch = () => setIsEnabled(previousState => !previousState);
  const array = [
    {id: 1, image: Images.logo},
    {id: 2, image: Images.logo},
    {id: 3, image: Images.logo},
    {id: 4, image: Images.logo},
    {id: 5, image: Images.logo},
    {id: 6, image: Images.logo},
    {id: 7, image: Images.logo},
  ];

  const renderItem = ({item, ind}) => {
    return (
      <View
        style={[
          styles.item,
          {backgroundColor: ind % 2 ? '#DEDEDD' : '#EEF5F5'},
        ]}>
        {isEnabled && (
          <Image
            source={item?.image}
            style={{width: width / 7.5, height: width / 7.5}}
          />
        )}
      </View>
    );
  };

  const chunkData = (data, size) => {
    const chunks = [];
    for (let i = 0; i < data.length; i += size) {
      chunks.push(data.slice(i, i + size));
    }
    return chunks;
  };
  const rows = chunkData(array, 4);
  return (
    <View style={styles.container}>
      <View
        style={{
          justifyContent: 'space-between',
          alignItems: 'center',
          flexDirection: 'row',
        }}>
        <Text
          style={{
            fontSize: 16,
            fontFamily: FontFamily.bold,
            color: BaseColor.blackColor,
          }}>
          {translate('MemoriesMoments')}
        </Text>
        {connectedDeviceList?.camera_detail >= 1 && (
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            {isEnabled && (
              <View
                style={{
                  width: 4,
                  height: 4,
                  borderRadius: 10,
                  backgroundColor: '#CE1F1F',
                  position: 'absolute',
                  left: 8,
                  bottom: 9.25,
                }}
              />
            )}
            <CustomIcon name="Recorder" size={20} style={{marginRight: 5}} />
            <SwitchComponent value={isEnabled} onValueChange={toggleSwitch} />
          </View>
        )}
      </View>
      {connectedDeviceList?.camera_detail >= 1 ? (
        <View style={{flexDirection: 'row', marginVertical: 5, marginTop: 25}}>
          <View
            style={{
              width: width / 3,
              height: width / 3,
              borderRadius: 10,
              marginRight: 10,
              backgroundColor: '#E9E9E9',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <View
              style={{
                width: 20,
                backgroundColor: '#DEDEDD',
                position: 'absolute',
                right: 10,
                top: 10,
              }}>
              <Text
                style={{
                  color: BaseColor.whiteColor,
                  fontSize: 5,
                  textAlign: 'center',
                  paddingVertical: 1,
                }}>
                LIVE
              </Text>
            </View>
            <CustomIcon
              name="Scanner-thick"
              color={BaseColor.whiteColor}
              size={80}
            />
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            directionalLockEnabled={true}
            alwaysBounceVertical={false}>
            <FlatList
              data={rows}
              renderItem={({item}) => (
                <View style={styles.row}>
                  {item.map((subItem, ind) => renderItem({item: subItem, ind}))}
                </View>
              )}
              keyExtractor={item => item.id}
              numColumns={Math.ceil(rows.length / 2)}
              scrollEnabled={false}
            />
          </ScrollView>
        </View>
      ) : (
        <View style={styles.emptyContainer}>
          <CustomIcon
            name={'Film-record'}
            size={70}
            style={{marginVertical: 16}}
            color={BaseColor.primary}
          />
          <Text
            style={styles.smartcamtext}
            onPress={() =>
              openInAppBrowser('https://qeridoo.de/zubehoer/smart-system')
            }>
            {'Checkout Qeridoo’s most loved \n product and feature - SmartCam'}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 20,
    borderColor: '#ECF1F1',
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 30,
    marginBottom: 40,
  },
  smartcamtext: {
    textAlign: 'center',
    fontSize: 13,
    fontFamily: FontFamily.robotoLight,
    textDecorationLine: 'underline',
    width: '60%',
    color: BaseColor.primary,
  },
  row: {
    flexDirection: 'row', // Align items horizontally in each row
  },
  item: {
    backgroundColor: '#f9c2ff',
    marginHorizontal: 10,
    marginVertical: 5,
    width: width / 7.5, // Adjust the width as needed
    height: width / 7.5,
    borderRadius: 10,
    marginBottom: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default MemoriesMoment;
