import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Text,
  Animated,
  Image,
  Platform,
  Easing,
} from 'react-native';
import * as Progress from 'react-native-progress';
import {FontFamily} from '../../config/typography';
import BaseColor from '../../config/colors';
import {useSelector} from 'react-redux';
import {getSliderDetail} from '../../utils/commonFunction';

const IOS = Platform.OS === 'ios';
const {width, height} = Dimensions.get('window');
const MultiStepSlider = ({totalSteps = 3}) => {
  const dots = [];
  const {sliderProgress} = useSelector(state => state.modal);
  const animatedProgress = useRef(new Animated.Value(0)).current;

  const startAnimation = () => {
    Animated.timing(animatedProgress, {
      toValue: sliderProgress * 282, // End progress value
      duration: 500, // Duration of the animation in milliseconds
      easing: Easing.linear, // Easing function
      useNativeDriver: false, // This should be false for width animation
    }).start();
  };

  useEffect(() => {
    startAnimation();
    getSliderDetail();
  }, [sliderProgress]);

  for (let i = 0; i < totalSteps; i++) {
    let color;
    if (sliderProgress === 0.3) {
      color = i == 0 ? BaseColor.blackColor : '#6F6F6F';
    } else if (sliderProgress === 0.6) {
      color = i == 0 || i === 1 ? BaseColor.blackColor : '#6F6F6F';
    } else {
      color = BaseColor.blackColor;
    }
    dots.push(
      <View
        key={i}
        style={[styles.dotWrapper, {left: `${(i / totalSteps) * 70}%`}]}>
        <View style={[styles.dot]} />
        <Text style={[styles.dotLabel, {color: color}]}>
          {i === 0 ? 'Sign Up' : i === 1 ? 'Profile Setup' : 'Add Product'}
        </Text>
      </View>,
    );
  }

  const trolleyPosition = useRef(new Animated.Value(0)).current; // Initial trolley position (0%)

  // Animate the trolley based on progress
  useEffect(() => {
    const trollyWidth =
      sliderProgress === 1
        ? width / 1.8
        : sliderProgress === 0.3
        ? width / 3.5
        : width / 2.2;
    Animated.timing(trolleyPosition, {
      toValue: sliderProgress * trollyWidth, // Progress is multiplied by the bar's width minus trolley's width
      duration: 500,
      useNativeDriver: false,
    }).start();
  }, [sliderProgress]);

  return (
    <View style={styles.container}>
      {/* Progress Bar */}
      <View>
        {sliderProgress >= 1 && (
          <Text style={[styles.qeridooTxt]}>
            {'Go Qeridoo!'} <Text> {'- - - - - - - - - - - - >'}</Text>
          </Text>
        )}
        <Animated.View style={[styles.trolley, {left: trolleyPosition}]}>
          <Image
            source={require('../../assets/images/troller.png')}
            style={styles.trolleyImage}
          />
        </Animated.View>
        {sliderProgress < 1 && (
          <View style={styles.flag}>
            <Image
              source={require('../../assets/images/Flag.png')}
              style={styles.trolleyImage}
            />
          </View>
        )}
      </View>
      <View
        style={{
          backgroundColor: '#D9D9D9',
          borderRadius: 30,
        }}>
        <Animated.View
          style={[
            {
              height: 12,
              width: animatedProgress,
            },
          ]}>
          <Progress.Bar
            progress={1}
            height={12}
            width={null}
            color="#87B5B3"
            borderRadius={30}
            borderWidth={0}
            style={{
              borderRadius: 30,
              width: '100%',
            }}
          />
        </Animated.View>
      </View>
      <View style={styles.dotContainer}>{dots}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 20,
    marginHorizontal: 20,
  },
  dotContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    left: 55,
    zIndex: 1,
  },
  dot: {
    position: 'absolute',
    width: 5,
    height: 5,
    borderRadius: 6,
    backgroundColor: '#ffffff',
    top: 3,
  },
  dotWrapper: {
    position: 'absolute',
    alignItems: 'center',
    top: 0,
  },
  dotLabel: {
    marginTop: 25, // Position text under the dots
    fontSize: 10,
    color: '#000000',
    fontFamily: FontFamily.regular,
  },
  trolleyImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  trolley: {
    position: 'absolute',
    bottom: 10, // Adjust to align the trolley with the bar
    width: 60, // Adjust trolley size
    height: 60,
  },
  flag: {
    position: 'absolute',
    bottom: 10, // Adjust to align the trolley with the bar
    width: 50, // Adjust trolley size
    height: 50,
    right: -15,
  },
  qeridooTxt: {
    position: 'absolute',
    bottom: 35, // Adjust to align the trolley with the bar
    width: '100%', // Adjust trolley size
    fontSize: IOS ? 14 : 16,
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
    paddingHorizontal: 10,
  },
});

export default MultiStepSlider;
