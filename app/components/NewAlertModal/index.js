/* eslint-disable quotes */
import {useTheme} from '@react-navigation/native';
import React, {useState} from 'react';
import {
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  Text,
  View,
} from 'react-native';
import {CustomIcon} from '../../config/LoadIcons';
import {FontFamily} from '../../config/typography';
import {translate} from '../../lang/Translate';
import CButton from '../CButton';

const NewAlert = props => {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const {
    visible,
    onRequestClose,
    alertMessage,
    alertName,
    alertTitle,
    onCancelPress = () => {},
    onOkPress = () => {},
    agreeTxt = translate('alertOkBtn'),
    cancelTxt = translate('alertCancelBtn'),
    type = '',
    loader = false,
    messageStyle,
    qeridoo,
  } = props;

  // this function for handle Ok button press
  function handleOk() {
    setTimeout(() => {
      onOkPress();
    }, 200);
  }

  return (
    <Modal
      visible={visible}
      onRequestClose={onRequestClose}
      style={{flex: 1}}
      transparent
      animationType="slide">
      <ScrollView contentContainerStyle={{flexGrow: 1}} bounces={false}>
        <KeyboardAvoidingView
          style={{
            flex: 1,
            overflow: 'hidden',
          }}
          behavior={Platform.OS === 'ios' ? 'height' : null}>
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              backgroundColor: BaseColor.black80,
              alignItems: 'center',
            }}>
            <View
              style={{
                borderRadius: 16,
                backgroundColor: BaseColor.whiteColor,
                padding: 20,
                alignItems: 'center',
                paddingVertical: qeridoo ? 20 : 40,
                width: '90%',
              }}>
              {qeridoo ? null : (
                <View style={{marginVertical: 10}}>
                  <CustomIcon
                    name="warning"
                    size={24}
                    color={BaseColor.error}
                  />
                </View>
              )}
              <Text
                style={{
                  color: BaseColor.error,
                  fontSize: 14,
                  fontFamily: FontFamily.bold,
                  fontWeight: 'bold',
                  textAlign: 'center',
                }}>
                {alertTitle}
              </Text>
              <Text
                style={
                  qeridoo
                    ? messageStyle
                    : {
                        textAlign: 'center',
                        width: '76%',
                        marginTop: 16,
                        fontFamily: FontFamily.bold,
                        color: BaseColor.blackColor,
                      }
                }>
                {alertMessage}
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  marginTop: 25,
                }}>
                <CButton
                  title={agreeTxt}
                  iconBg={BaseColor.whiteColor}
                  style={{width: '45%', marginRight: 20}}
                  loader={loader}
                  titleStyle={{fontFamily: FontFamily.bold, fontSize: 14}}
                  onPress={handleOk}
                />
                <CButton
                  title={cancelTxt}
                  style={{
                    width: '45%',
                    backgroundColor: '#BECACA',
                  }}
                  titleStyle={{fontFamily: FontFamily.bold, fontSize: 14}}
                  onPress={() => {
                    onCancelPress();
                  }}
                />
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </ScrollView>
    </Modal>
  );
};

export default NewAlert;
