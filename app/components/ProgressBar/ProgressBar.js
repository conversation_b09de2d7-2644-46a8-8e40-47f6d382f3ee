import React, { useEffect, useState } from "react";

import * as Progress from "react-native-progress";
import BaseColor from "../../config/colors";

function ProgressBar() {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((oldProgress) => {
        if (oldProgress === 1) {
          return 0;
        }
        const newProgress = oldProgress + 0.1;
        return newProgress > 1 ? 1 : newProgress;
      });
    }, 1000); // change progress every 1 second

    return () => {
      clearInterval(interval);
    };
  }, []);
  return (
    <Progress.Bar color={BaseColor.primary} progress={progress} width={200} />
  );
}

export default ProgressBar;
