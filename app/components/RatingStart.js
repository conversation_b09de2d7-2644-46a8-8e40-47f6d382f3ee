import React, {useEffect, useState} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';

const RatingStar = ({editable, totalRating, iconSize, gap}) => {
  const [rating, setRating] = useState(
    totalRating ? Math.floor(totalRating * 2) / 2 : 0,
  );

  useEffect(() => {
    if (totalRating) {
      setRating(Math.floor(totalRating * 2) / 2);
    }
  }, [totalRating]);
  const handleRatingPress = selectedRating => {
    setRating(selectedRating);
  };

  const renderStars = () => {
    const stars = [];
    const totalStars = 5;

    for (let i = 1; i <= totalStars; i++) {
      const starIconName =
        i <= rating ? 'star' : i - rating <= 0.5 ? 'star-half-o' : 'star-o';

      stars.push(
        <TouchableOpacity
          key={i}
          onPress={() => {
            editable && handleRatingPress(i);
          }}>
          <Icon
            name={starIconName}
            style={{marginLeft: gap ? gap : 4}}
            size={iconSize ? iconSize : 13}
            color="#FFB23F"
          />
        </TouchableOpacity>,
      );
    }

    return stars;
  };

  return <View style={{flexDirection: 'row'}}>{renderStars()}</View>;
};

export default RatingStar;
