import React, {useEffect, useState} from 'react';
import {
  Modal,
  Text,
  TouchableHighlight,
  View,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {FontFamily, FontWeight} from '../../config/typography';
import BaseColor from '../../config/colors';
import CButton from '../CButton';
import {useDispatch, useSelector} from 'react-redux';
import PlaceAction from '../../redux/reducers/place/actions';
import {translate} from '../../lang/Translate';
import TripAction from '../../redux/reducers/trip/actions';
import {SvgXml} from 'react-native-svg';
import commonSvg from '../../assets/commonSvg/commonnSvg';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import CInput from '../CInput';
import DropDownList from '../DropDownList';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {isEmpty} from 'lodash';
import {CustomIcon} from '../../config/LoadIcons';

const {width, height} = Dimensions.get('window');
const CustomModal = ({
  visible,
  setVisible,
  navigation,
  savedTrip,
  tripDetailData,
}) => {
  const [tripInput, setTripInput] = useState('');
  const [loader, setLoader] = useState(false);
  const dispatch = useDispatch();
  const {setPlaceLocation, setTripPlaceName, setPlaceDetails} = PlaceAction;

  const {
    currentTripName,
    currentTripDuration,
    currentTripTravelledDistance,
    currentTripPlaceDetails,
    currentTripId,
  } = useSelector(state => state.trip);
  const userData = useSelector(state => state.auth.userData);
  const {placeTripName} = useSelector(state => state.place);
  const {accessToken} = useSelector(state => state.auth);

  const {setTripRating, setTripReview, setTripResetCumSave} = TripAction;

  const starIcons = [
    {
      id: 1,
      icon: <SvgXml xml={commonSvg.Star4} />,
      iconSelect: <SvgXml xml={commonSvg.Star4Select} />,
      isSelected: false,
      rate: 2,
    },
    {
      id: 2,
      icon: <SvgXml xml={commonSvg.Star5} />,
      iconSelect: <SvgXml xml={commonSvg.Star4Select} />,
      isSelected: false,
      rate: 1,
    },
    {
      id: 3,
      icon: <SvgXml xml={commonSvg.Star3} />,
      iconSelect: <SvgXml xml={commonSvg.Star3Select} />,
      isSelected: false,
      rate: 3,
    },
    {
      id: 4,
      icon: <SvgXml xml={commonSvg.Star2} />,
      iconSelect: <SvgXml xml={commonSvg.Star2Select} />,
      isSelected: false,
      rate: 4,
    },
    {
      id: 5,
      icon: <SvgXml xml={commonSvg.Star1} />,
      iconSelect: <SvgXml xml={commonSvg.Star1Select} />,
      isSelected: false,
      rate: 5,
    },
  ];
  const [inputValue, setInputValue] = useState('');
  const [stars, setStars] = useState(starIcons);
  const [rate, setRate] = useState(null);
  const [tripTitle, setTripTitle] = useState(placeTripName || '');
  const [selectedValue, setSelectedValue] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [tagVal, setTagVal] = useState('');

  const [rateError, setRateError] = useState({error: false, txt: ''});
  const [titleError, setTitleError] = useState({error: false, txt: ''});
  const [descriptionError, setDescriptionError] = useState({
    error: false,
    txt: '',
  });
  const [locationTypeError, setLocationTypeError] = useState({
    error: false,
    txt: '',
  });
  const [categoryTypeError, setCategoryError] = useState({
    error: false,
    txt: '',
  });
  const [tagError, setTagError] = useState({
    error: false,
    txt: '',
  });

  const dropData = [
    {key: 'city', value: 'City'},
    {
      key: 'off_rode',
      value: 'Off road',
    },
    {
      key: 'countryside',
      value: 'Countryside',
    },
    {
      key: 'beach',
      value: 'Beach',
    },
    {
      key: 'coastal_route',
      value: 'Coastal route',
    },
    {
      key: 'lakes',
      value: 'Lakes',
    },
    {
      key: 'forest',
      value: 'Forest',
    },
  ];
  const categoryData = [
    {key: 'Easy', value: 'Easy'},
    {
      key: 'Intermediate',
      value: 'Intermediate',
    },
    {
      key: 'Difficult',
      value: 'Difficult',
    },
  ];

  const validation = () => {
    if (!rate) {
      setRateError({error: true, txt: 'Please select Star'});
    } else if (isEmpty(inputValue)) {
      setDescriptionError({error: true, txt: 'Please enter description'});
    } else if (!tripTitle) {
      setTitleError({error: true, txt: 'Please enter title'});
    } else if (!tagVal) {
      setTagError({error: true, txt: 'Please enter tag'});
    } else if (!selectedValue) {
      setLocationTypeError({error: true, txt: 'Please select location type'});
    } else if (!selectedCategory) {
      setCategoryError({error: true, txt: 'Please select category'});
    } else {
      saveAndEndTrip();
    }
  };

  const handleRating = id => {
    const updatedStars = stars.map(star => {
      if (star.id === id) {
        setRate(star.rate);
        return {...star, isSelected: true};
      } else {
        return {...star, isSelected: false};
      }
    });
    setStars(updatedStars);
  };

  const saveAndEndTrip = async () => {
    setLoader(true);
    const data = {
      trip_id: currentTripId,
      rating: rate,
      description: inputValue || '',
      title: tripTitle || '',
      location_type: selectedValue || '',
      difficulty_category: selectedCategory || '',
      tag: tagVal || '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.reviewTrip,
        'POST',
        data,
        '',
        true,
      );
      if (response.success) {
        if (savedTrip) {
          setVisible(!visible);
          navigation.navigate('TripSaved');
        }
      } else {
        console.log('🚀 ~ response', response);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      console.log('error in trip end ===', error);
      // sendErrorReport(error, 'saving_trip_end');
    }
  };

  useEffect(() => {
    if (tripDetailData) {
      setSelectedValue(tripDetailData?.location_type);
      setSelectedCategory(tripDetailData?.category);
    }
  }, [tripDetailData]);

  const onClose = () => {
    setRateError({error: false, txt: ''});
    setDescriptionError({error: false, txt: ''});
    setTitleError({error: false, txt: ''});
    setLocationTypeError({error: false, txt: ''});
    setCategoryError({error: false, txt: ''});
    setTagError({error: false, txt: ''});
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      close={() => {
        // setVisible(!visible);
      }}
      onRequestClose={() => {
        // setVisible(!visible);
      }}>
      <TouchableHighlight
        activeOpacity={1}
        disabled
        style={{
          flex: 1,
          justifyContent: 'center',
          alignContent: 'center',
          backgroundColor: 'rgba(37, 36, 36, 0.31)',
        }}
        onPress={() => {
          // setVisible(!visible);
          onClose();
        }}>
        <TouchableHighlight disabled activeOpacity={1} style={[styles.body]}>
          <KeyboardAwareScrollView
            bounces={false}
            contentContainerStyle={{
              flexGrow: 1,
              alignItems: 'center',
            }}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            enableOnAndroid={false}>
            <Text style={[styles.label, {}]}>
              Tell us more about this trip!
            </Text>
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: FontFamily.regular,
                  color: '#5B555C',
                  lineHeight: 19,
                }}>
                Leave your star rating here!
              </Text>
              <View style={{flexDirection: 'row', marginTop: 4}}>
                {stars.map(item => {
                  return (
                    <TouchableOpacity
                      onPress={() => handleRating(item.id)}
                      style={{marginLeft: '0.4%'}}
                      key={item.id}>
                      {item.isSelected ? item.iconSelect : item.icon}
                    </TouchableOpacity>
                  );
                })}
              </View>
              {rateError.error ? (
                <View style={{flexDirection: 'row'}}>
                  <View style={{paddingRight: 5}}>
                    <CustomIcon name="info" size={14} color={BaseColor.error} />
                  </View>
                  <Text
                    style={{
                      color: BaseColor.error,
                      fontFamily: FontFamily.regular,
                      marginBottom: 5,
                      fontSize: 14,
                    }}>
                    {rateError.txt}
                  </Text>
                </View>
              ) : null}
              <View style={{width: width / 1.5}}>
                <CInput
                  inputStyle={{
                    fontFamily: FontFamily.regular,
                    fontSize: 12,
                    color: BaseColor.textGrey1,
                  }}
                  placeholder="Type name here"
                  placeholderTextColor={BaseColor.textGrey}
                  value={tripTitle}
                  showError={titleError.error}
                  errorMsg={titleError.txt}
                  onChangeText={val => {
                    setTripTitle(val);
                  }}
                />
              </View>
              <View style={styles.inputWrapper}>
                <TextInput
                  style={styles.input}
                  multiline
                  numberOfLines={4}
                  onChangeText={text => setInputValue(text)}
                  value={inputValue}
                  placeholder="Tell us your experience here..."
                  placeholderTextColor={BaseColor.textGrey}
                />
              </View>
              {descriptionError.error ? (
                <View style={{flexDirection: 'row', marginTop: 3}}>
                  <View style={{paddingRight: 5}}>
                    <CustomIcon name="info" size={14} color={BaseColor.error} />
                  </View>
                  <Text
                    style={{
                      color: BaseColor.error,
                      fontFamily: FontFamily.regular,
                      marginBottom: 5,
                      fontSize: 14,
                    }}>
                    {descriptionError.txt}
                  </Text>
                </View>
              ) : null}
              <View style={{width: width / 1.5}}>
                <CInput
                  inputStyle={{
                    fontFamily: FontFamily.regular,
                    fontSize: 12,
                    color: BaseColor.textGrey1,
                  }}
                  placeholder="Type tag here"
                  placeholderTextColor={BaseColor.textGrey}
                  value={tagVal}
                  showError={tagError.error}
                  errorMsg={tagError.txt}
                  onChangeText={val => {
                    setTagVal(val);
                  }}
                />
              </View>
              <View style={{width: width / 1.5}}>
                <DropDownList
                  dropDownSty={{height: height / 20}}
                  titleText="Location Type"
                  placeholderTxt={translate('placeHolder')}
                  data={dropData}
                  value={selectedValue}
                  labelProp="value"
                  valueProp="key"
                  showError={locationTypeError.error}
                  errorText={locationTypeError.txt}
                  onChange={value => {
                    setSelectedValue(value);
                  }}
                />
              </View>
              <View style={{width: width / 1.5, marginTop: 14}}>
                <DropDownList
                  dropDownSty={{height: height / 20}}
                  titleText="Category"
                  placeholderTxt={translate('placeHolder')}
                  data={categoryData}
                  value={selectedCategory}
                  labelProp="value"
                  valueProp="key"
                  showError={categoryTypeError.error}
                  errorText={categoryTypeError.txt}
                  onChange={value => {
                    setSelectedCategory(value);
                  }}
                />
              </View>
            </View>
            <CButton
              title="Submit"
              style={[styles.btn]}
              iconBg={BaseColor.whiteColor}
              paddingVertical={4}
              titleStyle={styles.btnText}
              onPress={() => {
                onClose();
                validation();
                // setLoader(true);
                // setTimeout(async () => {
                //   if (tripInput) {
                //     navigation.navigate('SearchTrip', {
                //       tripInput,
                //       locationDetail: locationDetail ? locationDetail : null,
                //     });

                //     setTripInput('');
                //   } else {
                //     if (!rate) {
                //       Alert.alert(
                //         'Please give a rating',
                //         'Your rating was not recorded',
                //       );
                //       setLoader(false);
                //       return;
                //     }
                //     onClose();
                //     dispatch(setPlaceLocation({}));
                //     dispatch(setTripPlaceName(''));
                //     dispatch(
                //       setPlaceDetails({
                //         placeTitle: '',
                //         placeRating: '',
                //         placeRatingCount: '',
                //       }),
                //     );
                //     dispatch(setTripRating(rate));
                //     dispatch(setTripReview(inputValue));
                //     dispatch(setTripResetCumSave());
                //     navigation.navigate(translate('home'));
                //     return;
                //   }
                //   setLoader(false);
                // }, 3000);
              }}
              loader={loader}
              // done={done}
              anim
            />
            <Text
              onPress={() => {
                savedTrip && navigation.navigate('TripSaved');
                setVisible(!visible);
              }}
              style={{
                textDecorationLine: 'underline',
                color: BaseColor.primary,
                paddingTop: 10,
                fontSize: 12,
              }}>
              Skip
            </Text>
          </KeyboardAwareScrollView>
        </TouchableHighlight>
      </TouchableHighlight>
    </Modal>
  );
};

const styles = StyleSheet.create({
  body: {
    backgroundColor: BaseColor.whiteColor,
    paddingHorizontal: 30,
    paddingVertical: 20,
    elevation: 10,
    width: '90%',
    justifyContent: 'center',
    alignSelf: 'center',
    shadowColor: BaseColor.transparent,
    borderRadius: 10,
  },

  label: {
    fontSize: 18,
    fontFamily: FontFamily.regular,
    color: '#261E27',
    lineHeight: 32,
  },

  btn: {
    backgroundColor: BaseColor.primary,
    borderRadius: 12,
    marginTop: 32,
    height: 48,
    width: '80%',
  },

  btnText: {
    fontSize: 14,
    fontFamily: FontFamily.bold,
    fontWeight: 'normal',
  },
  inputWrapper: {
    paddingVertical: 2,
    paddingHorizontal: 10,
    width: width / 1.5,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#F6F6F6',
    backgroundColor: '#F6F6F6',
    marginTop: 5,
  },
  input: {
    color: '#908C91',
    fontFamily: FontFamily.regular, // Make sure this font is available in your project
    fontSize: 12,
    lineHeight: 24,
    maxHeight: height / 10,
  },
});

export default CustomModal;
