import React, {useEffect, useState} from 'react';
import {
  Text,
  View,
  StyleSheet,
  FlatList,
  Image,
  Dimensions,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import Toast from 'react-native-simple-toast';
import {flattenDeep} from 'lodash';
import {FontFamily} from '../../config/typography';
import BaseColor from '../../config/colors';
import {Images} from '../../config/Images';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {sendErrorReport} from '../../utils/commonFunction';

const {width, height} = Dimensions.get('window');
const ShopAccessories = ({navigation}) => {
  const [ecomProductList, setEcomProductList] = useState([]);
  const [pageInfo, setPageInfo] = useState('');
  const [page, setPage] = useState(1);
  const [nextPage, setNextPage] = useState(false);
  const [loader, setLoader] = useState(false);

  /** this function for get Product List
   * @function getEComProductList
   * @param {object} data token, per_page, page, category_id
   */
  const getEComProductList = page => {
    setLoader(true);
    const data = {
      collection_id: 636267299160 || '', // ID from Baby Auto Application.
      limit: 50,
      rel: '',
      page_info: '',
      per_page: 50,
      page,
      product_tag: '',
      sort_obj: '',
      platform: 'app',
    };
    getApiData(BaseSetting.endpoints.ecomProductList, 'POST', data, '', true)
      .then(response => {
        if (response.success) {
          const tempPArr =
            page > 1
              ? flattenDeep([ecomProductList, response.data])
              : response.data;
          setEcomProductList(tempPArr);
          if (response.page_info_next) {
            setPageInfo(response.page_info_next);
          }
          if (response?.next_enable === 1) {
            setNextPage(true);
          } else {
            setNextPage(false);
          }
        } else {
          Toast.show(response.message);
        }
        setLoader(false);
      })
      .catch(err => {
        setLoader(false);
        Toast.show('Something went wrong while getting ECOM product list');
        sendErrorReport(err, 'get_product_list');
      });
  };

  // this function is used when list data reached to limit while scrolling
  const onEndReached = () => {
    if (nextPage) {
      const tempPage = page + 1;
      setPage(tempPage);
      getEComProductList(tempPage);
    }
  };

  useEffect(() => {
    getEComProductList(page);
  }, []);

  // this function is used to find discount from product list
  const getDiscount = item => {
    let discount = 0;
    if (item?.variants) {
      item?.variants.map(i => {
        if (i?.compare_at_price !== null) {
          if (i?.compare_at_price > 0) {
            if (i?.inventory_quantity > 0) {
              discount = Math.round(
                (100 * (i?.compare_at_price - i?.price)) / i?.compare_at_price,
              ).toString();
            }
          }
        }
      });
    }
    return discount;
  };

  // get Price
  const getPrice = item => {
    let price = 0;
    if (item?.variants) {
      item?.variants.map(i => {
        if (i?.inventory_quantity > 0) {
          if (i?.price) {
            price = i?.price;
          }
        }
      });
    }
    return price;
  };

  // get Price
  const getCompareAtPrice = item => {
    let cprice = 0;
    if (item?.variants) {
      item?.variants.map(i => {
        if (i?.inventory_quantity > 0) {
          if (i?.compare_at_price) {
            cprice = i?.compare_at_price;
          }
        }
      });
    }
    return cprice;
  };

  const renderItem = ({item, ind}) => {
    return (
      <TouchableOpacity
        activeOpacity={0.7}
        style={styles.container}
        onPress={() => {
          navigation.navigate('ProductDetail', {
            productDetail: item,
            collectionTitle: 'signature',
          });
        }}>
        <View style={styles.imageContainer}>
          <Image
            source={
              item?.image
                ? {uri: item?.image.src}
                : require('../../assets/images/logo.png')
            }
            style={styles.imageStyle}
            resizeMode="cover"
          />
        </View>
        <View style={{paddingTop: 5}}>
          <Text style={styles.titleStyle} numberOfLines={2}>
            {item?.title}
          </Text>
        </View>
        <View
          style={{
            paddingTop: 20,
            paddingHorizontal: 10,
          }}>
          {getCompareAtPrice(item) > 0 ? (
            <Text
              style={{
                color: BaseColor.blackColor,
                fontSize: 12,
                fontFamily: FontFamily.bold,
              }}>
              €{getPrice(item)}
            </Text>
          ) : null}
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Text
              style={{
                color: '#808488',
                textDecorationLine: 'line-through',
                paddingEnd: 5,
                fontSize: 12,
              }}>
              €{getCompareAtPrice(item)}
            </Text>
            {getDiscount(item) !== 0 ? (
              <View
                style={{
                  paddingVertical: 5,
                  paddingHorizontal: 5,
                }}>
                <Text
                  style={{
                    color: '#FE735C',
                    fontFamily: FontFamily.regular,
                    fontSize: 10,
                  }}>
                  {getDiscount(item) !== 0 ? `Save ${getDiscount(item)}%` : ''}
                </Text>
              </View>
            ) : null}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={{marginTop: 15}}>
      <Text style={styles.label}>Shop Accessories </Text>

      <View>
        {loader ? (
          <ActivityIndicator
            style={{
              marginTop: height / 10,
            }}
            size="small"
            color={BaseColor.primary}
          />
        ) : (
          <FlatList
            keyExtractor={(item, index) => index}
            data={ecomProductList}
            renderItem={renderItem}
            horizontal
            contentContainerStyle={{flexGrow: 1, paddingBottom: 24}}
            onEndReachedThreshold={0.4}
            onEndReached={onEndReached}
            showsHorizontalScrollIndicator={false}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: BaseColor.whiteColor,
    height: height / 3.8,
    width: width / 2.5,
    borderRadius: 6,
    marginRight: 15,
    borderWidth: 0.5,
    borderColor: '#D6EBED',
    elevation: 2,
    shadowColor: 'black',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
  },
  imageContainer: {
    width: width / 2.5,
    height: height / 7,
  },
  imageStyle: {
    height: '100%',
    width: '100%',
    borderRadius: 6,
  },
  titleStyle: {
    flexWrap: 'wrap',
    color: BaseColor.blackColor,
    fontFamily: FontFamily.semibold,
    lineHeight: 18,
    paddingHorizontal: 8,
    fontSize: 14,
  },
  label: {
    fontSize: 22,
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    marginBottom: 8,
  },
});

export default ShopAccessories;
