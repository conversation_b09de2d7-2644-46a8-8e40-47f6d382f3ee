import React from 'react';
import {Text, View, StyleSheet, Dimensions} from 'react-native';
import {FontFamily} from '../../config/typography';
import BaseColor from '../../config/colors';
import MultiStepSlider from '../MultiSlider/MultiSlider';
import {useSelector} from 'react-redux';
import {SvgXml} from 'react-native-svg';
import commonSvg from '../../assets/commonSvg/commonnSvg';
import {Image} from 'react-native';
import {CustomIcon} from '../../config/LoadIcons';

const {width, height} = Dimensions.get('window');
const StepComponent = ({navigation}) => {
  const {sliderProgress} = useSelector(state => state.modal);
  const stepCount = sliderProgress >= 1 ? 4 : sliderProgress >= 0.6 ? 3 : 2;
  return (
    <View>
      {stepCount === 4 && <SvgXml xml={commonSvg.sucessSlider} />}
      <View
        style={{
          position: stepCount === 4 ? 'absolute' : 'relative',
          marginHorizontal: 10,
        }}>
        <Text style={styles.label}>
          {`Setup ${stepCount}/4 `}{' '}
          {stepCount === 4 ? (
            <Image
              source={require('../../assets/images/Flag.png')}
              style={{width: 40, height: 40}}
              resizeMode="contain"
            />
          ) : (
            <CustomIcon name="flag" size={25} />
          )}
        </Text>
        <Text
          style={{
            color: '#484444',
            fontSize: 14,
            lineHeight: 20,
            fontFamily: FontFamily.regular,
          }}>
          {stepCount === 4
            ? 'Congratulations on finishing up the setup!'
            : 'Complete setup by connecting your smart device and win a coupon as a first time user.'}
        </Text>
        <View
          style={{
            marginTop: height / 12,
            marginHorizontal: 20,
            marginBottom: 20,
          }}>
          <MultiStepSlider />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  label: {
    fontSize: 22,
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    marginBottom: 8,
  },
});

export default StepComponent;
