/* eslint-disable no-nested-ternary */
import React from 'react';
import {Image, Text, TouchableOpacity, View} from 'react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import {useTheme} from '@react-navigation/native';
import {FontFamily} from '../../config/typography';
import {Images} from '../../config/Images';

const SubHeader = props => {
  const {title, leftIconName, onLeftPress, backBtn, chat} = props;

  const colors = useTheme();
  const BaseColor = colors.colors;

  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        height: 110,
        alignContent: 'center',
        paddingTop: 48,
        marginBottom: 16,
      }}>
      <View
        style={{
          height: 40,
          width: 40,
          borderRadius: 20,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {leftIconName || backBtn ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={onLeftPress}
            style={{
              height: 40,
              width: 40,
              borderRadius: 20,
              justifyContent: 'center',
              alignItems: 'center',
              borderColor:
                leftIconName === 'left-arrow' ? null : BaseColor.textGrey,
            }}>
            {
              <FAIcon
                name="angle-left"
                size={35}
                color={BaseColor.blackColor}
              />
            }
          </TouchableOpacity>
        ) : null}
      </View>
      <View
        style={
          chat && {
            marginLeft: 60,
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
          }
        }>
        {chat && (
          <Image
            source={Images.chatHeader}
            width={20}
            height={20}
            style={{marginRight: 5}}
          />
        )}
        <Text
          style={{
            fontFamily: chat ? FontFamily.regular : FontFamily.bold,
            fontSize: 18,
            letterSpacing: 0.8,
            color: chat ? '#486264' : '#000000',
          }}
          numberOfLines={1}>
          {title}
        </Text>
      </View>
    </View>
  );
};

export default SubHeader;
