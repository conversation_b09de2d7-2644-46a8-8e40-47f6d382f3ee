import React, {Fragment, useEffect, useState} from 'react';
import {
  FlatList,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
} from 'react-native';
import CButton from './CButton';
import BaseColor from '../config/colors';
import {FontFamily} from '../config/typography';
import suggestionCardDummyData from '../assets/dummyCard/dummyCard';
import {Platform} from 'react-native';
import FastImage from 'react-native-fast-image';
import {translate} from '../lang/Translate';
import {Images} from '../config/Images';

//---Sub Main
const IOS = Platform.OS === 'ios';
export const SuggestionCard = ({card, onCardClick, index}) => {
  const [loader, setLoader] = useState(false);
  // Check if it's the first or last item in the list
  const isFirstItem = index === 0;
  return (
    <Fragment key={card.id}>
      <View
        key={card.id}
        style={[styles.suggestionCard, !isFirstItem && {marginLeft: 16}]}>
        <FastImage
          source={
            card?.gpx_image_url ? {uri: card?.gpx_image_url} : Images.mapImag
          }
          style={{
            height: '100%',
            width: '100%',
            borderRadius: 20,
          }}
          resizeMode={FastImage.resizeMode.cover}
        />
        <View style={[styles.cardInfo]}>
          <Text numberOfLines={1} style={[styles.cardInfoTitle]}>
            {card?.title}
          </Text>
          <Text numberOfLines={2} style={[styles.location]}>
            {card?.description}
          </Text>
          <View style={styles.footerCard}>
            <Text style={styles.footerValue}>{card?.distance}</Text>
            <CButton
              title={translate('route')}
              iconBg={BaseColor.whiteColor}
              style={[styles.footerCardBtn]}
              titleStyle={styles.footerCardBtnText}
              loader={loader}
              onPress={() => {
                setTimeout(() => {
                  setLoader(true);
                }, 1000);
                setTimeout(() => {
                  setLoader(false);
                  // setdone(true);
                  onCardClick();
                }, 3000);
              }}
            />
          </View>
        </View>
        {/* <TouchableOpacity
          style={styles.cardHeartContainer}
          onPress={() => onPressLike(card.id)}>
          <CustomIcon
            name="heart-filled"
            size={15}
            color={card.isLike ? BaseColor.red : BaseColor.whiteColor}
          />
        </TouchableOpacity> */}
      </View>
    </Fragment>
  );
};

//------Main-
export const SuggestionFlatList = ({navigation, places, onCardClick}) => {
  const [suggestionCardData, setSuggestionCardData] = useState(
    places ? places : suggestionCardDummyData,
  );

  useEffect(() => {
    if (places) {
      setSuggestionCardData(places);
    }
  });

  // function handleLike(id) {
  //   const cardIndex = suggestionCardData.findIndex(card => card.id === id);

  //   if (cardIndex !== -1) {
  //     suggestionCardData[cardIndex].isLike =
  //       !suggestionCardData[cardIndex].isLike;
  //     setSuggestionCardData([...suggestionCardData]);
  //   }
  // }
  return (
    <FlatList
      data={suggestionCardData}
      keyExtractor={item => item.id.toString()}
      renderItem={({item, index}) => (
        <SuggestionCard
          card={item}
          index={index}
          onCardClick={() => onCardClick(item?.title, item?.id)}
          // onPressLike={() => handleLike(item?.title)}
        />
      )}
      horizontal
      showsHorizontalScrollIndicator={false}
      optimizationFlags={
        Platform.OS === 'android'
          ? ['removeClippedSubviews', 'renderAheadOffset']
          : undefined
      }
    />
  );
};

const styles = StyleSheet.create({
  // suggestion card

  suggestionCard: {
    width: 180,
    height: 240,
    borderRadius: 20,
  },

  cardInfo: {
    zIndex: 1,
    position: 'absolute',
    borderRadius: 16,
    alignSelf: 'center',
    bottom: 10,
    padding: 12,
    width: 164,
    height: 91,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },

  cardInfoTitle: {
    fontSize: 12,
    color: BaseColor.blackColor,
    fontFamily: FontFamily.regular,
  },

  subCardInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    // borderWidth: 1,
  },

  location: {
    marginTop: 5,
    fontSize: 10,
    color: BaseColor.textGrey1,
    fontFamily: FontFamily.regular,
    width: '90%',
    height: 21,
  },

  footerCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: IOS ? 8 : 5,
  },

  footerValue: {
    fontSize: 12,
    fontFamily: FontFamily.bold,
    color: BaseColor.textGrey1,
  },

  footerCardBtn: {
    backgroundColor: BaseColor.primary,
    borderRadius: 8,
    width: '40%',
    height: 30,
  },
  footerCardBtnText: {
    color: BaseColor.whiteColor,
    fontSize: 12,
    fontFamily: FontFamily.bold,
    letterSpacing: 0,
    fontWeight: 'normal',
  },

  cardHeartContainer: {
    position: 'absolute',
    top: 12,
    left: 152,

    elevation: 5,
    shadowColor: 'black',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

SuggestionFlatList;
