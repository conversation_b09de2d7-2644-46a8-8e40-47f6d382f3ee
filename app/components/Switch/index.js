import React, {useEffect, useRef, useState} from 'react';
import {Dimensions, Platform} from 'react-native';
import {Switch} from 'react-native-switch';
import BaseColor from '../../config/colors';

const IOS = Platform.OS === 'ios';
const {width, height} = Dimensions.get('window');
const SwitchComponent = ({onValueChange = () => {}, value}) => {
  return (
    <Switch
      value={value}
      onValueChange={onValueChange}
      circleSize={20}
      barHeight={22}
      circleBorderWidth={1}
      circleBorderActiveColor={'#f4f3f4'}
      circleBorderInactiveColor={'#f4f3f4'}
      backgroundActive={BaseColor.primary}
      backgroundInactive={'#DEDEDD'}
      circleActiveColor={'#f4f3f4'}
      circleInActiveColor={'#f4f3f4'}
      changeValueImmediately={true} // if rendering inside circle, change state immediately or wait for animation to complete
      innerCircleStyle={{
        alignItems: 'center',
        justifyContent: 'center',
      }} // style for inner animated circle for what you (may) be rendering inside the circle
      outerCircleStyle={{}} // style for outer animated circle
      renderActiveText={false}
      renderInActiveText={false}
      switchLeftPx={2.2} // denominator for logic when sliding to TRUE position. Higher number = more space from RIGHT of the circle to END of the slider
      switchRightPx={2.2} // denominator for logic when sliding to FALSE position. Higher number = more space from LEFT of the circle to BEGINNING of the slider
      switchWidthMultiplier={2} // multiplied by the `circleSize` prop to calculate total width of the Switch
      switchBorderRadius={30} // Sets the border Radius of the switch slider. If unset, it remains the circleSize.
    />
  );
};

export default SwitchComponent;
