/* eslint-disable no-nested-ternary */
/* eslint-disable global-require */
/* eslint-disable no-console */
/* eslint-disable array-callback-return */
/* eslint-disable no-plusplus */
/* eslint-disable no-unused-vars */
/* eslint-disable no-fallthrough */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-param-reassign */
/* eslint-disable max-len */
/* eslint-disable quotes */
import React, {useEffect, useState, useRef} from 'react';
import {
  ActivityIndicator,
  Alert,
  AppState,
  Modal,
  NativeEventEmitter,
  NativeModules,
  PermissionsAndroid,
  Platform,
  Text,
  View,
} from 'react-native';
import _, {
  debounce,
  find,
  findIndex,
  flattenDeep,
  isArray,
  isEmpty,
  isObject,
  isString,
  isUndefined,
  map,
  throttle,
  toNumber,
} from 'lodash';
import {useDispatch, useSelector} from 'react-redux';
import moment from 'moment';
// import PushNotification from "react-native-push-notification";
import GetLocation from 'react-native-get-location';
import Toast from 'react-native-simple-toast';
import BleManager from 'react-native-ble-manager';
import BluetoothStateManager from 'react-native-bluetooth-state-manager';
import {bytesToString} from 'convert-string';
import {check, PERMISSIONS, request} from 'react-native-permissions';
// import BackgroundTimer from "react-native-background-timer";
import _BackgroundTimer from 'react-native-background-timer';
import CAlert from '../CAlert';
import BluetoothActions from '../../redux/reducers/bluetooth/actions';
import AuthActions from '../../redux/reducers/auth/actions';
import {translate} from '../../lang/Translate';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {
  getSWValue,
  getUpdatedList,
  sendErrorReport,
} from '../../utils/commonFunction';
import BaseColor from '../../config/colors';

const BleManagerModule = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);

let parseBleData = {};
let interval = null;
/**
 *
 *@module SeatAlerts
 *
 */
const TempAlert = (props, {navigation}) => {
  const peripherals = new Map();
  const navRef = props?.navigation?.current;
  const currentScreen = props?.navigation?.current?.getCurrentRoute();
  const [isReadData, setIsReadData] = useState(true);
  const dispatch = useDispatch();
  const {
    setAlertTime,
    setEmergencyAlert,
    setTempModal,
    setHumdtModal,
    setIsConvert,
    setConnectedDeviceDetails,
    setConnectedDeviceDetail,
    setLeftChildAlert,
    setDeviceDetail,
  } = BluetoothActions;
  const [visible, setVisible] = useState(false);
  const visibleRef = React.useRef(null);
  visibleRef.current = visible;
  const step8Done = useSelector(state => state.auth.step8Done);
  const [alertData, setAlertData] = useState({
    name: '',
    title: '',
    message: '',
    type: '',
  });
  const {
    bleData,

    isBleConnected,
    connectedDeviceDetail,
    connectedDeviceDetails,
    activeChildDetail,
    deviceID,

    serviceID,
    characteristicID,
    isCurrentActiveDevice,
    activeDeviceId,
    deviceDetail,
    isConnecting,
    isConnectedNet,
    qrWifiSSID,
  } = useSelector(state => state.bluetooth);
  const locationDisclouser = useSelector(
    state => state.auth.locationDisclouser,
  );
  const bleDataRef = useRef();
  bleDataRef.current = bleData;
  // console.log("activeChildDetail=======", activeChildDetail);

  const [appState, setAppState] = useState(true);

  const nowTime = moment();

  const accessToken = useSelector(state => state.auth.accessToken);
  const languageData = useSelector(state => state.language);
  const [state, setSWState] = useState({
    key: '',
    value: null,
    isDisCon: true,
    isAutoConnect: '',
  });
  const [loader, setloader] = useState(false);

  useEffect(() => {
    if (isConnectedNet) {
      // getChildInfo();
      // Alert.alert("internet is available now");
      // check new registration flow----------mmmmmm------
      // and screen shot save in local array----mmm---
    }
  }, [isConnectedNet]);
  // this function for add use product
  /** this function for add use product
   * @function addUserProduct
   * @param {object} data child_id, device_id, product_id, service_id, characteristic_id, type, platform
   */
  async function addUserProduct(data, deviceData) {
    const obj = {
      child_id: data?.id,
      device_id: deviceData.id,
      product_id: deviceData.product_id,
      device_data: qrWifiSSID,
      device_ssid: deviceData.device_ssid,
      service_id: serviceID,
      characteristic_id: characteristicID,
      type: 'type 1',
      platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
    };
    console.log('ccccc', data.deviceDetail);
    sendErrorReport(obj, 'add_user_prod_obj');
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.addUserProduct,
        'POST',
        obj,
        headers,
      );
      sendErrorReport(response, 'add_user_prod_response_temp');
      console.log('hgjhghgghhjjhjh-----', response);
      if (response.success) {
        console.log('user pro success');
        sendErrorReport(response, 'add_user_prod_response2_temp');
      }
    } catch (error) {
      console.log('error ==qqqqqqpppopo=', error);
      sendErrorReport(error, 'add_user_prod_temp');
    }
  }

  /** this function for add/update child Profile
   * @function childProfile
   * @param {object} data nick_name, date_of_birth, height, weight, gender, child_profile, emergency_name, emergency_phone, emergency_phone_code, country
   */
  const childProfile = (childData, deviceData) => {
    setloader(true);
    const data = {
      nick_name: childData.nick_name,
      date_of_birth: childData.date_of_birth,
      height: childData.height,
      weight: childData.weight,
      gender: childData.gender,
      emergency_name: childData.conatactname,
      emergency_phone: childData.conatactnumber,
      emergency_phone_second: null,
      emergency_phone_code: null,
      emergency_phone_code_second: null,
      country: childData.country || 'US',
      second_country: childData?.second_country || 'US',
      child_profile: childData.child_profile,
      platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
      lang_code: languageData?.languageData || 'en',
      brand_name: 'Qeridoo',
      app_name: 'qeridoo',
    };

    if (childData?.id) {
      data.child_id = state?.id;
    }

    data.device_id = deviceData.id;

    data.product_id = deviceData.product_id;

    data.device_data = qrWifiSSID;

    data.device_ssid = deviceData.device_ssid;

    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    let url = BaseSetting.endpoints.childProfile;
    if (state?.id) {
      url = BaseSetting.endpoints.updateChild;
    }
    getApiData(url, 'POST', data, headers)
      .then(response => {
        console.log('ressssspppppppp2222', response);
        if (response.success) {
          console.log('ressssspppppppp2222');
          if (isObject(response.data) && !isEmpty(response.data)) {
            dispatch(BluetoothActions.setActiveChildDetail(response.data));
          }
        } else {
          Toast.show(response.message);
        }
        setloader(false);
      })
      .catch(err => {
        console.log('ERRR', err);
        Toast.show('Something went wrong! Unable to save child profile');
        sendErrorReport(err, 'add_update_child_profile_temp');
        setloader(false);
      });
  };
  async function getDeviceList() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    const newObj = {
      type: 'add',
    };

    const data = {
      platform: Platform.OS,
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.connectedDevice,
        'POST',
        data,
        headers,
      );
      sendErrorReport(response, 'response__childinFoList _ list');
      if (response.success && isArray(response.data)) {
        dispatch(setConnectedDeviceDetails(flattenDeep(response.data)));
        dispatch(setConnectedDeviceDetail(flattenDeep(response.data)));
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      console.log('error for device list ===', error);
      sendErrorReport(error, 'get_device_l');
    }
  }

  const getChildInfo = () => {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };
    console.log('getChildInfo -> headers', headers);

    getApiData(
      BaseSetting.endpoints.getUserChild,
      'POST',
      {
        platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
      },
      headers,
    )
      .then(async response => {
        sendErrorReport(response, 'response__childinFo');
        if (response.success) {
          const childArr = response.data;
          console.log('childArrr==temp=1111=', childArr);
          dispatch(setDeviceDetail(response.data));
          if (!isEmpty(childArr)) {
            if (qrWifiSSID !== '') {
              console.log('hjjjhhjhjhjhjjhjjh');
              try {
                const resp = await getApiData(
                  BaseSetting.endpoints.getDevice,
                  'POST',
                  {
                    device_ssid: qrWifiSSID,
                    product_id: qrWifiSSID,
                    lang_code: languageData?.languageData || 'en',
                  },
                  headers,
                );
                console.log('get debiver data===1=', resp);
                if (resp.success && !isEmpty(resp.data)) {
                  console.log('get debiver data====', resp.data);
                  addUserProduct(childArr[0], resp?.data);
                  childProfile(childArr[0], resp?.data);
                } else {
                  Toast.show(response.message);
                }
              } catch (error) {
                console.log('error device detail ===', error);
                sendErrorReport(error, 'on_read_qr');
              }
            }
          }
        } else {
          Toast.show(response.message);
        }
      })
      .catch(err => {
        // console.log("ERRR", err);
        Toast.show('Something went wrong while getting child detail');
        sendErrorReport(err, 'get_child_in_device4');
      });
  };

  let deviceName = '';
  let childName = '';
  Object.keys(isCurrentActiveDevice).map(key => {
    if (state?.key === key) {
      parseBleData = JSON.parse(isCurrentActiveDevice[key]);
      const nData = find(
        connectedDeviceDetail,
        item => item?.product_id === key && item?.connected === 1,
      );
      if (!isUndefined(nData)) {
        deviceName = nData?.device_bluetooth_name;
        childName = nData?.nick_name;
      }
    }
  });

  // const SW = getSWValue(parseBleData, connectedDeviceDetail);

  const retrieveConnected = async deviceId =>
    new Promise(resolve => {
      BleManager.getConnectedPeripherals([]).then(results => {
        console.log(results);
        let matched = false;
        if (results.length === 0) {
          console.log('No connected peripherals');
          matched = false;
        }
        for (let i = 0; i < results.length; i++) {
          const peripheral = results[i];
          if (deviceId === peripheral?.id) {
            sendErrorReport(peripheral, 'read_data_peripheral');
            matched = true;
          }
          // peripheral.connected = true;
          // peripherals.set(peripheral.id, peripheral);
          // setList(Array.from(peripherals.values()));
        }
        resolve(matched);
      });
    });

  // useEffect(() => {
  //   dispatch(AuthActions.setCurrentScreen(currentScreen?.name));
  // }, [currentScreen]);

  useEffect(() => {
    if (deviceID) {
      sendErrorReport(deviceID, 'read_data_deviceID');
      // readData();
    } else {
      if (isBleConnected) {
        dispatch(BluetoothActions.setDeviceID(''));
      }
    }
  }, [deviceID]);

  useEffect(() => {
    if (!locationDisclouser) {
      if (Platform.OS === 'android' && Platform.Version >= 23) {
        console.log('called---4');
        PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ).then(result => {
          if (result) {
            console.log('Permission is OK');
            sendErrorReport(true, 'Fine_location');
          } else {
            PermissionsAndroid.request(
              PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            ).then(res => {
              if (res) {
                console.log('User accept');
              } else {
                console.log('User refuse');
              }
            });
          }
        });
      } else {
        check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE).then(res => {
          if (res !== 'granted') {
            sendErrorReport(true, 'getCurrentLocation5');
            // getCurrentLocation(false, "");
          }
        });
      }
    }
  }, [locationDisclouser]);

  async function getAutoConnect(id) {
    sendErrorReport(id, 'autoConnect_id');

    const findData = connectedDeviceDetail.findIndex(i => i.product_id === id);

    sendErrorReport(
      connectedDeviceDetail[findData],
      'connectedDeviceDetail[findData]',
    );
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.autoConn,
        'POST',
        {
          product_id: connectedDeviceDetail[findData].product_id,
          child_id: connectedDeviceDetail[findData].child_id,
        },
        headers,
      );
      sendErrorReport(response, 'response_autoConnect_childId');

      if (response.success) {
        // getChildInfo();
        // getDeviceList();
      } else {
        // Toast.show("error===>>>", response.message);
      }
    } catch (error) {
      sendErrorReport(error, 'autoConnect');
      console.log('feed post error ===', error);
    }
  }

  useEffect(() => {
    console.log('App ddddd');
    const handleAppStateChange = nextAppState => {
      console.log(`App State: ${nextAppState}`);
      if (nextAppState === 'active') {
        console.log('App State======', true);
        // sendErrorReport(true, "app_state_active");
        setAppState(true);
      } else if (nextAppState === 'background') {
        console.log('App State======', false);
        // sendErrorReport(false, "app_state_background");
        setAppState(false);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => subscription.remove();
  }, []);
  function pushNotification(title, message) {}

  const currentRoute = navRef?.getCurrentRoute()?.name || '';
  const dontShowAlertOn = [
    'SplashScreen',
    'Walkthrough',
    'RedirectLS',
    'Login',
    'Signup',
    'ForgotPassword',
    'Otp',
  ].includes(currentRoute);
  const alertVisible = (visibleRef?.current && !dontShowAlertOn) || false;

  return (
    <>
      <CAlert
        visible={alertVisible}
        // onRequestClose={handleModal}
        // onCancelPress={() => handleModal("cancel")}
        // onOkPress={() => handleModal("ok")}
        type={alertData?.type !== '' ? alertData?.type : 'tempAlert'}
        alertName={alertData?.name}
        alertTitle={alertData?.title}
        alertMessage={alertData?.message}
        agreeTxt={
          alertData.type === 'leftChild'
            ? translate('sendSMS')
            : translate('alertOkBtn')
        }
        cancelTxt={
          alertData.type === 'leftChild'
            ? translate('responsibleAdult')
            : translate('alertCancelBtn')
        }
      />
      {/* <Modal
        visible={loader}
        transparent
        style={{
          flex: 1,
        }}
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: BaseColor.black30,
          }}
        >
          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              justifyContent: "center",
              alignItems: "center",
              padding: 24,
              paddingHorizontal: 32,
              borderRadius: 24,
            }}
          >
            <ActivityIndicator size={24} color={BaseColor.blueDark} />
            <Text
              style={{
                color: BaseColor.blackColor,
                fontWeight: "bold",
                marginTop: 8,
              }}
            >
              Loading
            </Text>
          </View>
        </View>
      </Modal> */}
    </>
  );
};

export default TempAlert;
