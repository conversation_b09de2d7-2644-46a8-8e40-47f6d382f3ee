import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Platform,
  Text,
  TouchableOpacity,
  NativeModules,
  DeviceEventEmitter,
  NativeEventEmitter,
} from 'react-native';
import {FontFamily} from '../../config/typography';
import BaseColor from '../../config/colors';
import {translate} from '../../lang/Translate';
import {SvgFromXml} from 'react-native-svg';
import commonSvg from '../../assets/commonSvg/commonnSvg';
import {CustomIcon} from '../../config/LoadIcons';
import MapView, {MapMarker, PROVIDER_GOOGLE} from 'react-native-maps';
import smartTagMapStyle from '../../config/smartTagMapStyle';
import {useDispatch, useSelector} from 'react-redux';
import {isEmpty, isUndefined} from 'lodash';
import {BluetoothStatus} from 'react-native-bluetooth-status';
import bluetoothActions from '../../redux/reducers/bluetooth/actions';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Toast from 'react-native-simple-toast';

const IOS = Platform.OS === 'ios';
const {width, height} = Dimensions.get('window');
const {NutSdkModule} = NativeModules;
const TrackerConnect = ({navigation}) => {
  const dispatch = useDispatch();
  const [scannedDeviceList, setScannedDeviceList] = useState([]);
  const {
    lastKnownSmartTagLocation,
    isSmartTagDeviceConnected,
    pairedSmartTagDevice,
  } = useSelector(state => state.bluetooth);
  const connectedArr = [
    {
      id: 1,
      name: 'Ring Device',
    },
    {
      id: 2,
      name: 'Unpair Device',
    },
    {
      id: 3,
      name: 'Enable Anti-lost Feature',
    },
  ];
  const [isRing, setisRing] = useState(false);
  const [enableAntiLost, setEnableAntiLost] = useState(true);
  const [batteryLevel, setBatteryLevel] = useState(0);

  const disconnectFromDeviceRemoveFromRedux = async () => {
    try {
      if (pairedSmartTagDevice?.address) {
        const resp = await NutSdkModule.disconnectFromDevice(
          pairedSmartTagDevice?.address,
        );
        dispatch(bluetoothActions.setSmartTagDevice({}));
        dispatch(bluetoothActions.setSmartTagDeviceLocation({}));
        dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
      } else {
        dispatch(bluetoothActions.setSmartTagDevice({}));
      }
    } catch (error) {
      console.error('Error disconnecting from device:', error);
      if (error.message.includes('Device not found with address')) {
        dispatch(bluetoothActions.setSmartTagDevice({}));
      }
    }
  };

  const connectToDevice = async deviceAddress => {
    const isEnabled = await BluetoothStatus.state();
    if (!isEnabled) {
      dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
      return;
    }
    dispatch(bluetoothActions.setSmartTagDeviceConnectionLoading(true));
    if (isUndefined(deviceAddress) || !deviceAddress) {
      navigation.navigate('SmartTag');
      Toast.show('Please select device first');
      return;
    }
    if (!isEmpty(pairedSmartTagDevice)) {
    }
    NutSdkModule.connectToDevice(deviceAddress)
      .then(res => {
        let connectedDeviceInfo = res;
        if (Platform.OS === 'android') {
          connectedDeviceInfo = JSON.parse(res);
        }
        dispatch(
          bluetoothActions.setSmartTagDevice({
            name: connectedDeviceInfo.name,
            customName: 'Qeridoo Smart Tag',
            address: connectedDeviceInfo.address,
            id: connectedDeviceInfo.id,
            product_id: connectedDeviceInfo?.productId,
          }),
        );
        setTimeout(() => {
          dispatch(bluetoothActions.setSmartTagDeviceConnectionLoading(false));
        }, 5000);
        dispatch(bluetoothActions.setSmartTagDeviceConnectionState(true));
      })
      .catch(error => {
        if (
          error.message === 'No devices scanned. Please start scanning first.'
        ) {
          console.warn('Please start scanning before connecting');
          Toast.show('Please Unpair and statrt scanning first');
        } else {
          console.error('Error connecting to device:', error);
          Toast.show(JSON.stringify(error));
        }
        dispatch(bluetoothActions.setSmartTagDeviceConnectionLoading(false));
      });
  };

  //Ring Device
  const ringDevice = async () => {
    setisRing(true);
    const isEnabled = await BluetoothStatus.state();
    if (!isEnabled) {
      dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
      return;
    }
    if (Platform.OS === 'android') {
      NutSdkModule.ringDevice(
        (!isEmpty(pairedSmartTagDevice) && pairedSmartTagDevice.address) ||
          pairedSmartTagDevice?.address,
      )
        .then(() => {
          // console.log("Device ringing...");
        })
        .catch(error => {
          console.error('Error ringing device:', error);
        });
    } else {
      NutSdkModule.beepDevice(pairedSmartTagDevice?.address, true, 30)
        .then(result => {
          // console.log("Beep command successful:", result);
        })
        .catch(error => {
          console.error('Beep command failed:', error);
          if (error.message.includes('Failed to beep device with identifier')) {
            Toast.show('Please Unpair and start scanning first');
          }
        });
    }
  };

  //Stop Ring Device
  const stopRingDevice = async () => {
    setisRing(false);
    const isEnabled = await BluetoothStatus.state();
    if (!isEnabled) {
      dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
      return;
    }
    if (Platform.OS === 'android') {
      NutSdkModule.stopRingingDevice(pairedSmartTagDevice?.address)
        .then(() => {})
        .catch(error => {
          console.error('Error stopping device ringing:', error);
        });
    } else {
      NutSdkModule.beepDevice(pairedSmartTagDevice?.address, false, 30)
        .then(result => {})
        .catch(error => {
          console.error('Beep command failed:', error);
        });
    }
  };

  //read battery level
  const readBatteryLevel = async () => {
    try {
      const isEnabled = await BluetoothStatus.state();
      if (!isEnabled) {
        dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
        return;
      }
      await NutSdkModule.readBattery(pairedSmartTagDevice?.address);
      // console.log("Battery level:");
    } catch (error) {
      console.error('Error reading battery level:', error);
    }
  };

  const handleBatteryChangeEvent = batteryInfo => {
    console.log('Battery Info--->>>', batteryInfo);
    setBatteryLevel(batteryInfo?.battery);
  };

  const antilostFeature = () => {
    if (enableAntiLost && !isEmpty(pairedSmartTagDevice)) {
      // To enable disconnect alert
      NutSdkModule.enableAntiLost(pairedSmartTagDevice?.address, true)
        .then(() => {
          // console.log("Disconnect alert enabled TRUE");
          setEnableAntiLost(false);
        })
        .catch(error =>
          console.error('Failed to enable disconnect alert:', error),
        );
    } else {
      // To disable disconnect alert
      NutSdkModule.enableAntiLost(pairedSmartTagDevice?.address, false)
        .then(() => {
          // console.log("Disconnect alert disabled fALSE");
          setEnableAntiLost(true);
        })
        .catch(error =>
          console.error('Failed to disable disconnect alert:', error),
        );
    }
  };

  useEffect(() => {
    readBatteryLevel();
    // Subscribe to the DeviceConnected event
    const batteryChangeListener = DeviceEventEmitter.addListener(
      'onBatteryChanged',
      handleBatteryChangeEvent,
    );

    // Cleanup function to unsubscribe when the component unmounts
    return () => {
      batteryChangeListener.remove();
    };
  }, []);

  //For iOS only for getting device list..
  useEffect(() => {
    if (Platform.OS === 'ios') {
      // Check if NutSdkModule exists (optional)
      if (NativeModules.NutSdkModule) {
        try {
          const eventEmitter = new NativeEventEmitter(NutSdkModule);
          const deviceDiscoveredListener = eventEmitter.addListener(
            'discoveredDevice',
            deviceData => {
              const existingDeviceIndex =
                scannedDeviceList &&
                scannedDeviceList.findIndex(
                  device => device.device === deviceData.device,
                );

              if (existingDeviceIndex === -1) {
                // Device not found in list
              } else {
                // Optionally update device data if needed (e.g., latest RSSI)
                console.log('Duplicate Device:', deviceData); // Optional logging
              }
            },
          );

          const stateUpdateListener = eventEmitter.addListener(
            'didUpdateState',
            stateData => {
              // console.log('Central Manager State Updated:', stateData);
            },
          );

          const clickListener = eventEmitter.addListener('didClicked', data => {
            // console.log('Device clicked:', data);
          });

          const connectionFailureListener = eventEmitter.addListener(
            'didFailedToConnect',
            data => {
              // console.log('Failed to Connect:', data);
            },
          );

          const batteryUpdateListener = eventEmitter.addListener(
            'didUpdateBattery',
            data => {
              console.log('Battery Updated:', data);
              const {battery} = data;
              setBatteryLevel(battery);
            },
          );

          const rssiUpdateListener = eventEmitter.addListener(
            'didUpdateRSSI',
            data => {
              // console.log('RSSI Updated:', data);
            },
          );

          const connectListener = eventEmitter.addListener(
            'deviceDidConnect',
            data => {
              console.log(
                'Device Connected from Listener connectListner: jŚSSSSSS',
                data,
              );
              if (!isEmpty(pairedSmartTagDevice)) {
                if (data?.device === pairedSmartTagDevice?.address) {
                  dispatch(
                    bluetoothActions.setSmartTagDeviceConnectionState(true),
                  );
                }
              }
            },
          );

          const disconnectListener = eventEmitter.addListener(
            'deviceDidDisconnected',
            data => {
              if (data?.device === pairedSmartTagDevice?.address) {
                dispatch(
                  bluetoothActions.setSmartTagDeviceConnectionState(false),
                );
                NutSdkModule.startScanning(); // Start scanning
              }

              console.log('Device Disconnected:Listener connectListner:', data);
            },
          );

          return () => {
            deviceDiscoveredListener.remove();
            stateUpdateListener.remove();
            clickListener.remove();
            connectionFailureListener.remove();
            batteryUpdateListener.remove();
            rssiUpdateListener.remove();
            connectListener.remove();
            disconnectListener.remove();
          };
        } catch (error) {
          console.error('Error subscribing to event:', error);
        }
      } else {
        console.warn('NutSdkModule not available');
      }
    }
  }, []);

  return (
    <View style={styles.container}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingBottom: 20,
        }}>
        <Text style={styles.titleColor}>{translate('Location')}</Text>
        <Text
          style={{
            color: '#94A1A2',
            fontSize: 13,
            fontFamily: FontFamily.regular,
          }}>{`Battery ${batteryLevel}%`}</Text>
      </View>
      <MapView
        style={{
          height: height / 2.3,
          borderRadius: 10,
        }}
        customMapStyle={smartTagMapStyle}
        provider={PROVIDER_GOOGLE}
        region={{
          latitude: lastKnownSmartTagLocation?.latitude || 0,
          longitude: lastKnownSmartTagLocation?.longitude || 0,
          latitudeDelta: 0.015,
          longitudeDelta: 0.0121,
        }}
        pitchEnabled={false}
        rotateEnabled={false}
        scrollEnabled={true}
        zoomEnabled={true}>
        {isSmartTagDeviceConnected && (
          <MapMarker
            coordinate={{
              latitude: lastKnownSmartTagLocation?.latitude || 0,
              longitude: lastKnownSmartTagLocation?.longitude || 0,
            }}>
            <View>
              {isSmartTagDeviceConnected ? (
                <SvgFromXml
                  xml={commonSvg.SmartTagSvgOn}
                  width={40}
                  height={40}
                />
              ) : (
                <SvgFromXml
                  xml={commonSvg.SmartTagSvg}
                  width={40}
                  height={40}
                />
              )}
            </View>
          </MapMarker>
        )}
      </MapView>
      <View
        style={{
          flex: 1,
          backgroundColor: BaseColor.whiteColor,
          width: '100%',
          borderTopRightRadius: 24,
          borderTopLeftRadius: 24,
          paddingVertical: 20,
        }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}>
          <View>
            <Text
              style={[
                styles.title,
                {
                  fontSize: 20,
                  color: '#5E5F60',
                },
              ]}>
              {'Qeridoo Smart Tag'}
            </Text>
            <Text
              style={{
                color: '#5E5F60',
                fontFamily: FontFamily.regular,
                fontSize: 14,
                paddingVertical: 4,
              }}>
              {lastKnownSmartTagLocation?.latitude
                ? lastKnownSmartTagLocation?.localAddress1
                  ? lastKnownSmartTagLocation?.localAddress1
                  : lastKnownSmartTagLocation?.localAddress2
                  ? lastKnownSmartTagLocation?.localAddress2
                  : lastKnownSmartTagLocation?.localAddress3
                  ? lastKnownSmartTagLocation?.localAddress3
                  : lastKnownSmartTagLocation?.localAddress1 &&
                    lastKnownSmartTagLocation?.localAddress3
                  ? `${lastKnownSmartTagLocation?.localAddress1}, ${lastKnownSmartTagLocation?.localAddress3}`
                  : 'N/A'
                : 'N/A'}
            </Text>
            <Text
              style={{
                color: '#5E5F60',
                fontFamily: FontFamily.regular,
                fontSize: 14,
                paddingVertical: 4,
              }}>
              Status :
              <Text
                style={{
                  color: isSmartTagDeviceConnected
                    ? BaseColor.primary
                    : BaseColor.error,
                  fontFamily: FontFamily.regular,
                }}>
                {isSmartTagDeviceConnected ? ' Connected' : ' Disconnected'}
              </Text>
            </Text>
          </View>
          <View style={{alignSelf: 'center'}}>
            <CustomIcon
              name="maps-global-thick"
              size={24}
              color={
                isSmartTagDeviceConnected
                  ? BaseColor.blackColor
                  : BaseColor.textGrey
              }
            />
          </View>
        </View>
        <View
          style={{
            backgroundColor: BaseColor.whiteColor,
            borderColor: BaseColor.lightgray,
            borderRadius: 12,
            width: '100%',
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Text
              style={{
                fontSize: 16,
                fontFamily: FontFamily.bold,
                paddingVertical: 10,
              }}>
              Controls
            </Text>
            {isRing && (
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => stopRingDevice()}>
                <Text
                  style={{
                    fontSize: 16,
                    fontFamily: FontFamily.regular,
                    color: '#B58787',
                  }}>
                  Stop <Icon name="bell-off-outline" size={16} />
                </Text>
              </TouchableOpacity>
            )}
          </View>
          {!isSmartTagDeviceConnected ? (
            <>
              <TouchableOpacity
                style={styles.controllerDesign}
                onPress={() => {
                  connectToDevice(pairedSmartTagDevice?.address);
                }}>
                <Text style={styles.controllerText}>Connect Device</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.controllerDesign}
                onPress={() => {
                  disconnectFromDeviceRemoveFromRedux();
                }}>
                <Text style={styles.controllerText}>
                  Un-Pair & Remove Device
                </Text>
              </TouchableOpacity>
            </>
          ) : (
            <>
              {connectedArr &&
                connectedArr.map(li => {
                  return (
                    <TouchableOpacity
                      style={[styles.controllerDesign]}
                      onPress={() => {
                        if (li?.id === 1) {
                          ringDevice();
                        } else if (li?.id === 2) {
                          disconnectFromDeviceRemoveFromRedux();
                        } else if (li?.id === 3) {
                          console.log('connect device=-----');
                        }
                      }}>
                      <Text style={styles.controllerText}>{li.name}</Text>
                    </TouchableOpacity>
                  );
                })}
              {Platform.OS === 'android' && (
                <TouchableOpacity
                  style={{
                    backgroundColor: BaseColor.whiteColor,
                    borderWidth: 0.5,
                    borderColor: BaseColor.lightgray,
                    borderRadius: 12,
                    padding: 10,
                    width: '100%',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}
                  onPress={() => {
                    antilostFeature();
                  }}>
                  <Text
                    style={[
                      styles.title,
                      {
                        fontSize: 20,
                        color: BaseColor.blackColor,
                      },
                    ]}>
                    {!enableAntiLost ? 'Disable' : 'Enable'} Anti-Lost Feature
                  </Text>

                  <Icon
                    name="link-variant-remove"
                    size={24}
                    color={BaseColor.blackColor}
                  />
                </TouchableOpacity>
              )}
            </>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginVertical: 20,
  },
  titleColor: {
    fontSize: 22,
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
  },
  controllerDesign: {
    backgroundColor: '#F5F8F8',
    borderWidth: 0.5,
    borderColor: '#E5ECEC',
    borderRadius: 5,
    marginBottom: 10,
  },
  controllerText: {
    fontSize: 14,
    color: '#5E5F60',
    padding: 10,
    fontFamily: FontFamily.regular,
  },
  title: {
    fontFamily: FontFamily.bold,
    color: '#5E5F60',
    fontSize: 22,
    flexShrink: 0,
  },
});

export default TrackerConnect;
