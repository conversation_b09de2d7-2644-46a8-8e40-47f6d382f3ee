import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Platform,
  Text,
  FlatList,
  Image,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import {FontFamily} from '../../config/typography';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {sendErrorReport} from '../../utils/commonFunction';
import {Images} from '../../config/Images';
import BaseColor from '../../config/colors';
import {useIsFocused} from '@react-navigation/native';

const IOS = Platform.OS === 'ios';
const {width, height} = Dimensions.get('window');
const TriphistoryComponent = props => {
  const {onPress = () => {}, type = ''} = props;
  const isFocused = useIsFocused();
  const [loader, setLoader] = useState(false);
  const [tripHistoryList, settripHistoryList] = useState([]);

  const getTripHistory = async () => {
    setLoader(true);
    const url =
      BaseSetting.endpoints.getTripList +
      `?status=${type === 'repeat' ? 'completed' : ''}`;
    try {
      const response = await getApiData(url, 'GET');
      if (response?.success) {
        settripHistoryList(response?.data);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      console.log('error for device list ===', error);
      sendErrorReport(error, 'saving_trip_name');
    }
  };

  useEffect(() => {
    getTripHistory();
  }, [isFocused]);

  //render item for Advertisement list..
  const renderItem = ({item}) => {
    return (
      <TouchableOpacity
        style={[styles.nPCard]}
        activeOpacity={0.8}
        onPress={() => onPress(item?.trip_name, item?.gpx_id)}>
        <View style={[styles.nPCardImage]}>
          <Image
            source={
              item.gpx_image_url ? {uri: item?.gpx_image_url} : Images.mapImag
            }
            style={{
              height: '100%',
              width: '100%',
              borderTopLeftRadius: 14,
              borderBottomLeftRadius: 14,
            }}
          />
        </View>
        <View style={[styles.nPCardInfo]}>
          <Text numberOfLines={1} style={[styles.nPCardInfoTitle]}>
            {item?.trip_name}
          </Text>
          <Text numberOfLines={1} style={[styles.tripText]}>
            {item?.distance} | {item?.converted_time}
          </Text>
          <Text style={styles.yesterdayText}>{item?.formated_date}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {loader ? (
        <ActivityIndicator color={BaseColor.primary} />
      ) : (
        <FlatList
          horizontal
          data={tripHistoryList}
          renderItem={renderItem}
          showsHorizontalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 5,
  },
  nPCard: {
    borderWidth: 1,
    backgroundColor: '#ffffff',
    width: width / 1.8,
    height: IOS ? height / 12 : height / 10,
    borderRadius: 16,
    marginBottom: '3%',
    marginRight: 10,
    flexDirection: 'row',
    borderColor: '#FBFDFD',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.1,
    shadowRadius: 1.84,
  },

  nPCardImage: {
    width: width / 5.5,
    height: IOS ? height / 12 : height / 10,
    borderRadius: 16,
  },

  nPCardInfo: {
    paddingHorizontal: 7,
    paddingVertical: 10,
    width: '65%',
  },
  nPCardInfoTitle: {
    fontSize: 13,
    color: '#486264',
    fontFamily: FontFamily.regular,
    marginVertical: 5,
  },

  nPCardInfoAddress: {
    fontSize: 11,
    fontFamily: FontFamily.regular,
    color: '#B8C9C9',
  },
  tripText: {
    fontSize: 10,
    fontFamily: FontFamily.regular,
    color: '#486264',
    paddingBottom: 8,
    width: width / 4,
  },
  yesterdayText: {
    fontSize: 10,
    color: '#B8C9C9',
    fontFamily: FontFamily.regular,
  },
});

export default TriphistoryComponent;
