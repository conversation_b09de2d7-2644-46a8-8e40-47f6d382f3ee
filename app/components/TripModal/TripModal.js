import React, {useState} from 'react';
import {
  Modal,
  Text,
  TouchableHighlight,
  View,
  TextInput,
  Button,
  StyleSheet,
  Alert,
} from 'react-native';
import {StatusBar} from 'react-native';
import {FontFamily, FontWeight} from '../../config/typography';
import BaseColor from '../../config/colors';
import CInput from '../CInput';
import CButton from '../CButton';
import {TouchableOpacity} from 'react-native-gesture-handler';
import PlaceAction from '../../redux/reducers/place/actions';
import TripAction from '../../redux/reducers/trip/actions';
import {useDispatch, useSelector} from 'react-redux';
import {createUniqueKey} from '../../utils/commonFunction';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';

const CustomModal = ({visible, onClose, navigation, locationDetail}) => {
  const [tripName, setTripName] = useState('');
  const {setTripPlaceName, setPlaceDetails} = PlaceAction;
  // const placeRelatedData = useSelector((state) => state.place);
  const [tripLoader, setTripLoader] = useState(false);
  const {setTripTitle, setTripPlaceDetails, setTripUniqueId, setTripId} =
    TripAction;

  const dispatch = useDispatch();

  // Save Trip Name API Integration
  const createTrip = async () => {
    try {
      setTripLoader(true);
      const data = {
        trip_name: tripName,
        trip_type: 'custom_trip',
      };
      const response = await getApiData(
        BaseSetting.endpoints.saveTripName,
        'POST',
        data,
        '',
        true,
      );
      if (response?.success) {
        dispatch(setTripPlaceName(tripName));
        dispatch(setTripTitle(tripName));
        dispatch(
          setPlaceDetails({
            placeId: locationDetail?.placeId || 'N/A',
            placeTitle: locationDetail?.title || 'N/A',
            placeRating: locationDetail?.rating || 'N/A',
            placeRatingCount: locationDetail?.reviewCount || 'N/A',
          }),
        );
        dispatch(setTripUniqueId(createUniqueKey()));
        dispatch(setTripId(response?.data?.id));
        dispatch(
          setTripPlaceDetails({
            placeName: locationDetail?.title || 'N/A',
            placeRating: locationDetail?.rating || 'N/A',
            placeRatingCount: locationDetail?.reviewCount || 'N/A',
            placeDescription: locationDetail?.about || 'N/A',
            placePhotoURL: locationDetail?.imageSource || null,
            placeReview: locationDetail?.reviews?.length
              ? locationDetail?.reviews
              : [],
            placeWebSite: locationDetail?.website || null,
            placeMyLikeState: locationDetail?.isLike,
            latitude: locationDetail?.latitude || null,
            longitude: locationDetail?.longitude || null,
          }),
        );
        navigation.navigate('SearchTrip', {
          tripName,
          locationDetail: locationDetail ? locationDetail : null,
        });
        setTripName('');
        onClose();
      }
      setTripLoader(false);
    } catch (error) {
      setTripLoader(false);
      console.log('error for device list ===', error);
      sendErrorReport(error, 'saving_trip_name');
    }
  };
  // End Save Trip Name API Integration

  return (
    <Modal animationType="slide" transparent={true} visible={visible}>
      <View
        style={{
          flex: 1,
          height: '100%',

          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(0, 0, 0, 0.2)',
        }}
        onStartShouldSetResponder={() => {
          onClose();
          return true;
        }}>
        <StatusBar backgroundColor={'rgba(0, 0, 0, 0.2)'} translucent />
        <View style={[styles.body]} onStartShouldSetResponder={() => true}>
          <View>
            <Text style={[styles.label, {}]}>
              What would you like to call this trip?
            </Text>
          </View>
          <View>
            <CInput
              textInputWrapper={{
                marginTop: 17,
                backgroundColor: '#F6F6F6',
                borderWidth: 0,
                width: '100%',
                // height: "100%",
                borderRadius: 10,
              }}
              inputStyle={{
                fontFamily: FontFamily.regular,
                fontSize: 14,
                color: BaseColor.textGrey1,
              }}
              placeholder="Type name here"
              placeholderTextColor={BaseColor.textGrey}
              value={tripName}
              onChangeText={val => {
                setTripName(val);
              }}
            />
          </View>
          <View>
            <CButton
              title="Start"
              style={[styles.btn]}
              iconBg={{color: BaseColor.whiteColor}}
              paddingVertical={4}
              titleStyle={styles.btnText}
              onPress={() => {
                if (tripName) {
                  createTrip();
                } else {
                  return;
                }
              }}
              loader={tripLoader}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  body: {
    backgroundColor: BaseColor.whiteColor,
    padding: '8%',
    borderRadius: 20,
  },

  label: {
    fontSize: 16,
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
  },

  btn: {
    backgroundColor: BaseColor.primary,
    borderRadius: 12,
    marginTop: 24,
    // height: 48,
    width: '100%',
  },

  btnText: {
    fontSize: 14,
    fontFamily: FontFamily.bold,
    fontWeight: 'normal',
  },
});

export default CustomModal;
