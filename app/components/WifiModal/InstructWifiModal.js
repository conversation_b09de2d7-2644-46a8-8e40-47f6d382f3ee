import React, { useState } from "react";
import {
  Modal,
  Text,
  TouchableHighlight,
  View,
  TextInput,
  Button,
  StyleSheet,
  Alert,
} from "react-native";
import { StatusBar } from "react-native";
import { FontFamily, FontWeight } from "../../config/typography";
import BaseColor from "../../config/colors";
import CInput from "../CInput";
import CButton from "../CButton";
import { TouchableOpacity } from "react-native-gesture-handler";
import AndroidOpenSettings from "react-native-android-open-settings";

const InstructWifiModal = ({ visible, onClose }) => {
  return (
    <Modal animationType="slide" transparent={true} visible={visible}>
      <View
        style={{
          flex: 1,
          height: "100%",

          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "rgba(0, 0, 0, 0.2)",
        }}
        onStartShouldSetResponder={() => {
          onClose();
          return true;
        }}
      >
        <StatusBar backgroundColor={"rgba(0, 0, 0, 0.2)"} translucent />
        <View style={[styles.body]} onStartShouldSetResponder={() => true}>
          <View>
            <Text style={[styles.label, { lineHeight: 23 }]}>
              Due to Android 10+ privacy updates, apps can’t auto-toggle Wi-Fi.
              Please manually enable Wi-Fi.
            </Text>
          </View>
          <View style={{ marginTop: 10 }}>
            <Text style={{ lineHeight: 23 }}>
              <Text style={{ fontWeight: "bold", color: BaseColor.alertRed }}>
                Step 1 :
              </Text>{" "}
              Please turn on mobile data.
            </Text>
          </View>
          <View style={{ marginTop: 10 }}>
            <Text style={{ lineHeight: 23 }}>
              <Text style={{ fontWeight: "bold", color: BaseColor.alertRed }}>
                Step 2 :
              </Text>{" "}
              Open settings, wifi list, and find your firmware WIFI
              name(WIFI-SSID) i.e{" "}
              <Text style={{ fontWeight: "bold" }}>"QeridooCAMXXXX".</Text>
            </Text>
          </View>

          <View style={{ marginTop: 10 }}>
            <Text style={{ lineHeight: 23 }}>
              <Text style={{ fontWeight: "bold", color: BaseColor.alertRed }}>
                Step 3 :
              </Text>{" "}
              Please join this network.
            </Text>
          </View>
          <View style={{ marginTop: 10 }}>
            <Text style={{ lineHeight: 23 }}>
              <Text style={{ fontWeight: "bold", color: BaseColor.alertRed }}>
                Step 4 :
              </Text>{" "}
              Return and re-scan QR code.
            </Text>
          </View>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              flexWrap: "wrap",
            }}
          >
            <CButton
              title="Close"
              style={[styles.btn]}
              iconBg={{ color: BaseColor.whiteColor }}
              //   paddingVertical={4}
              titleStyle={styles.btnText}
              onPress={() => {
                onClose();
              }}
              //   loader={loader}
              // done={done}
              anim
            />
            <CButton
              title="Connect"
              style={[styles.btn]}
              iconBg={{ color: BaseColor.whiteColor }}
              //   paddingVertical={4}
              titleStyle={styles.btnText}
              onPress={() => {
                if (Platform.OS === "ios") {
                  Linking.openURL("App-Prefs:root=WIFI");
                } else {
                  AndroidOpenSettings.wifiSettings();
                }
                onClose();
              }}
              anim
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  body: {
    backgroundColor: BaseColor.whiteColor,
    padding: "8%",
    borderRadius: 20,
  },

  label: {
    fontSize: 16,
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
  },

  btn: {
    backgroundColor: BaseColor.primary,
    borderRadius: 12,
    marginTop: 24,
    // height: 48,
    width: "40%",
  },

  btnText: {
    fontSize: 14,
    fontFamily: FontFamily.bold,
    fontWeight: "normal",
  },
});

export default InstructWifiModal;
