import {StyleSheet} from 'react-native';

/**
 * Main color use for whole application
 */
const BaseColor = {
  primary: '#87B5B3',
  primary2: '#51CCC7',
  primary3: '#699A98',
  blueLight: '#4AD7E6',
  bLight: '#aae9f0',
  bDelight: '#c6f0f5',
  blueLightTxt: '#4AD7E690',
  blueDark: '#01CBB3',
  orange: '#FF5B30',
  textGrey: '#82828270', // "#7A838E",
  textGrey1: '#828282', // "#7A838E",
  btnBlue: '#61C9D3',
  darkGrey: '#6E6D6D',
  alertRed: '#FF0B1E',
  onBoardBgColor: '#505050',
  homeBgColor: '#494949',
  green: '#4caf50',
  red: '#f44336',
  yellow: '#ffeb3b',
  amber: '#ffc107',
  whiteColor: '#ffffff',
  blackColor: '#000000',
  transparentWhite: '#ffffff45',
  placeHolderColor: '#ffffff99',
  white10: '#ffffff10',
  white20: '#ffffff20',
  white30: '#ffffff30',
  white40: '#ffffff40',
  white50: '#ffffff50',
  white60: '#ffffff60',
  white70: '#ffffff70',
  white80: '#ffffff80',
  white90: '#ffffff90',
  black10: '#00000010',
  black20: '#00000020',
  black30: '#00000030',
  black40: '#00000040',
  black50: '#00000050',
  black60: '#00000060',
  black70: '#00000070',
  black80: '#00000080',
  black90: '#00000090',
  IntroOrng: '#FF5B23',
  tabBackground: '#54ACA1',
  IntroLight: '#ff9571',
  org: '#FF5B30',
  card2: '#F0F0F9',
  ramaGreen: '#01CBB3',
  card1: '#B7B7B7',
  infoView: '#777575', //"#F0F0F0",
  tempSvg: '#FFDFD6',
  lightgray: '#CBCBCB80',
  rightChat: '#F0FFD5',
  typingText: '#898989',
  greyColor: '#5E5F60',
  lightgreen: '#0E2C2B',
  neyonWhite: '#EEF5F5',
  opal: '#B4D1D0',
  coolmint: '#D0DCDB',
  coolmint1: '#E6EEEE54',
  coolmint2: '#DBE2E2',
  darkSlateGrey: '#27504E',
  peacoat: '#232738',
  cadetBlue: '#70A09E',
  antiFlashWhite: '#EAEFEF',
  platinum: '#E0E5E5',
  ashGray: '#ADBCBB',
  mediumGray: '#BDBDBD',
  darkCharcoal: '#333333',
  error: '#D12E34',
  lightPrimary: '#C3D6D6',
  disableInput: '#9E9E9E',
};

export const DarkBaseColor = {
  blueLight: '#e0e0e0',
  blueLightTxt: '#90a4ae90',
  blueDark: '#37474f',
  orange: '#FF5B30',
  textGrey: '#7A838E',
  btnBlue: '#61C9D3',
  alertRed: '#FFced2',
  green: '#4caf50',
  red: '#f44336',
  yellow: '#ffeb3b',
  amber: '#ffc107',
  whiteColor: '#000000',
  blackColor: '#ffffff',
  transparentWhite: '#00000045',
  placeHolderColor: '#00000099',

  white10: '#00000010',
  white20: '#00000020',
  white30: '#00000030',
  white40: '#00000040',
  white50: '#00000050',
  white60: '#00000060',
  white70: '#00000070',
  white80: '#00000080',
  white90: '#00000090',
  black10: '#ffffff10',
  black20: '#ffffff20',
  black30: '#ffffff30',
  black40: '#ffffff40',
  black50: '#ffffff50',
  black60: '#ffffff60',
  black70: '#ffffff70',
  black80: '#ffffff80',
  black90: '#ffffff90',
  greyColor: '#5E5F60',
};

export default BaseColor;

export const BaseStyles = StyleSheet.create({
  shadow: {
    shadowColor: BaseColor.black40,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  roundCorner: {
    borderTopStartRadius: 10,
    borderTopEndRadius: 10,
    borderBottomStartRadius: 10,
    borderBottomEndRadius: 10,
  },
  flexCenter: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF',
  },
});
