/* eslint-disable quotes */
/* eslint-disable no-undef */
const devMode = __DEV__;

// const baseUrl = devMode
//   ? "http://192.168.2.112:8090/"
//   : "https://api.chillbaby-test.io/";
const baseUrl = 'https://api.chillbaby-test.io/';
// const baseUrl = 'http://192.168.1.76:1337/';
const BaseSetting = {
  name: 'Qerido<PERSON>',
  displayName: 'Qeridoo',
  appVersionCode: '1',
  // bugsnagApiKey: "97983f80d92e9c63fa56df79f1252515",
  baseUrl,
  socketUrl: baseUrl,
  api: `${baseUrl}api/`,
  // ? 'http://192.168.0.137/kashtah/kahstahApi/backend/v1/'
  // : `${baseUrl}/api/`,
  shareEndPoint: baseUrl,
  endpoints: {
    signUp: 'user/signup',
    login: 'user/login',
    childProfile: 'user/add-child',
    gender: 'user/update-gender',
    forgotPassword: 'user/forgot-password',
    updatePassword: 'user/update-password',
    otp: 'user/check-otp',
    addToken: 'user/add-token',
    deleteToken: 'delete-token',
    getUserChild: 'getUserChild',
    sendOtp: 'user/send-otp',
    productList: 'getProductDetails',
    sendPackage: 'getTokenByPackage',
    sendNotifi: 'sendUserNotification',
    getPost: 'campaigns/get-post',
    uploadChatAttachment: 'uploadChatAttachment',
    getAppChat: 'chat/get-app-chat',
    insertAttachment: 'insertAttachment',
    getLanguageList: 'getLanguageList',
    getFeed: 'campaigns/add-action',
    addUserProduct: 'addUserProduct',
    updateChild: 'user/update-child',
    getDevice: 'brand_devices/get-device',
    connectedDevice: 'getUserConnectedDevices',
    getFeedPost: 'campaigns/get-feed-post',
    addAction: 'campaigns/add-action',
    getAlerts: 'alerts/get-alerts',
    addAlert: 'alerts/add-alert',
    clearAlerts: 'alerts/clear-alerts',
    notificationStatus: 'user/change-notification-status',
    editDeviceChild: 'editDeviceChild',
    deleteChildDevice: 'deleteChildDevice',
    faqsList: 'brand_faqs/get-list',
    getProductCategories: 'getProductCategories',
    disconnectChildDevice: 'disconnectChildDevice',
    setUserLogActiveTime: 'setUserLogActiveTime',
    sendEmergencyMessageToUser: 'sendEmergencyMessageToUser',
    saveDeviceDataInfo: 'saveDeviceDataInfo',
    saveErrorLog: 'saveErrorLog',
    campaignlike: 'campaigns/campaign-like',
    addproductaction: 'product/add-product-action',
    readNotification: 'readNotification',
    getUserAlertCount: 'getUserAlertCount',
    removeChild: 'user/remove-child',
    autoConn: 'autoConnectChildDevice',
    deleteAccount: 'user/delete-account',
    getDeviceConnectStatus: 'getDeviceConnectStatus',
    userManual: 'brand_devices/user-manual-list',
    updateMessageSendRecord: 'updateMessageSendRecord',
    ecomProductList: 'e-commerce/app-product-list',
    ecomCatalougeList: 'e-commerce/collections-list',
    addEcomProductAction: 'e-commerce/product-action',
    qeridooSaveImage: 'qeridoo/save-image',
    qeridooSaveImageList: 'qeridoo/save-images-list',
    deleteSnapVideo: 'qeridoo/delete-image',
    googleLocation: 'google-location/save-location',
    saveTripName: 'qeridoo/save-trip-name',
    saveTrip: 'qeridoo/save-trip',
    getTripList: 'qeridoo/get-trip-list',
    uploadProfile: 'user/update-profile',
    updateChildProfile: 'qeridoo/update-child',
    getDeviceList: 'manage-trailer/index',
    getConnectedList: 'manage-trailer/user-trailer-list',
    addProductDevice: 'manage-trailer/add-user-product',
    getTrailerDetail: 'manage-trailer/get-trailer-detail',
    stepProcess: 'qeridoo/setup-process',
    getConnectedDetails: 'qeridoo/get-dashboard-detail',
    startTrip: 'qeridoo/start-trip',
    stopTrip: 'qeridoo/stop-trip',
    tripDetails: 'qeridoo/stop-trip-details',
    maintananceinfo: 'manage-maintenance/get-maintenance-detail',
    otherPreference: 'gpx/tag-list',
    gpxLocation: 'gpx/location-list',
    saveTrackDetail: 'qeridoo/save-trip-track-detail',
    gpxMapDetail: 'get-gpx-detail',
    reviewTrip: 'qeridoo/save-trip-review',
    hightLightDetails: 'qeridoo/get-highlight-detail',
    challengesList: 'qeridoo/get-user-challenge-details',
    deleteChallenges: 'qeridoo/remove-challenge',
    removeMaintenance: 'manage-maintenance/remove-maintenance',
    pauseTrip: 'qeridoo/pause-trip',
    pageViewData: 'time-line/save-page-view-data',
  },
};

export default BaseSetting;
