import { useEffect } from "react";
import { DeviceEventEmitter, NativeModules, Platform } from "react-native";

const { NutSdkModule } = NativeModules;

const useBluetoothConstantlyListenState = (callback) => {
  useEffect(() => {
    if(Platform.OS === 'android'){
      NutSdkModule.registerBluetoothStateReceiver();
    }

    const subscription = DeviceEventEmitter.addListener(
      "BluetoothStateChanged",
      (event) => {
        callback(event.isEnabled);
      }
    );

    return () => {
      subscription.remove();
    };
  }, [callback]);
};

export default useBluetoothConstantlyListenState;
