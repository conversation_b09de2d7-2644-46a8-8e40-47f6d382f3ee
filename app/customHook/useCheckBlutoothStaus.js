import {useEffect} from 'react';

const useCheckBluetoothStatus = () => {
  useEffect(() => {
    const checkBluetoothStatus = async () => {
      try {
        const {NutSdkModule} = NativeModules;
        const isEnabled = await NutSdkModule.checkBluetoothStatus();
      } catch (error) {
        console.error('Error checking Bluetooth status:', error);
      }
    };
    checkBluetoothStatus();
  }, []);
};
