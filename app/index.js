import BackgroundGeolocation from 'react-native-background-geolocation';

// Improve the headless task handler
const headlessTask = async event => {
  // Get a reference to the plugin
  const bgGeo = BackgroundGeolocation;

  console.log('[Headless] Event received: ', event);

  // Ensure the plugin is configured
  await bgGeo.ready({
    // Minimal configuration for headless mode
    desiredAccuracy: BackgroundGeolocation.DESIRED_ACCURACY_HIGH,
    distanceFilter: 10,
    stopOnTerminate: false,
    startOnBoot: true,
    enableHeadless: true,
    foregroundService: true,
    debug: true,
    persistMode: BackgroundGeolocation.PERSIST_MODE_ALL,
    maxRecordsToPersist: 1000,
    logLevel: BackgroundGeolocation.LOG_LEVEL_VERBOSE,
  });

  // Handle different event types
  switch (event.name) {
    case 'location':
      // Handle location update
      console.log('[Headless] Location update: ', event.params);
      // Ensure location is persisted
      await bgGeo.insertLocation({
        coords: event.params.coords,
        timestamp: event.params.timestamp,
        uuid: event.params.uuid || Date.now().toString(),
        extras: {headless: true},
      });
      break;
    case 'motionchange':
      // Handle motion state change
      console.log('[Headless] Motion changed: ', event.params.isMoving);
      if (event.params.isMoving) {
        // Device has started moving
        await bgGeo.changePace(true);
        // Force a location update
        await bgGeo.getCurrentPosition({
          samples: 1,
          persist: true,
          extras: {motionchange: true},
        });
      }
      break;
    case 'heartbeat':
      // Handle heartbeat
      console.log('[Headless] Heartbeat received');
      // Ensure we're still tracking
      await bgGeo.getCurrentPosition({
        samples: 1,
        persist: true,
        extras: {heartbeat: true},
      });
      break;
    case 'providerchange':
      // Handle provider change (GPS on/off)
      console.log('[Headless] Provider changed: ', event.params);
      if (event.params.enabled) {
        // Location services were re-enabled, force a location update
        await bgGeo.getCurrentPosition({
          samples: 1,
          persist: true,
          extras: {providerChange: true},
        });
      }
      break;
  }
};

// Register the headless task
BackgroundGeolocation.registerHeadlessTask(headlessTask);
