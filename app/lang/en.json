{"text": "TEXT", "welcomeToBaby": "Welcome to Qeridoo", "functionalComfortable": "Functional and comfortable, but above all safe", "login": "LOGIN", "signup": "Signup", "createAccount": "Create Account", "loginscreen": "<PERSON><PERSON>", "loginUser": "ENTER MOBILE / USER NAME", "loginPassword": "Password", "selectYourCountry": "Country", "enterTheCode": "Enter the code to verify your account.", "loginRemember": "REMEMBER ME", "codeHasBeenSend": "Code has been sent to you email address", "loginBtn": "<PERSON><PERSON>", "loginWithGoggle": "Login with Google", "loginForgot": "Forgot Password?", "loginAccount": "Don't Have An Account?", "loginToSignup": "Register", "forgotScreen": "Forgot Password", "forgotEmail": "No worries! Enter your email address below and we will send you a code to reset password.", "resetPassword": "Reset Password", "forgotInput": "Phone Number", "forgotBtn": "Send Reset Instruction", "otpScreen": "We sent a code", "verifyAccount": "Verify Account", "otpText": "Enter the Code sent to your Phone number and Email", "otpEmail": "Enter the OTP sent to associated Email", "otpBtn": "SUBMIT", "otpResend": "Resend Code", "createNewPassword": "Create New Password", "genderScreen": "SELECT GENDER", "genderFemale": "Female", "genderMale": "Male", "qrScannerScreen": "DEVICES", "qrScannerText": "Using your phones camera, scan your\nunique QR code which can be found on your\nBaby Auto.", "qrScannerLink": "Click here if you are using non SMART product", "connectScreen": "Connected!", "connectSuccessText": "You Have Successfully connected The\nDevice", "childProfileScreen": "Child Profile", "chooseProfileText": "CHOOSE PROFILE", "addNew": "ADD NEW", "tapToAddText": "Tap to add new profile", "infoTitle": "INFO", "nickName": "NICKNAME", "age": "AGE", "fewWordInputText": "FEW WORDS ABOUT ME", "height": "HEIGHT (cm)", "weight": "WEIGHT (kg) ", "devices": "DEVICES", "dashboard": "DASHBOARD", "home": "HOME", "deviceThreeText": "Device Connected", "activeDevice": "Active", "deactivedDevice": "Deactivated", "tapToAddNewProfile": "Tap To Add New Profile", "homeAddSmartDevice": " Add SMART Device", "homeAlertMsg": "The car seat is loose or not connected properly, Please ensure the safety before continuing journey", "homeAlertTitle": "<PERSON> Seat Alert", "alertOkBtn": "OK", "alertCancelBtn": "CANCEL", "homeVideoGuideText": "Top tip for installing a car seat", "settingScreen": "SETTINGS", "tempText": "Temperature setting", "pushText": "Push Notification", "darkmodeText": "Dark Mode", "customerText": "Customer Service", "tempC": "C", "tempF": "F", "modeOn": "On", "modeOff": "Off", "about": "About", "support": "FAQ'S & Support", "privacypolicy": "Privacy Policy", "version": "VERSION 1.0", "alertScreen": "ALERTS", "tempAlert": "High Temperature Alert", "tempAlertText": "11 Min <PERSON><PERSON>", "strollerAlert": "<PERSON>'s Stroller", "strollerText": "Today, 11:22PM", "strollerMsg": "<PERSON>", "babyInSeatAlert": "Baby In Seat Alert", "babyInSeatTime": "2Hr Ago", "babyOutOfSeatAlert": "<PERSON> Out of Seat", "babyOutOfSeatTime": "2Hr Ago", "listenIntoAlert": "Listen Into The Alert", "connectedDeviceName": "<PERSON><PERSON>", "charlie": "<PERSON>", "smartcushion": "SMART cushion", "chatScreenTitle": "<PERSON> - Baby Auto", "chatInputText": "Type An Answer", "fullName": "Full Name", "updatePasswordScreen": "Update Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "logout": "Logout", "emailId": "E-mail", "enterYourEmail": "Enter your Email", "enterYourPassword": "Enter your password", "agreeTo": "Agree to", "termNCondition": "terms & conditions", "whatsNew": "WHAT'S NEW", "dob": "Date of birth", "faqScreen": "FAQ'S & Support", "selectChild": "Select Child", "deleteDevice": "DELETE DEVICE", "changeDevice": "CHANGE DEVICE", "editDevice": "EDIT DEVICE", "assignDevice": "ASSIGN DEVICE", "children": "CHILDREN", "products": "Products", "productsCatalouge": "Product Catalouge", "productDetail": "Product Detail", "technicalDrawing": "Product video", "faqNManuals": "FAQ & Manuals", "agree": "Agree", "ticketClosed": "Your ticket has been closed by service provider", "selectLang": "Select Language", "temperatureAlertTitle": "Enter Low & High Temperature Alert Value", "humidityAlertTitle": "Enter Low & High Humidity Alert Value", "emergencyAlertTitle": "Emergency Alert", "emergencyAlertMessage": "Your child is still in their seat, please ensure they are with a responsible adult", "noAlerts": "No Alerts", "highAlertMsg": "The temperature is high around your child, please ensure they are safe and comfortable.", "highAlertMsg1": "The temperature is high around", "highAlertMsg2": "please ensure they are safe and comfortable.", "lowTempAlertMsg": "The temperature is low around, please ensure they are safe and comfortable.", "lowAlertMsg1": "The temperature is low around", "lowAlertMsg2": "please ensure they are safe and comfortable.", "tempAlertTitle": "Temperature alert", "humdtAlertTitle": "Humidity alert", "batteryAlertTitle": "Battery alert", "batteryLowMsg": "Battery is lower then 25%", "noPosts": "No Posts", "noDevice": "No Devices", "urlError": "Requested URL is not valid", "powerDownTitle": "Power Down", "powerDownMessage": "Powering down", "productDesc": "Product Description", "addDeviceTitle": "ADD DEVICE", "camera": "CAMERA", "scanQRText": "Using your phones camera. Scan your unique QR code which can be found on your Qeridoo Booklet.", "qrLinkText": "Click here if you are using non SMART product", "childLeftInSeatTitle": "{{child_name}} is in Seat", "childInSeatTitle": "<PERSON>", "childLeftInSeatMessage": "is still in their car seat. Please ensure they are with a responsible adult.", "changeSeatToForward": "Due to your child's weight you've entered, please set the child seat to forward facing position", "changeSeatToRearFace": "Due to your child's weight you've entered, please set the child seat to rear facing position", "fanOn": "Fan is on", "fanOff": "Fan is off", "fanTitle": "Fan", "tempTitle": "TEMPERATURE", "humidityTitle": "HUMIDITY", "carSeatText": "STATUS", "noProducts": "No Products", "noProductsConnected": "No products connected", "contactname": "CONTACT NAME", "contactnumber": "CONTACT NUMBER", "emergencycontact": "EMERGENCY CONTACT", "leftSeatTitle": "{{child_name}} left in seat", "leftSeatMessage": "{{child_name}} is out of seat and the system will power down in 30 seconds", "sendSMS": "Send Emergency SMS", "responsibleAdult": "With Responsible Adult", "productConnected": "SMART cushion connected", "noCameraAcces": "No access to camera. To scan code allow us to use your camera in Settings > Privacy > Camera", "openSettings": "Tap to open Settings", "deleteProfile": "Delete Profile", "editProfile": "Edit Profile", "emergencyFeatures": "and messages are enabled for the app.", "emergencySettings": "Settings > Location >  make sure location", "leftEmergency": "Emergency Left in seat", "MyQRcode": "My QR code", "deleteAccount": "Delete account", "delete": "Delete", "child": "Child", "inSeat": "in Seat", "allowPairing": "Allow pairing", "allowPairingText": "Press the button to allow another phone to connect to the cushion.", "introTitle1": "Welcome to Qeridoo", "introTitle2": "Connecting your device", "introTitle3": "Your digital partner", "introText11": "The Qeridoo app works with your Qeridoo SMART products ensuring you get more out of your products!", "introText12": "become the perfect complement for", "introText13": "your baby travels even more safely.", "introText21": "During Setup you will need to locate your unique QR code to scan with your SMART phone which will link to your new SMART Qeridoo products", "introText22": "your personal QR code to scan with", "introText23": "you phone which will link to your", "introText24": "new Qeridoo smart device.", "introText31": "You can use your Qeridoo app to find set up guides, helpful hints and tips, contact our customer service team and even find new and exciting products!", "introText32": "contact our customer service team and", "introText33": "even discover our latest news.", "introText41": "Customize your Qeridoo experience, specifically for you and your little one's adventures. ", "yourchild": "your child", "highHumidity1": "The humidity is high around", "highHumidity2": "please ensure they are safe and comfortable.", "lowHumidity1": "The humidity is low around", "lowHumidity2": "please ensure they are safe and comfortable.", "seatBatteryAlert": "Battery Alert", "seatBatteryAlertMsg": "We recommend changing the battery as it will soon be low", "locationSettingsAlert": "SETTINGS", "enterName": "Please enter FullName", "enterLastName": "Please enter Last Name", "enterPasswrd": "Please enter Password", "passwrdValid": "Password must contain 8-15 characters, 1 x Upper case, 1 x Lower case, 1 x number and 1 x special character such as !,?,&", "enterEmail": "Please enter email", "enterEmailvalid": "Please enter valid Email", "enterPhone": "Please enter Phone number", "enterPhonevalid": "Please enter valid Phone number", "accept": "Please accept Terms and Conditions", "enterOTP": "Enter One Time Password", "enterCode": "Enter Code", "enterNickname": "Please enter Nickname", "selectDOB": "Please Select DOB", "selectHeight": "Please select Height", "selectWeight": "Please select Weight", "enterContactName": "Please enter contact name", "enterContactNumber": "Please enter contact number", "entervalidContactNumber": "Please enter valid contact number", "addImage": "Please Add Profile Picture", "turnOnBle": "Please turn on your bluetooth", "cannotFindDevice": "Can't find any device. Please try again", "connecting": "Connecting...", "childLeftSeatTitle": "{{child_name}} left seat", "childDeleted": "Child record deleted successfully", "sendSMSAlert": "{{child_name}}'s emergency SMS sent", "userManual": "My User manual", "awakeDeviceTxt": "Please ensure cushion is awake and in range", "CONNECTED": "CONNECTED", "dataNotFound": "Data Not Found", "connectBtn": "Connect", "disconnectSeat": "{{child_name}}'s device has disconnected", "deviceDisconnect": "Device disconnect", "locationSettingsTitle": "Allow location and emergency messages", "searchingQRCode": "Searching for QR code...", "readingQRCode": "Reading QR code...", "skip": "<PERSON><PERSON>", "disconnectedFromDevice": "Disconnected from device", "connectToYourBabyAuto": "Connect to device", "taxIncluded": "Taxes included. Shipping costs are calculated on the payment screen.", "maintainPressure": "Push on Qeridoo® cushion to turn on and maintain pressure throughout the set up", "oneLowerCase": "At least one lower case letter", "oneUpperCase": "At least one upper case letter", "oneNumber": "At least one number", "oneSp": "At least one special character", "twLong": "At least eight character", "pswdReq": "Password requirements:", "locationDis": "Qeridoo collects location data to enable sending emergency SMS, handling from smart safety cushion even when the app is closed or not in use.", "clickOn": "Click on ", "addnew": "add new ", "buttonConnectLinko": "button to connect your Qerido®", "childProPopup": "Complete your child profile for the Qerido® and add up to 2 emergency contacts in case of an emergency or child left in car.", "childProPopup2": "Once you have entered all the details press the green tick in the top right hand corner", "step2": "Unzip the cushion cover on your Qerido® to and battery plastic tab", "step3a": "keep the QR code facing you and lean on the Qerido® with your elbow applying pressure to active.", "step3b": "You will need to apply pressure to Qerido® throughout the process until its complete", "step4": "Next you will need to scan the QR code on your cushion", "step4b1": "If QR code scan fails please press the ", "step4b2": " button at the bottom of the page", "step5": "Press next and Swipe down on the screen to show your Qerido® then press connect", "step5b": "If your Qerido® does not appear in the list try troubleshooting options to resolve any issues", "step5c": "Check Bluetooth is switched on by pressing the settings button below", "step5d": "Open up the battery compartment and reposition battery then carefully add battery cover", "step5e": "Apply pressure to Qerido® ensuring a minimum on 2kg to activate the pressure sensor", "step6": "Press next and then the orange tick to confirm", "step7": "Press next and then the green tick in the top right hand corner to assign Q<PERSON><PERSON>® to your child profile", "step8a": "CONGRATULATIONS!", "step8b": "You have successfully connected your Qerido®. You can release the pressure from Qerido®", "step8c": "Click on the highlighted image above to go to the dashboard", "Start": "Start", "Next": "Next", "Settings": "Settings", "Back": "Back", "Troubleshoot": "Troubleshoot", "Finish": "Finish", "stepAllowBT": "to Bluetooth and location when the app requests. You will also need good mobile and data coverage", "allowAccess": "allow access", "please": "Please,", "goodmorning": "Good Morning", "goodafternoon": "Good Afternoon", "goodevening": "Good Evening", "goodnight": "Good Night", "didNotReceiveCode": "Didn't receive code?", "resend": "Resend", "continueAgree": "By continuing, you agree to our", "termsPrivacy": "Terms of Service", "search": "Search", "createTrip": "Create", "whereWouldYouLikeToGo": "Where would you like to go?", "distance": "Distance", "miles": "miles", "preferredLocationType": "Preferred Location Type", "otherPreferences": "Other Preferences", "suggestion": "Suggestions", "nearestPlaces": "Nearest Routes", "nearby": "Near by", "forts": "Forts", "Religious": "Religious", "CityWalk": "City Walk", "Bridges": "Bridges", "lakes": "Lakes", "settings": "Settings", "myAccount": "Edit Account", "savedProfile": "Saved Profile", "myDevices": "My Devices", "smartTag": "Smart Tag", "tripHistory": "Trip History", "firmwareSettingGuide": "Firmware Setting Guide", "preferredLocation": "Preferred Location", "route": "Route", "seeAll": "See All", "getStarted": "Get Started", "signin": "Sign In", "firstName": "First Name", "lastName": "Last Name", "4DigtiCode": "4 Digit Code", "setupProfile": "Setup Profile", "youregister": "You’re now registered!", "addChildProfile": "Let’s add child profile now", "addNewChild": "Add New Profile", "saveChanges": "Save Changes", "confirmPassValid": "Password and Confirm Password Must be the same", "ProductInfo": "Product Info", "GotoHomePage": "Go to HomePage", "DownloadUserManual": "Download User Manual", "TrailerInfo": "Trailer Info", "CameraInfo": "Camera Info", "TrackerInfo": "Tracker Info", "WarrantyCard": "Warranty card", "Packaging": "Packaging", "FrameBody": "Frame / body", "SafetyInstructions": "Safety instructions", "WarrantyContent": "Every Qeridoo has a warranty card with information about the warranty and the serial number. If you cannot find the card, just have a look in the manual.", "PackageContent": "You can find the serial number on a sticker ('article label') on the outer box of your Qeridoo. This is usually located on the smaller side of the box and also contains a small product picture and other product information.", "FrameBodyContent": "The serial number is also labelled on the frame of your product. For the strollers, it is best to look on the frame in the trunk. The position may vary depending on the item.", "SafetyContent": "All our main products come with warning labels. On your stroller, you will usually find this on the inside of the trunk lid. The serial number is also located at the bottom centre of the trunk.", "ChildCam": "Child <PERSON>", "MaintenanceInfo": "Maintenance Info", "MemoriesMoments": "Memories & Moments", "TheQeridooMagazine": "The Qeridoo Magazine", "ReadArticle": "Read Article", "QeridooFeatureFav": "Qeridoo Featured Favourites", "ViewTestReport": "VIEW TEST REPORT", "OffRoad": "Off Road", "CountrySide": "Country Side", "Beach": "Beach", "CoastalRoute": "Coastal Route", "Forest": "Forest", "ToDashboard": "To Dashboard", "Ecommorce": "E-commerce store"}