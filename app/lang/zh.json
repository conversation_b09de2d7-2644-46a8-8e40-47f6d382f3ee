{"text": "文本", "welcomeToBaby": "欢迎来到宝贝汽车", "functionalComfortable": "功能性和舒适性，但最重要的是安全", "login": "登录", "loginWithGoggle": "使用 Google 登录", "signup": "登记", "createAccount": "创建帐户", "loginscreen": "登录", "loginUser": "输入手机/用户名", "loginPassword": "密码", "loginRemember": "记得我", "enterYourEmail": "输入您的电子邮件", "loginBtn": "登录", "loginForgot": "忘记密码", "loginAccount": "没有账户？", "loginToSignup": "报名", "selectYourCountry": "国家", "enterTheCode": "输入验证码以验证您的帐户。", "forgotScreen": "忘记密码？", "forgotEmail": "输入与您的帐户关联的电子邮件。", "forgotInput": "电话号码", "codeHasBeenSend": "验证码已发送至您的电子邮件地址", "forgotBtn": "重启", "verifyAccount": "验证帐户", "otpScreen": "我们发送了一个代码", "otpText": "输入发送到关联电话号码的 OTP", "otpEmail": "输入发送到关联电子邮件的 OTP", "resetPassword": "重置密码", "introText41": "个性化您的宝贝汽车体验", "createNewPassword": "创建新密码", "otpBtn": "提交", "otpResend": "重发", "genderScreen": "选择性别", "genderFemale": "女性", "genderMale": "男性", "enterCode": "输入验证码", "qrScannerScreen": "设备", "qrScannerText": "使用您的手机摄像头，扫描您的\n独特的二维码，可以在您的\n宝贝汽车。", "qrScannerLink": "如果您使用的是非 SMART 产品，请单击此处", "connectScreen": "连接的！", "connectSuccessText": "您已成功连接\n设备", "childProfileScreen": "儿童资料", "chooseProfileText": "选择个人资料", "addNew": "添加新", "tapToAddText": "点击以添加新配置文件", "infoTitle": "信息", "nickName": "昵称", "age": "年龄", "fewWordInputText": "关于我的几句话", "height": "高度（厘米）", "weight": "重量（公斤）", "devices": "设备", "dashboard": "仪表盘", "home": "家", "deviceThreeText": "已连接 个设备", "activeDevice": "积极的", "deactivedDevice": "停用", "tapToAddNewProfile": "点击以添加新配置文件", "homeAddSmartDevice": " 添加智能设备", "homeAlertMsg": "汽车座椅松动或未正确连接，请确保安全后再继续行驶", "homeAlertTitle": "汽车座椅警报", "alertOkBtn": "好的", "alertCancelBtn": "取消", "homeVideoGuideText": "安装汽车座椅的重要提示", "settingScreen": "设置", "tempText": "温度设置", "pushText": "推送通知", "darkmodeText": "黑暗模式", "customerText": "客户服务", "goodmorning": "早上好！", "goodafternoon": "下午好！", "goodevening": "晚上好", "goodnight": "晚安", "tempC": "C", "tempF": "F", "modeOn": "上", "modeOff": "离开", "about": "关于", "support": "常见问题与支持", "privacypolicy": "隐私政策", "version": "版本 1.0", "alertScreen": "警报", "tempAlert": "高温警报", "tempAlertText": "11 分钟前", "strollerAlert": "查理的婴儿车", "strollerText": "今天，晚上 11:22", "strollerMsg": "哭泣警报", "babyInSeatAlert": "婴儿就座警报", "babyInSeatTime": "2小时前", "babyOutOfSeatAlert": "婴儿不在座位上", "babyOutOfSeatTime": "2小时前", "listenIntoAlert": "聆听警报", "connectedDeviceName": "米奇", "charlie": "查理", "smartcushion": "智能坐垫", "chatScreenTitle": "亚历克斯 - 婴儿汽车", "chatInputText": "输入答案", "fullName": "全名", "updatePasswordScreen": "更新密码", "newPassword": "新密码", "confirmPassword": "确认密码", "logout": "登出", "emailId": "电子邮件", "agreeTo": "同意", "termNCondition": "条款与协议", "whatsNew": "什么是新的", "dob": "出生日期", "faqScreen": "常见问题与支持", "selectChild": "选择孩子", "deleteDevice": "删除设备", "changeDevice": "更换设备", "editDevice": "编辑设备", "assignDevice": "分配设备", "children": "孩子们", "products": "产品", "productsCatalouge": "产品目录", "productDetail": "产品信息", "technicalDrawing": "产品视频", "faqNManuals": "常见问题和手册", "agree": "同意", "ticketClosed": "您的工单已被服务提供商关闭", "selectLang": "选择语言", "temperatureAlertTitle": "输入低温和高温警报值", "humidityAlertTitle": "输入低湿度警报值和高湿度警报值", "emergencyAlertTitle": "紧急警报", "emergencyAlertMessage": "您的孩子仍在座位上，请确保他们与负责任的成年人在一起", "noAlerts": "没有警报", "highAlertMsg": "您孩子周围的温度很高，请确保他们安全舒适。", "highAlertMsg1": "周围温度很高", "highAlertMsg2": "请确保他们安全舒适。", "lowTempAlertMsg": "您孩子周围的温度很低，请确保他们安全舒适。", "lowAlertMsg1": "周围温度低", "lowAlertMsg2": "请确保他们安全舒适。", "tempAlertTitle": "温度警报", "humdtAlertTitle": "湿度警报", "batteryAlertTitle": "电池警报", "batteryLowMsg": "电池电量低于 25%", "noPosts": "没有帖子", "noDevice": "没有设备", "urlError": "请求的 URL 无效", "powerDownTitle": "掉电", "powerDownMessage": "掉电", "productDesc": "产品描述", "addDeviceTitle": "添加设备", "camera": "相机", "scanQRText": "使用手机摄像头。扫描您可以在 Baby Auto 上找到的唯一二维码。", "qrLinkText": "如果您使用的是非 SMART 产品，请单击此处", "childLeftInSeatTitle": "儿童座椅", "childInSeatTitle": "儿童座椅", "childLeftInSeatMessage": "您的孩子仍在汽车座椅上。请确保他们与负责任的成年人在一起。", "changeSeatToForward": "由于您输入的孩子的体重，请将儿童座椅设置为面向前方的位置", "changeSeatToRearFace": "由于您输入的孩子的体重，请将儿童座椅设置为面向后的位置", "fanOn": "风扇开着", "fanOff": "风扇关闭", "fanTitle": "扇子", "tempTitle": "温度", "humidityTitle": "湿度", "carSeatText": "地位", "noProducts": "没有产品", "noProductsConnected": "没有连接的产品", "contactname": "联系人姓名", "contactnumber": "联系电话", "emergencycontact": "紧急联系人", "leftSeatTitle": "孩子离开座位", "leftSeatMessage": "孩子不在座位上，系统将在 30 秒内断电", "sendSMS": "发送紧急短信", "responsibleAdult": "与负责任的成年人", "productConnected": "智能坐垫连接，儿童座椅。", "noCameraAcces": "无法访问相机。要扫描代码，我们可以在“设置”>“隐私”>“相机”中使用您的相机", "openSettings": "点按打开设置", "deleteProfile": "删除个人资料", "editProfile": "编辑个人资料", "emergencyFeatures": "和消息已为应用程序启用。", "emergencySettings": "设置 > 位置 > 确定位置", "leftEmergency": "紧急离开座位", "MyQRcode": "我的二维码", "deleteAccount": "删除帐户", "delete": "删除", "child": "孩子", "inSeat": "在座位", "allowPairing": "允许配对", "allowPairingText": "按下按钮以允许另一部手机连接到坐垫。", "introTitle1": "欢迎来到婴儿车", "introTitle2": "连接您的设备", "introTitle3": "您的数字合作伙伴", "introText11": "享受我们的智能产品。他们将", "introText12": "成为完美的补充", "introText13": "您的宝宝旅行更加安全。", "introText21": "在设置过程中，您需要找到", "introText22": "用于扫描的个人二维码", "introText23": "您的电话将链接到您的", "introText24": "新的 Qeridoo 智能设备。", "introText31": "您可以使用 Qeridoo 应用程序", "introText32": "联系我们的客户服务团队和", "introText33": "甚至发现我们的最新消息。", "yourchild": "你的孩子", "highHumidity1": "周围湿度大", "highHumidity2": "请确保他们安全舒适。", "lowHumidity1": "周围湿度低", "lowHumidity2": "请确保他们安全舒适。", "seatBatteryAlert": "电池警报", "seatBatteryAlertMsg": "我们建议更换电池，因为它很快就会变低", "locationSettingsAlert": "设置", "enterName": "请输入全名", "enterLastName": "请输入姓氏", "enterPasswrd": "请输入密码", "passwrdValid": "密码必须包含 8-15 个字符，1 x 大写，1 x 小写，1 x 数字和 1 x 特殊字符，例如 !,?,&", "enterEmail": "请输入邮箱", "enterEmailvalid": "请输入有效邮箱", "enterPhone": "请输入电话号码", "enterPhonevalid": "请输入有效的电话号码", "accept": "请接受条款和条件", "enterOTP": "输入一次性密码", "enterNickname": "请输入昵称", "selectDOB": "请选择出生日期", "selectHeight": "请选择身高", "selectWeight": "请选择重量", "enterContactName": "请输入联系人姓名", "enterContactNumber": "请输入联系电话", "entervalidContactNumber": "请输入有效的联系电话", "addImage": "请添加头像", "turnOnBle": "请打开你的蓝牙", "cannotFindDevice": "找不到任何设备。请再试一次", "connecting": "连接...", "childLeftSeatTitle": "{{child_name}}已离开座位", "childDeleted": "子记录删除成功", "sendSMSAlert": "{{child_name}} 的紧急短信已发送", "userManual": "我的用户手册", "awakeDeviceTxt": "请确保坐垫处于唤醒状态且在范围内", "CONNECTED": "连接的", "dataNotFound": "未找到数据", "connectBtn": "连接", "disconnectSeat": "{{child_name}} 台设备已断开连接", "deviceDisconnect": "设备断开连接", "locationSettingsTitle": "允许位置和紧急消息", "searchingQRCode": "正在搜索二维码...", "readingQRCode": "读取二维码...", "skip": "跳过", "disconnectedFromDevice": "与设备断开连接", "connectToYourBabyAuto": "连接到设备", "taxIncluded": "含税。运费在付款屏幕上计算。", "maintainPressure": "推上 Qeridoo® 缓冲垫以打开并在整个设置过程中保持压力", "oneLowerCase": "至少一个小写字母", "oneUpperCase": "至少有一个大写字母", "oneNumber": "至少一个数字", "oneSp": "至少一个特殊字符", "twLong": "至少八個字符", "pswdReq": "密码要求：", "locationDis": "Qeridoo 收集位置数据以发送紧急短信，即使在应用程序关闭或未使用时也能通过智能安全垫进行处理。", "clickOn": "点击", "addnew": "添新", "buttonConnectLinko": "连接您的 Qeridoo® 的按钮", "childProPopup": "为 Qeridoo® 填写您的孩子资料，并添加最多 2 个紧急联系人，以防出现紧急情况或孩子被遗忘在车内。", "childProPopup2": "输入所有详细信息后，请按右上角的绿色勾号", "step2": "将 Qeridoo® 上的垫套拉到电池塑料标签上", "step3a": "保持二维码面向您，靠在 Qeridoo® 上，肘部施加压力以激活。", "step3b": "您需要在整个过程中对 Qeridoo® 施加压力，直到其完成", "step4": "接下来您需要扫描坐垫上的二维码", "step4b1": "如果二维码扫描失败，请按", "step4b2": " 页面底部的按钮", "step5": "按下一步并在屏幕上向下滑动以显示您的 Qeridoo®，然后按连接", "step5b": "如果您的 Qeridoo® 未出现在列表中，请尝试使用故障排除选项来解决任何问题", "step5c": "按下面的设置按钮检查蓝牙是否已打开", "step5d": "打开电池盒并重新放置电池，然后小心盖上电池盖", "step5e": "向 Qeridoo® 施加压力，确保至少 2kg 以激活压力传感器", "step6": "按下一步，然后按橙色勾号确认", "step7": "按下一步，然后点击右上角的绿色勾号，将 Qeridoo® 分配给您的孩子个人资料", "step8a": "恭喜！", "step8b": "您已成功连接您的 Qeridoo®。您可以通过 Qeridoo® 释放压力", "step8c": "单击上面突出显示的图像以转到仪表板", "Start": "开始", "Next": "下一个", "Settings": "设置", "Back": "后退", "Troubleshoot": "疑难解答", "Finish": "结束", "stepAllowBT": "应用程序请求时连接到蓝牙和位置。您还需要良好的移动和数据覆盖", "allowAccess": "允许访问", "please": "请,", "didNotReceiveCode": "未收到验证码?", "resend": "重新发送", "continueAgree": "继续即表示您同意我们的", "termsPrivacy": "服務條款", "search": "搜索", "createTrip": "创造", "whereWouldYouLikeToGo": "您想去哪儿", "distance": "距离", "miles": "英里", "preferredLocationType": "优先位置类型", "otherPreferences": "其他偏好", "suggestion": "建议", "nearestPlaces": "最近的路线", "nearby": "附近", "forts": "堡垒", "Religious": "宗教", "CityWalk": "城市步行街", "Bridges": "桥梁", "lakes": "湖泊", "settings": "设置", "myAccount": "编辑帐户", "savedProfile": "已保存的配置文件", "myDevices": "我的设备", "smartTag": "智能标签", "tripHistory": "行程历史", "firmwareSettingGuide": "固件设置指南", "preferredLocation": "优先位置", "route": "路线", "seeAll": "查看全部", "getStarted": "开始使用", "enterYourPassword": "輸入您的密碼", "signin": "登入", "firstName": "名", "lastName": "姓", "4DigtiCode": "4 位元代碼", "setupProfile": "設定設定檔", "youregister": "您现在已注册！", "addChildProfile": "现在让我们添加孩子的个人资料", "addNewChild": "添加新的个人资料", "saveChanges": "保存更改", "confirmPassValid": "密码和确认密码必须相同", "ProductInfo": "产品信息", "GotoHomePage": "前往主页", "DownloadUserManual": "下载用户手册", "TrailerInfo": "预告片信息", "CameraInfo": "相机信息", "TrackerInfo": "追踪信息", "WarrantyCard": "保修卡", "Packaging": "包装", "FrameBody": "框架/本体", "SafetyInstructions": "安全须知", "WarrantyContent": "每个 Qeridoo 都有一张保修卡，其中包含有关保修和序列号的信息。如果找不到该卡，只需查看手册即可。", "PackageContent": "您可以在 Qeridoo 外箱上的贴纸（“商品标签”）上找到序列号。它通常位于盒子较小的一侧，还包含小产品图片和其他产品信息。", "FrameBodyContent": "序列号也贴在产品的框架上。对于婴儿车，最好看后备箱内的车架。该位置可能因项目而异。", "SafetyContent": "我们所有的主要产品都带有警告标签。在婴儿车上，您通常会在行李箱盖内侧找到它。序列号也位于行李箱底部中央。", "ChildCam": "儿童摄像头信息", "MaintenanceInfo": "维护信息", "MemoriesMoments": "回忆与瞬间", "TheQeridooMagazine": "Qeridoo 杂志", "ReadArticle": "阅读文章", "QeridooFeatureFav": "Qeridoo 精选收藏", "ViewTestReport": "查看测试报告", "OffRoad": "越野", "CountrySide": "乡村", "Beach": "海滩", "CoastalRoute": "沿海路线", "Forest": "森林", "ToDashboard": "到仪表板", "Ecommorce": "电子商务商店"}