import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Animated,
  Platform,
  Alert,
} from 'react-native';
import Orientation, {
  OrientationLocker,
  PORTRAIT,
  useDeviceOrientationChange,
} from 'react-native-orientation-locker';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import LinearGradient from 'react-native-linear-gradient';
import {useTheme} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import {isEmpty} from 'lodash';
import {CustomIcon} from '../config/LoadIcons';
import {FontFamily} from '../config/typography';
import BaseColors from '../config/colors';
import {translate} from '../lang/Translate';
import BaseSetting from '../config/setting';
import {getApiData} from '../utils/apiHelper';
import {sendErrorReport} from '../utils/commonFunction';
import CAlert from '../components/CAlert';

const IOS = Platform.OS === 'ios';
const TabBar = ({state, descriptors, navigation}) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  useEffect(() => {
    setspring(state.index);
    setObject(state.routes[state.index]);
  }, [state]);
  const {isBleConnected, lastDeviceId} = useSelector(
    bleState => bleState.bluetooth,
  );
  const token = useSelector(state => state.auth.accessToken);
  const [isConnected, setIsConnected] = useState(true);
  const [tempAlert, setTempAlert] = useState({
    type: '',
    bool: false,
    title: '',
    message: '',
  });

  const selectedIndex = state.index;
  // const selectedObject = state.routes[state.index];
  const [selectedObject, setObject] = useState(state.routes[state.index]);

  const [translateValue, setTranslateValue] = useState(new Animated.Value(0));

  const focusedOptions = descriptors[state.routes[state.index].key].options;

  if (focusedOptions.tabBarVisible === false) {
    return null;
  }

  const tabWidth = Dimensions.get('window').width;
  const sTabWidth = tabWidth / 4;

  const setspring = index => {
    const tabWidth = sTabWidth;
    Animated.spring(translateValue, {
      toValue: 1 + index * tabWidth,
      velocity: 10,
      useNativeDriver: true,
    }).start();
  };

  useDeviceOrientationChange(ori => {
    if (ori === 'LANDSCAPE-RIGHT' || ori === 'LANDSCAPE-LEFT') {
      if (state.index === 2) {
        Orientation.unlockAllOrientations();
      }
    } else if (ori === 'UNKNOWN' || ori === 'FACE-UP') {
      // No action needed for these cases
      null;
    } else if (ori === 'PORTRAIT-UPSIDEDOWN') {
      Orientation.lockToPortrait();
    } else {
      // Lock to portrait if not in landscape mode or unknown
      Orientation.lockToPortrait();
    }
  });

  return (
    <>
      <OrientationLocker orientation={PORTRAIT} />
      <View
        style={{
          flexDirection: 'row',
          width: tabWidth,
          height: 56,
          backgroundColor: BaseColors.whiteColor,
          justifyContent: 'center',
          alignItems: 'center',
          alignSelf: 'center',
        }}>
        {state.routes.map((route, index) => {
          const {options} = descriptors[route.key];
          const label =
            options.tabBarLabel !== undefined
              ? options.tabBarLabel
              : options.title !== undefined
              ? options.title
              : route.name;

          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: 1,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          };

          return (
            <View
              key={`tab_${label}`}
              style={{
                backgroundColor: BaseColors.witeColor,
                flex: 1,

                width: sTabWidth,
              }}>
              <TouchableOpacity
                accessibilityRole="button"
                accessibilityState={isFocused ? {selected: true} : {}}
                accessibilityLabel={options.tabBarAccessibilityLabel}
                testID={options.tabBarTestID}
                onPress={() => {
                  setObject(route);
                  setspring(index);
                  onPress();
                }}
                onLongPress={onLongPress}
                style={{
                  flex: 1,
                }}>
                <LinearGradient
                  start={{x: 0, y: 0}}
                  end={{x: 0, y: 1}}
                  colors={[BaseColor.whiteColor, BaseColor.whiteColor]}
                  style={{
                    flex: 1,
                    height: 90,
                    justifyContent: 'space-evenly',
                    alignItems: 'center',
                    borderBottomWidth: IOS ? 5 : 3,
                    borderColor: isFocused
                      ? BaseColor.primary
                      : BaseColors.whiteColor,
                    // overflow: 'hidden',
                  }}>
                  <CustomIcon
                    name={
                      label === `${translate('setting')}`
                        ? 'setting'
                        : label === `${translate('dashboard')}`
                        ? 'cam-dash'
                        : label === `${translate('qeridoo')}`
                        ? 'qeridoo'
                        : 'home'
                    }
                    size={20}
                    color={BaseColor.greyColor}
                  />
                </LinearGradient>
              </TouchableOpacity>
            </View>
          );
        })}
        <Animated.View
          style={{
            position: 'absolute',
            left: -1,
            right: 0,
            bottom: 0,
            top: 5,
            alignItems: 'center',
            justifyContent: 'center',
            width: sTabWidth,
            borderBottomWidth: IOS ? 5 : 3,
            borderColor: true ? BaseColor.primary : BaseColors.whiteColor,
            overflow: 'hidden',
            transform: [{translateX: translateValue}],
          }}>
          <LinearGradient
            start={{x: 0, y: 0}}
            end={{x: 0, y: 1}}
            colors={
              true
                ? [BaseColor.whiteColor, BaseColors.whiteColor]
                : [BaseColor.whiteColor, BaseColor.whiteColor]
            }
            style={{
              width: sTabWidth,
              height: IOS ? 45 : 50,
              borderBottomWidth: IOS ? 5 : 3,
              borderColor: true ? BaseColor.primary : BaseColors.whiteColor,
              justifyContent: 'space-evenly',
              alignItems: 'center',
              overflow: 'hidden',
            }}>
            <CustomIcon
              name={
                selectedObject.name === `${translate('setting')}`
                  ? 'setting'
                  : selectedObject.name === `${translate('dashboard')}`
                  ? 'cam-dash'
                  : selectedObject.name === `${translate('qeridoo')}`
                  ? 'qeridoo'
                  : 'home'
              }
              size={20}
              color={true ? BaseColor.primary : BaseColor.greyColor}
            />
          </LinearGradient>
        </Animated.View>
        <CAlert
          visible={tempAlert.bool} // Only visible if there are no product connected
          type={tempAlert.type}
          onCancelPress={() =>
            setTempAlert({
              type: '',
              bool: false,
              title: '',
              message: '',
            })
          }
          alertTitle={translate(tempAlert.title)}
          alertMessage={translate(tempAlert.message)}
          onOkPress={() =>
            setTempAlert({
              type: '',
              bool: false,
              title: '',
              message: '',
            })
          }
        />
      </View>
    </>
  );
};

export default TabBar;
