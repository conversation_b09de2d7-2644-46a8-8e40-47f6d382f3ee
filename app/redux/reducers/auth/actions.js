const actions = {
  SET_DATA: 'auth/SET_DATA',
  LOGOUT: 'auth/LOGOUT',
  SET_WALKTHROUGH: 'auth/SET_WALKTHROUGH',
  SET_DARKMODE: 'auth/SET_DARKMODE',
  SET_ACCESSSTOKEN: 'auth/SET_ACCESSSTOKEN',
  SET_USERID: 'auth/SET_USERID',
  SET_UUID: 'auth/SET_UUID',
  SET_BASECOLOR: 'auth/SET_BASECOLOR',
  SET_BRANDTOKEN: 'auth/SET_BRANDTOKEN',
  SET_LEAD: 'auth/SET_LEAD',
  SET_NOTIFICATIONSHOW: 'auth/SET_NOTIFICATIONSHOW',
  SET_NOTIFICATIONDATA: 'auth/SET_NOTIFICATIONDATA',
  SET_LANGUAGELIST: 'auth/SET_LANGUAGELIST',
  SET_IS_FARENHEIT: 'auth/SET_IS_FARENHEIT',
  SET_USER_LOCATION: 'auth/SET_USER_LOCATION',
  SET_DEVICESETTING: 'auth/SET_DEVICESETTING',
  SET_NOTI_COUNT: 'auth/SET_NOTI_COUNT',
  SET_CURRENT_SCREEN: 'auth/SET_CURRENT_SCREEN',
  SET_LOCATIONDISCLOUSER: 'auth/SET_LOCATIONDISCLOUSER',
  SET_STEP7: 'auth/SET_STEP7',
  SET_CLOSE_ONBORDING: 'auth/SET_CLOSE_ONBORDING',
  SET_STEP1_DONE: 'auth/SET_STEP1_DONE',
  SET_STEP2_DONE: 'auth/SET_STEP2_DONE',
  SET_STEP3_DONE: 'auth/SET_STEP3_DONE',
  SET_STEP4_DONE: 'auth/SET_STEP4_DONE',
  SET_STEP4B_DONE: 'auth/SET_STEP4B_DONE',
  SET_STEP5_DONE: 'auth/SET_STEP5_DONE',
  SET_STEP5B_DONE: 'auth/SET_STEP5B_DONE',
  SET_STEP5C_DONE: 'auth/SET_STEP5C_DONE',
  SET_STEP5D_DONE: 'auth/SET_STEP5D_DONE',
  SET_STEP5E_DONE: 'auth/SET_STEP5E_DONE',
  SET_STEP5F_DONE: 'auth/SET_STEP5F_DONE',
  SET_STEP6_DONE: 'auth/SET_STEP6_DONE',
  SET_STEP8_DONE: 'auth/SET_STEP8_DONE',
  SET_ONBOARDING_DONE: 'auth/SET_ONBOARDING_DONE',
  SET_ALLOW_BT_DONE: 'auth/SET_ALLOW_BT_DONE',
  SET_LOCAL_ADDRESS: 'auth/SET_LOCAL_ADDRESS',
  SET_USER_LAT_LONG: 'auth/SET_USER_LAT_LONG',

  setWalkthrough: walkthrough => dispatch =>
    dispatch({
      type: actions.SET_WALKTHROUGH,
      walkthrough,
    }),

  setLanguageList: langList => dispatch =>
    dispatch({
      type: actions.SET_LANGUAGELIST,
      langList,
    }),

  setNotificationShow: notificationShow => dispatch =>
    dispatch({
      type: actions.SET_NOTIFICATIONSHOW,
      notificationShow,
    }),

  setDeviceSettingOpen: deviceSetting => dispatch =>
    dispatch({
      type: actions.SET_DEVICESETTING,
      deviceSetting,
    }),
  setLocationDisclouser: locationDisclouser => dispatch =>
    dispatch({
      type: actions.SET_LOCATIONDISCLOUSER,
      locationDisclouser,
    }),

  setNotificationData: notificationData => dispatch =>
    dispatch({
      type: actions.SET_NOTIFICATIONSHOW,
      notificationData,
    }),

  setLeadId: leadId => dispatch =>
    dispatch({
      type: actions.SET_LEAD,
      leadId,
    }),

  setBrandToken: brandToken => dispatch =>
    dispatch({
      type: actions.SET_BRANDTOKEN,
      brandToken,
    }),

  setBaseColor: baseColor => dispatch =>
    dispatch({
      type: actions.SET_BASECOLOR,
      baseColor,
    }),

  setDarkmode: darkmode => dispatch =>
    dispatch({
      type: actions.SET_DARKMODE,
      darkmode,
    }),

  setIsFarenheit: isFarenheit => dispatch =>
    dispatch({
      type: actions.SET_IS_FARENHEIT,
      isFarenheit,
    }),

  setAccessToken: accessToken => dispatch =>
    dispatch({
      type: actions.SET_ACCESSSTOKEN,
      accessToken,
    }),

  setUserId: user_id => dispatch =>
    dispatch({
      type: actions.SET_USERID,
      user_id,
    }),

  setUUid: uuid => dispatch =>
    dispatch({
      type: actions.SET_UUID,
      uuid,
    }),

  setUserData: data => {
    let uData = {};
    if (data !== undefined && data !== null && Object.keys(data).length > 0) {
      uData = data;
    }

    return dispatch =>
      dispatch({
        type: actions.SET_DATA,
        userData: uData,
      });
  },

  setUserLocation: userLocation => dispatch =>
    dispatch({
      type: actions.SET_USER_LOCATION,
      userLocation,
    }),

  setCurrentScreen: currentScreen => dispatch =>
    dispatch({
      type: actions.SET_CURRENT_SCREEN,
      currentScreen,
    }),

  setNotiCount: notificationCount => dispatch =>
    dispatch({
      type: actions.SET_NOTI_COUNT,
      notificationCount,
    }),
  setStep7: step7ChildInfo => dispatch =>
    dispatch({
      type: actions.SET_STEP7,
      step7ChildInfo,
    }),
  setCloseOnbording: closeOnboarding => dispatch =>
    dispatch({
      type: actions.SET_CLOSE_ONBORDING,
      closeOnboarding,
    }),
  setStep1Done: step1Done => dispatch =>
    dispatch({
      type: actions.SET_STEP1_DONE,
      step1Done,
    }),
  setStep2Done: step2Done => dispatch =>
    dispatch({
      type: actions.SET_STEP2_DONE,
      step2Done,
    }),
  setStep3Done: step3Done => dispatch =>
    dispatch({
      type: actions.SET_STEP3_DONE,
      step3Done,
    }),
  setStep4Done: step4Done => dispatch =>
    dispatch({
      type: actions.SET_STEP4_DONE,
      step4Done,
    }),
  setStep4bDone: step4bDone => dispatch =>
    dispatch({
      type: actions.SET_STEP4B_DONE,
      step4bDone,
    }),
  setStep5Done: step5Done => dispatch =>
    dispatch({
      type: actions.SET_STEP5_DONE,
      step5Done,
    }),
  setStep5bDone: step5bDone => dispatch =>
    dispatch({
      type: actions.SET_STEP5B_DONE,
      step5bDone,
    }),
  setStep5cDone: step5cDone => dispatch =>
    dispatch({
      type: actions.SET_STEP5C_DONE,
      step5cDone,
    }),
  setStep5dDone: step5dDone => dispatch =>
    dispatch({
      type: actions.SET_STEP5D_DONE,
      step5dDone,
    }),
  setStep5eDone: step5eDone => dispatch =>
    dispatch({
      type: actions.SET_STEP5E_DONE,
      step5eDone,
    }),
  setStep5fDone: step5fDone => dispatch =>
    dispatch({
      type: actions.SET_STEP5F_DONE,
      step5fDone,
    }),
  setStep6Done: step6Done => dispatch =>
    dispatch({
      type: actions.SET_STEP6_DONE,
      step6Done,
    }),
  setStep8Done: step8Done => dispatch =>
    dispatch({
      type: actions.SET_STEP8_DONE,
      step8Done,
    }),
  setOnboardingDone: onBordingDone => dispatch =>
    dispatch({
      type: actions.SET_ONBOARDING_DONE,
      onBordingDone,
    }),
  setStepAllowBTDone: allowBTDone => dispatch =>
    dispatch({
      type: actions.SET_ALLOW_BT_DONE,
      allowBTDone,
    }),
  logOut: () => dispatch =>
    dispatch({
      type: actions.LOGOUT,
    }),

  setUserLocalAddress: data => {
    let uData = {};
    if (data !== undefined && data !== null && Object.keys(data).length > 0) {
      uData = data;
    }

    return dispatch =>
      dispatch({
        type: actions.SET_LOCAL_ADDRESS,
        userLocalAddress: uData,
      });
  },
  setUserLatLong: data => {
    return dispatch =>
      dispatch({
        type: actions.SET_USER_LAT_LONG,
        userLatLong: data,
      });
  },
};

export default actions;
