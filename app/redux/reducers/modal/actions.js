const actions = {
  SET_FIRST_TIME_LOGIN: 'SET_FIRST_TIME_LOGIN',
  SET_SLIDER_PROGRESS: 'SET_SLIDER_PROGRESS',
  SET_CLEAR_MODAL_REDUX: 'SET_CLEAR_MODAL_REDUX',
  SET_TRAILER_CONNETCED: 'SET_TRAILER_CONNETCED',
  SET_CONNECTED_DEVICE_DETAILS: 'SET_CONNECTED_DEVICE_DETAILS',

  setFirstTimeLogin: firstTimeLogin => dispatch =>
    dispatch({
      type: actions.SET_FIRST_TIME_LOGIN,
      firstTimeLogin,
    }),
  setTrailerConnectedModal: trailerConnectedModal => dispatch =>
    dispatch({
      type: actions.SET_TRAILER_CONNETCED,
      trailerConnectedModal,
    }),
  setSliderProgress: sliderProgress => dispatch =>
    dispatch({
      type: actions.SET_SLIDER_PROGRESS,
      sliderProgress,
    }),
  setConnectedDeviceList: connectedDeviceList => dispatch =>
    dispatch({
      type: actions.SET_CONNECTED_DEVICE_DETAILS,
      connectedDeviceList,
    }),
  setClearModalRedux: () => dispatch =>
    dispatch({
      type: actions.SET_CLEAR_MODAL_REDUX,
    }),
};

export default actions;
