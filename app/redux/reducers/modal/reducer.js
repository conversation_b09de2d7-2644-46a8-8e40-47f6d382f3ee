import types from './actions';
// import actions from './actions';

const initialState = {
  firstTimeLogin: null,
  sliderProgress: 0.3,
  trailerConnectedModal: null,
  connectedDeviceList: {},
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case types.SET_FIRST_TIME_LOGIN:
      return {
        ...state,
        firstTimeLogin: action.firstTimeLogin,
      };
    case types.SET_SLIDER_PROGRESS:
      return {
        ...state,
        sliderProgress: action.sliderProgress,
      };
    case types.SET_TRAILER_CONNETCED:
      return {
        ...state,
        trailerConnectedModal: action.trailerConnectedModal,
      };
    case types.SET_CONNECTED_DEVICE_DETAILS:
      return {
        ...state,
        connectedDeviceList: action.connectedDeviceList,
      };
    case types.SET_CLEAR_MODAL_REDUX:
      return {
        firstTimeLogin: null,
        sliderProgress: 0.3,
        connectedDeviceList: {},
        trailerConnectedModal: null,
      };
    default:
      return state;
  }
}
