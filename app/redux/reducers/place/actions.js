const actions = {
  SET_PLACE_COORD: 'SET_PLACE_COORD',
  SET_TRIP_NAME: 'SET_TRIP_NAME',
  SET_TIMER_STATE: 'SET_TIMER_STATE',
  SET_TIMER_VALUE: 'SET_TIMER_VALUE',
  SET_PLACE_DETAILS: 'SET_PLACE_DETAILS',
  SET_TRIP_DISTANCE: 'SET_TRIP_DISTANCE',
  SET_DIRECT_TRIP_START: 'SET_DIRECT_TRIP_START',
  SET_PAUSE_TRIP: 'SET_PAUSE_TRIP',
  SET_CLEAR_TRIP_DATA: 'SET_CLEAR_TRIP_DATA',

  setPlaceLocation: placeLocation => dispatch =>
    dispatch({
      type: actions.SET_PLACE_COORD,
      placeLocation,
    }),
  setTripPlaceName: tripName => dispatch =>
    dispatch({
      type: actions.SET_TRIP_NAME,
      tripName,
    }),
  setTimerState: state => dispatch =>
    dispatch({
      type: actions.SET_TIMER_STATE,
      state,
    }),
  setTripTimerValue: state => dispatch =>
    dispatch({
      type: actions.SET_TIMER_VALUE,
      state,
    }),
  setPlaceDetails: state => dispatch =>
    dispatch({
      type: actions.SET_PLACE_DETAILS,
      state,
    }),
  setTripDistance: state => dispatch =>
    dispatch({
      type: actions.SET_TRIP_DISTANCE,
      state,
    }),
  setDirectTripStart: state => dispatch =>
    dispatch({
      type: actions.SET_DIRECT_TRIP_START,
      state,
    }),
  setIsPause: state => dispatch =>
    dispatch({
      type: actions.SET_PAUSE_TRIP,
      state,
    }),
  setClearTripData: () => dispatch =>
    dispatch({
      type: actions.SET_CLEAR_TRIP_DATA,
    }),
};

export default actions;
