import types from './actions';
const initialState = {
  placeLocation: {},
  placeTripName: '',
  tripTime: 0,
  isTimerStart: false,
  placeTitle: '',
  placeRating: 0,
  placeRatingCount: 0,
  distanceKm: 0,
  tripDistance: 0,
  directTripStart: false,
  isPause: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case types.SET_PLACE_COORD:
      return {
        ...state,
        placeLocation: action.placeLocation,
      };
    case types.SET_TRIP_NAME:
      return {
        ...state,
        placeTripName: action.tripName,
      };
    case types.SET_TIMER_STATE:
      return {
        ...state,
        isTimerStart: action.state,
      };
    case types.SET_TIMER_VALUE:
      return {
        ...state,
        tripTime: action.state,
      };
    case types.SET_TRIP_DISTANCE:
      return {
        ...state,
        tripDistance: action.state,
      };
    case types.SET_DIRECT_TRIP_START:
      return {
        ...state,
        directTripStart: action.state,
      };
    case types.SET_PAUSE_TRIP:
      return {
        ...state,
        isPause: action.state,
      };
    case types.SET_CLEAR_TRIP_DATA:
      return {
        ...state,
        placeLocation: {},
        placeTripName: '',
        tripTime: 0,
        isTimerStart: false,
        placeTitle: '',
        placeRating: 0,
        placeRatingCount: 0,
        distanceKm: 0,
        tripDistance: 0,
        directTripStart: false,
        isPause: false,
      };
    case types.SET_PLACE_DETAILS:
      const {placeTitle, placeRating, placeRatingCount, distanceKm} =
        action.state;
      return {
        ...state,
        placeTitle,
        placeRating,
        placeRatingCount,
        distanceKm,
      };
    default:
      return state;
  }
}
