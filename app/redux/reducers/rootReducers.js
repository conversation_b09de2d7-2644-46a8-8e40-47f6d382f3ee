import {combineReducers} from 'redux';
import auth from './auth/reducer';
import language from './language/reducer';
import socket from './socket/reducer';
import bluetooth from './bluetooth/reducer';
import place from './place/reducer';
import timer from './timer/reducer';
import trip from './trip/reducer';
import modal from './modal/reducer';

const rootReducer = combineReducers({
  auth,
  language,
  socket,
  bluetooth,
  place,
  timer,
  trip,
  modal,
});

export default rootReducer;
