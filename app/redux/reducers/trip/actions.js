const actions = {
  SET_TRIP_NAME: 'trip/SET_TRIP_NAME',
  SET_TRIP_UNIQUE_ID: 'trip/SET_TRIP_UNIQUE_ID',
  SET_TRIP_DURATION: 'trip/SET_TRIP_DURATION',
  SET_PLACE_DETAILS: 'trip/SET_PLACE_DETAILS',
  SET_PLACE_COORD: 'trip/SET_PLACE_COORD',
  SET_DISTANCE_TRAVELLED: 'trip/SET_DISTANCE_TRAVELLED',
  SET_TRIP_RATING: 'trip/SET_TRIP_RATING',
  SET_TRIP_REVIEW: 'trip/SET_TRIP_REVIEW',
  RESET_CURRENT_TRIP: 'trip/RESET_CURRENT_TRIP',
  SET_SYNC_WITH_SERVER: 'trip/SET_SYNC_WITH_SERVER',
  SET_TRIP_START_DATE: 'trip/SET_TRIP_START_DATE',
  SET_TRIP_ID: 'trip/SET_TRIP_ID',

  setPlaceCoordinate: coord => dispatch =>
    dispatch({
      type: actions.SET_PLACE_COORD,
      coord,
    }),

  setTripTitle: tripName => dispatch =>
    dispatch({
      type: actions.SET_TRIP_NAME,
      tripName,
    }),

  setTripDuration: timeTaken => dispatch =>
    dispatch({
      type: actions.SET_TRIP_DURATION,
      timeTaken,
    }),

  setTripPlaceDetails: details => dispatch =>
    dispatch({
      type: actions.SET_PLACE_DETAILS,
      details,
    }),
  setDistanceTravelled: distance => dispatch =>
    dispatch({
      type: actions.SET_DISTANCE_TRAVELLED,
      distance,
    }),

  setTripRating: rate => dispatch =>
    dispatch({
      type: actions.SET_TRIP_RATING,
      rate,
    }),

  setTripReview: review => dispatch =>
    dispatch({
      type: actions.SET_TRIP_REVIEW,
      review,
    }),

  setTripResetCumSave: () => dispatch =>
    dispatch({
      type: actions.RESET_CURRENT_TRIP,
    }),

  setTripUniqueId: id => dispatch =>
    dispatch({
      type: actions.SET_TRIP_UNIQUE_ID,
      id,
    }),

  setSyncWithServer: data => dispatch =>
    dispatch({
      type: actions.SET_SYNC_WITH_SERVER,
      data,
    }),

  setTripStartDate: date => dispatch =>
    dispatch({
      type: actions.SET_TRIP_START_DATE,
      date,
    }),

  setTripId: id => dispatch =>
    dispatch({
      type: actions.SET_TRIP_ID,
      id,
    }),
};

export default actions;
