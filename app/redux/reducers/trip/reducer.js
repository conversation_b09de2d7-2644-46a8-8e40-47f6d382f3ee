import types from './actions';

const initialState = {
  tripsData: [],
  currentTripUniqueId: null,
  currentTripName: '',
  currentTripDuration: 0,
  currentTripTravelledDistance: 0,
  currentTripPlaceDetails: {},
  currentTripPlaceCoord: {},
  currentTripRating: 0,
  currentTripReview: '',
  currentTripStartDate: '',
  currentTripId: '',
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case types.SET_PLACE_COORD:
      return {
        ...state,
        currentTripPlaceCoord: action.coord,
      };

    case types.SET_TRIP_NAME:
      return {
        ...state,
        currentTripName: action.tripName,
      };

    case types.SET_PLACE_DETAILS:
      return {
        ...state,
        currentTripPlaceDetails: action.details,
      };

    case types.SET_TRIP_DURATION:
      return {
        ...state,
        currentTripDuration: action.timeTaken,
      };

    case types.SET_DISTANCE_TRAVELLED:
      return {
        ...state,
        currentTripTravelledDistance: action.distance,
      };

    case types.SET_TRIP_RATING:
      return {
        ...state,
        currentTripRating: action.rate,
      };

    case types.SET_TRIP_REVIEW:
      return {
        ...state,
        currentTripReview: action.review,
      };

    case types.SET_TRIP_UNIQUE_ID:
      return {
        ...state,
        currentTripUniqueId: action.id,
      };

    case types.RESET_CURRENT_TRIP:
      return {
        ...state,
        tripsData: [
          ...state.tripsData,
          {
            uniqueId: state.currentTripUniqueId,
            name: state.currentTripName,
            duration: state.currentTripDuration,
            travelledDistance: state.currentTripTravelledDistance,
            placeDetails: state.currentTripPlaceDetails,
            placeCoord: state.currentTripPlaceCoord,
            rating: state.currentTripRating,
            review: state.currentTripReview,
          },
        ],
        currentTripName: '',
        currentTripDuration: 0,
        currentTripTravelledDistance: 0,
        currentTripPlaceDetails: {},
        currentTripPlaceCoord: {},
        currentTripRating: 0,
        currentTripReview: '',
        currentTripUniqueId: null,
      };

    case types.SET_SYNC_WITH_SERVER:
      return {
        ...state,
        tripsData: action.data,
      };
    case types.SET_TRIP_START_DATE:
      return {
        ...state,
        currentTripStartDate: action.date,
      };

    case types.SET_TRIP_ID:
      return {
        ...state,
        currentTripId: action.id,
      };
    default:
      return state;
  }
}
