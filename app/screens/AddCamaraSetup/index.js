import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  BackHandler,
  Text,
  Modal,
} from 'react-native';
import {RNCamera} from 'react-native-camera';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import MapView from 'react-native-maps';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';
import {CustomIcon} from '../../config/LoadIcons';
let backPressed = 0;
const {width, height} = Dimensions.get('window');

const AddCameraSetup = ({navigation}) => {
  const cameraRef = useRef(null);
  const [showCameraBig, setShowCameraBig] = useState(true);
  const [capturedImage, setCapturedImage] = useState(null);
  const [introModal, setIntroModal] = useState(true);

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      navigation.goBack();
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  // Function to handle the swap between camera and map
  const toggleView = () => {
    console.log('comes to toggel---------');
    setShowCameraBig(!showCameraBig);
  };

  // Function to capture the image
  const takePicture = async () => {
    if (cameraRef?.current) {
      try {
        const options = {quality: 0.5, base64: true}; // Add more options if needed
        const data = await cameraRef.current.takePictureAsync(options);
        console.log('Image data:', data); // Log the image data
        setCapturedImage(data.uri); // Set the captured image URI
      } catch (error) {
        console.error('Error capturing image:', error);
      }
    }
  };

  return (
    <View style={styles.container}>
      <View
        style={{
          marginTop: getStatusBarHeight() + 20,
          height: height / 2,
        }}>
        {showCameraBig ? (
          <TouchableOpacity style={styles.bigBox} onPress={() => toggleView()}>
            <RNCamera
              saveToPhotoAlbum
              type={RNCamera.Constants.Type.back}
              style={styles.flexBox}
              ref={cameraRef}
            />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity style={styles.bigBox} onPress={() => toggleView()}>
            <MapView style={styles.flexBox} />
          </TouchableOpacity>
        )}

        {showCameraBig ? (
          <TouchableOpacity
            style={styles.smallBox}
            onPress={() => toggleView()}>
            <MapView style={styles.flexBox} />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={styles.smallBox}
            onPress={() => toggleView()}>
            <RNCamera
              saveToPhotoAlbum
              type={RNCamera.Constants.Type.back}
              style={styles.flexBox}
              ref={cameraRef}
            />
          </TouchableOpacity>
        )}
        <TouchableOpacity
          onPress={() => takePicture()}
          style={{
            width: 50,
            height: 50,
            borderRadius: 30,
            position: 'absolute',
            backgroundColor: 'white',
            alignItems: 'center',
            justifyContent: 'center',
            bottom: 20,
            left: 20,
          }}>
          <View
            style={{
              backgroundColor: '#FB3D3D',
              borderRadius: 30,
              justifyContent: 'center',
              alignItems: 'center',
              width: 36,
              height: 36,
            }}>
            <CustomIcon
              name="add-image"
              color={BaseColor.whiteColor}
              size={15}
            />
          </View>
        </TouchableOpacity>
      </View>
      <View style={[styles.portraitValueContainer]}>
        <View style={{alignItems: 'center'}}>
          <Text style={[styles.placeHolder]}>Distance (Km)</Text>
          <Text style={styles.values}>{'-'}</Text>
        </View>
        <View style={{alignItems: 'center'}}>
          <Text style={[styles.placeHolder]}>IR Value</Text>
          <Text style={styles.values}>{'-'}</Text>
        </View>
        <View style={{alignItems: 'center'}}>
          <Text style={[styles.placeHolder]}>Time (minutes)</Text>
          <Text style={styles.values}>{'-'}</Text>
        </View>
      </View>
      <Text
        style={{fontSize: 14, fontFamily: FontFamily.regular, marginTop: 10}}>
        Saved Snapshots
      </Text>
      <Modal
        visible={introModal}
        transparent
        style={{
          flex: 1,
        }}>
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => {
            setIntroModal(false);
          }}
          style={{
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.83)',
          }}>
          <View>
            <View
              style={{
                position: 'absolute',
                backgroundColor: BaseColor.primary,
                top: height / 2.09,
                right: width / 2.78,
              }}>
              <Text
                style={{
                  paddingVertical: 9,
                  paddingHorizontal: 30,
                  fontFamily: FontFamily.bold,
                  fontSize: 16,
                  color: BaseColor.whiteColor,
                }}>
                Search
              </Text>
            </View>
            <View
              style={{
                position: 'absolute',
                top: height / 1.18,
                right: width / 5,
              }}>
              <Text
                style={{
                  color: BaseColor.whiteColor,
                  textAlign: 'center',
                  fontFamily: FontFamily.regular,
                  lineHeight: 20,
                }}>
                {`Tap here on trip icon \n to access camera and all info related to your trips`}
              </Text>
            </View>
            <View
              style={{
                position: 'absolute',
                top: height / 1.1,
                right: width / 2.5,
              }}>
              <CustomIcon
                name="point-out"
                color={BaseColor.whiteColor}
                size={30}
                style={{transform: [{rotate: '90deg'}]}}
              />
            </View>
            <View
              style={{
                position: 'absolute',
                top: height / 1.05,
                right: width / 2.85,
              }}>
              <CustomIcon
                color={BaseColor.whiteColor}
                size={20}
                name="cam-dash"
              />
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: 20,
  },
  bigBox: {
    width: '100%',
    height: '100%',
    borderColor: '#ddd',
    borderWidth: 1,
    borderRadius: 25,
    overflow: 'hidden',
  },
  smallBox: {
    width: 138,
    height: 98,
    position: 'absolute',
    bottom: 20,
    right: 20,
    borderColor: '#ddd',
    borderWidth: 1,
    borderRadius: 10,
    overflow: 'hidden',
  },
  flexBox: {
    flex: 1,
  },
  portraitValueContainer: {
    backgroundColor: BaseColor.whiteColor,
    marginVertical: 15,
    height: 60,
    flexDirection: 'row',
    borderRadius: 10,
    justifyContent: 'space-between',
    paddingHorizontal: 30,
    borderWidth: 0.3,
    padding: 10,
    borderColor: '#D6EBED',

    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 1.84,
  },
  placeHolder: {
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
    fontSize: 10,
  },
  values: {
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
    fontSize: 16,
    marginTop: 5,
  },
});

export default AddCameraSetup;
