import React, {useRef, useState, useEffect, useCallback, useMemo} from 'react';
import {
  ActivityIndicator,
  Alert,
  AppState,
  BackHandler,
  Dimensions,
  FlatList,
  Image,
  Platform,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import {ScrollView} from 'react-native-gesture-handler';
import Iicon from 'react-native-vector-icons/Ionicons';
import FIcon from 'react-native-vector-icons/Feather';
import {MMKV} from 'react-native-mmkv';
import {isEmpty, isUndefined, debounce} from 'lodash';
import WebView from 'react-native-webview';
import ViewShot from 'react-native-view-shot';
import moment from 'moment';
import Orientation from 'react-native-orientation-locker';
import Toast from 'react-native-simple-toast';
import MapView, {Marker, Polyline, PROVIDER_GOOGLE} from 'react-native-maps';
import BackgroundGeolocation from 'react-native-background-geolocation';
import BackgroundTimer from 'react-native-background-timer';
import BaseColor from '../../config/colors';
import {CustomIcon} from '../../config/LoadIcons';
import {savePageViewData, sendErrorReport} from '../../utils/commonFunction';
import {FontFamily} from '../../config/typography';
import PlaceAction from '../../redux/reducers/place/actions';
import TimerAction from '../../redux/reducers/timer/action';
import CButton from '../../components/CButton';
import TripAction from '../../redux/reducers/trip/actions';
import fetchWeatherData from '../../utils/weatherFunction';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import GPXMapScreen from '../../components/MapRendering';
import mapStyle from '../../config/mapCustomStyle';
import {useIsLandscape} from '../../components/LandScapOrientation';
import styles from './styles';
import {useFocusEffect} from '@react-navigation/native';
let backPressed = 0;

const displaySpinner = () => (
  <ActivityIndicator
    color="#bc2b78"
    size="large"
    styles={styles.activityIndicator}
  />
);
const mmkv = new MMKV();
const TRIP_DATA_KEY = 'trip_location_data';
const {width, height} = Dimensions.get('window');

function CamDash({navigation, route}) {
  const {userLocalAddress, userLatLong, userData} = useSelector(
    state => state.auth,
  );
  const {connectedDeviceList} = useSelector(state => state.modal);
  const [region, setRegion] = useState(null);
  const {isBleConnected} = useSelector(state => state.bluetooth);
  const {timer} = useSelector(state => state.timer);
  const {
    setTimerState,
    setTripTimerValue,
    setPlaceLocation,
    setTripPlaceName,
    setTripDistance,
    setDirectTripStart,
    setIsPause,
  } = PlaceAction;
  const {setStartCountDown} = TimerAction;
  const {setTripId, setTripDuration} = TripAction;
  const {currentTripId, currentTripDuration} = useSelector(state => state.trip);
  const {placeLocation, isTimerStart, tripDistance, directTripStart, isPause} =
    useSelector(state => state.place);
  const [weatherText, setWeatherText] = useState('');
  const dispatch = useDispatch();
  const [loc, setLoc] = useState({});
  const [averageSpeed, setAverageSpeed] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [appState, setAppState] = useState(AppState.currentState);
  const [showImage, setShowImage] = useState(false);
  const [isLandscape, setIsLandscape] = useState(false);
  const webViewRef = useRef();
  const [eventData, setEventData] = useState('event');
  const [headerData, setHeaderData] = useState('header');
  const [tap, setTap] = useState(false);
  const [imageUri, setImageUri] = useState();
  const [imageUriArr, setImgUriArr] = useState([]);
  const refM = useRef();
  const refS = useRef();
  const [arrUri, setArrUri] = useState([]);
  const [routeCoordinates, setRouteCoordinates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [startLoader, setStartLoader] = useState(false);
  const [details, setDetails] = useState({});
  const [currentPosition, setCurrentPosition] = useState(null);
  const [mapLoader, setMapLoader] = useState(false);
  const lastSavedPosition = useRef(null);
  const lastUpdateTime = useRef(null);
  const speedReadings = useRef([]);
  const mapViewRef = useRef(null);
  const [userHeading, setUserHeading] = useState(0);
  const isInitializingRef = useRef(false);
  const isInBackgroundRef = useRef(false);

  const landscape = useIsLandscape();
  useEffect(() => {
    if (Platform.OS === 'ios') {
      setMapLoader(true);
      setIsLandscape(landscape);
      setTimeout(() => {
        setMapLoader(false);
      }, 500);
    } else {
      setIsLandscape(landscape);
    }
  }, [landscape]);

  useFocusEffect(
    useCallback(() => {
      savePageViewData('timeline');
    }, []),
  );

  const saveNewCoordinate = useCallback((newCoordinate, type) => {
    console.log(`Saving coordinate: ${type}`, newCoordinate);
    sendErrorReport(type, 'save_coordinate_type');
    const tripDataString = mmkv.getString(TRIP_DATA_KEY);
    const tripData = tripDataString ? JSON.parse(tripDataString) : [];
    tripData.push(newCoordinate);
    sendErrorReport(tripDataString, 'newCoordinate_tracking');
    setRouteCoordinates(tripData);
    mmkv.set(TRIP_DATA_KEY, JSON.stringify(tripData));
  }, []);

  const calculateBearing = (startPoint, endPoint) => {
    const startLat = (startPoint.latitude * Math.PI) / 180;
    const startLng = (startPoint.longitude * Math.PI) / 180;
    const endLat = (endPoint.latitude * Math.PI) / 180;
    const endLng = (endPoint.longitude * Math.PI) / 180;

    const dLng = endLng - startLng;

    const y = Math.sin(dLng) * Math.cos(endLat);
    const x =
      Math.cos(startLat) * Math.sin(endLat) -
      Math.sin(startLat) * Math.cos(endLat) * Math.cos(dLng);

    let bearing = (Math.atan2(y, x) * 180) / Math.PI;
    if (bearing < 0) {
      bearing += 360;
    }

    return bearing;
  };

  const handleLocationSuccess = location => {
    if (!location || !location.latitude || !location.longitude) {
      console.warn('Invalid location data received');
      return;
    }

    const {latitude, longitude} = location;
    const defaultLocation = {
      latitude,
      longitude,
      latitudeDelta: 0.0009,
      longitudeDelta: 0.0009,
    };

    setRegion(defaultLocation);
    setCurrentPosition(location);
    setLoc(location);
    sendErrorReport(location, 'SMS_location');
  };

  const locationCount = useRef(0);
  const isWarmRef = useRef(false);

  const checkGPSWarmUp = location => {
    if (isWarmRef.current) return;

    locationCount.current += 1;
    const accuracy = location?.coords?.accuracy;

    console.log(`Update #${locationCount.current} - Accuracy: ${accuracy}m`);

    if (accuracy !== undefined) {
      isWarmRef.current = true;
      console.log('✅ GPS is warmed up and ready for precise tracking!');
    }
  };

  const updatePositionWithDebounce = useCallback(
    debounce((position, distance) => {
      if (!isTimerStart) {
        dispatch(setTripDistance(0));
        setAverageSpeed(0);
        return;
      }
      const {latitude, longitude, heading, speed, accuracy} = position;
      const newCoordinate = {latitude, longitude};
      sendErrorReport(position, 'position_update_debounced');
      sendErrorReport(heading, 'heading_location_update');
      sendErrorReport(speed, 'speed_locationn_update');
      const currentTime = Date.now();

      // Handle heading
      if (heading >= 0) {
        setUserHeading(heading);
      } else if (lastSavedPosition.current) {
        const calculatedHeading = calculateBearing(
          lastSavedPosition.current,
          newCoordinate,
        );
        setUserHeading(calculatedHeading);
      }

      // Handle speed
      if (speed === -1) {
        setAverageSpeed(0);
      } else {
        const speedKmh = speed * 3.6;
        setAverageSpeed(speedKmh);
      }
      if (!lastSavedPosition.current) {
        lastSavedPosition.current = newCoordinate;
        setStartTime(currentTime);
        saveNewCoordinate(newCoordinate, 'initial');
        setCurrentPosition(newCoordinate);
        setRegion({
          latitude: newCoordinate.latitude,
          longitude: newCoordinate.longitude,
          latitudeDelta: 0.0009,
          longitudeDelta: 0.0009,
        });
        return;
      }

      // Calculate distance between last saved position and current position
      const distanceInMeters = getDistance(
        lastSavedPosition.current,
        newCoordinate,
      );
      sendErrorReport(distanceInMeters, 'distanceInMeters_location_update');
      if (distance === 0) {
        const manualdistance = tripDistance + distanceInMeters;
        dispatch(setTripDistance(manualdistance));
        sendErrorReport(manualdistance, 'manualdistance_update');
      } else {
        dispatch(setTripDistance(distance));
      }
      lastSavedPosition.current = newCoordinate;
      saveNewCoordinate(newCoordinate, 'update');
      setCurrentPosition(newCoordinate);

      // Update map region
      setRegion(prev => ({
        latitude: newCoordinate.latitude,
        longitude: newCoordinate.longitude,
        latitudeDelta: prev?.latitudeDelta || 0.0009,
        longitudeDelta: prev?.longitudeDelta || 0.0009,
      }));
    }, 100),
    [tripDistance, startTime, isLandscape, averageSpeed],
  );

  const updatePositionDirect = useCallback(
    (position, distance) => {
      if (!isTimerStart) {
        return;
      }
      const {latitude, longitude, heading, speed, accuracy} = position;
      const newCoordinate = {latitude, longitude};
      sendErrorReport(position, 'position_update_direct_trip');
      sendErrorReport(heading, 'heading_location_update');
      sendErrorReport(speed, 'speed_locationn_update');
      const currentTime = Date.now();

      // Handle heading
      if (heading >= 0) {
        setUserHeading(heading);
      } else if (lastSavedPosition.current) {
        const calculatedHeading = calculateBearing(
          lastSavedPosition.current,
          newCoordinate,
        );
        setUserHeading(calculatedHeading);
      }

      // Handle speed
      if (speed === -1) {
        setAverageSpeed(0);
      } else {
        const speedKmh = speed * 3.6;
        setAverageSpeed(speedKmh);
      }
      if (!lastSavedPosition.current) {
        lastSavedPosition.current = newCoordinate;
        setStartTime(currentTime);
        saveNewCoordinate(newCoordinate, 'initial');
        setCurrentPosition(newCoordinate);
        setRegion({
          latitude: newCoordinate.latitude,
          longitude: newCoordinate.longitude,
          latitudeDelta: 0.0009,
          longitudeDelta: 0.0009,
        });
        return;
      }

      // Calculate distance between last saved position and current position
      const distanceInMeters = getDistance(
        lastSavedPosition.current,
        newCoordinate,
      );
      sendErrorReport(distanceInMeters, 'distanceInMeters_location_update');
      if (distance === 0) {
        const manualdistance = tripDistance + distanceInMeters;
        dispatch(setTripDistance(manualdistance));
        sendErrorReport(manualdistance, 'manualdistance_update');
      } else {
        dispatch(setTripDistance(distance));
      }
      lastSavedPosition.current = newCoordinate;
      saveNewCoordinate(newCoordinate, 'update');
      setCurrentPosition(newCoordinate);

      // Update map region
      setRegion(prev => ({
        latitude: newCoordinate.latitude,
        longitude: newCoordinate.longitude,
        latitudeDelta: prev?.latitudeDelta || 0.0009,
        longitudeDelta: prev?.longitudeDelta || 0.0009,
      }));
    },
    [tripDistance, startTime, isLandscape, averageSpeed],
  );

  // Use this function as the main entry point that decides which implementation to use
  const updatePosition = useCallback(
    (position, distance) => {
      // Check the ref instead of state
      const isBackground = isInBackgroundRef.current;
      if (isBackground && Platform.OS === 'android') {
        console.log('app in background mode - using direct updates');
        // In background, use direct version to ensure all points are captured
        updatePositionDirect(position, distance);
      } else {
        console.log(
          'app in foreground mode - using debounced updates',
          position,
          distance,
        );
        // In foreground, use debounced version for performance
        updatePositionWithDebounce(position, distance);
      }
    },
    [updatePositionDirect, updatePositionWithDebounce],
  );

  const getDistance = (point1, point2) => {
    const R = 6371e3;
    const φ1 = (point1.latitude * Math.PI) / 180;
    const φ2 = (point2.latitude * Math.PI) / 180;
    const Δφ = ((point2.latitude - point1.latitude) * Math.PI) / 180;
    const Δλ = ((point2.longitude - point1.longitude) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  };

  // Initialize BackgroundGeolocation with event listeners
  const setupBackgroundGeolocation = async () => {
    if (isInitializingRef.current) return;
    isInitializingRef.current = true;

    try {
      // 1. Register event listeners FIRST (before ready)
      BackgroundGeolocation.onLocation(
        location => {
          console.log('[onLocation] in CamDash Screen------>>>>', location);
          checkGPSWarmUp(location);
          updatePosition(location.coords, location.odometer);
          sendErrorReport(location, 'onLocation_trip_tracking');
        },
        error => {
          console.error('[onLocation] ERROR:', error);
          sendErrorReport(error, 'onLocation_error_trip_tracking');
        },
      );

      // 2. Configure and initialize
      await BackgroundGeolocation.ready({
        desiredAccuracy: BackgroundGeolocation.DESIRED_ACCURACY_HIGH,
        distanceFilter: 5, // Minimum distance between location updates (meters)
        stationaryRadius: 10, // Radius for stationary detection (meters)
        locationUpdateInterval: 1000,
        fastestLocationUpdateInterval: 500,
        activityRecognitionInterval: 5000,
        disableMotionActivityUpdates: false,
        stopOnStationary: false, // Don't stop when stationary
        preventSuspend: true,
        locationAuthorizationRequest: 'Always',
        allowsBackgroundLocationUpdates: true,
        pausesLocationUpdatesAutomatically: false,
        showsBackgroundLocationIndicator: true,
        debug: false, // Enable logging during development
        stopOnTerminate: false,
        startOnBoot: true,
        foregroundService: true,
        enableHeadless: true,
        disableElasticity: true,
        heartbeatInterval: 30, // Reduced to 30 seconds to get more frequent updates
        activityType: BackgroundGeolocation.ACTIVITY_TYPE_OTHER_NAVIGATION,
      });

      isInitializingRef.current = false;
    } catch (error) {
      console.error('Error setting up background geolocation:', error);
      sendErrorReport(error, 'setup_bg_geolocation_error');
      isInitializingRef.current = false;
    }
  };

  const cleanupBackgroundGeolocation = async () => {
    try {
      console.log('BackgroundGeolocation stoppedn4444444');
      await BackgroundGeolocation.stop();
      console.log('[CamDash] BackgroundGeolocation stopped');
    } catch (error) {
      sendErrorReport(error, 'cleanup_bg_geolocation_error');
    }
  };

  // Initialize once when component mounts
  useEffect(() => {
    // Setup the plugin first, don't call getCurrentLocation before ready
    setupBackgroundGeolocation();

    return () => {
      // Only stop, don't remove listeners
      cleanupBackgroundGeolocation();
    };
  }, []);

  // Separate effect for starting/stopping tracking based on trip state
  useEffect(() => {
    if (isTimerStart && !isPause) {
      // Start tracking immediately when trip starts
      BackgroundGeolocation.start().then(() => {
        // Force immediate tracking with changePace
        BackgroundGeolocation.changePace(true);

        // Get initial position after starting
        BackgroundGeolocation.getCurrentPosition({
          samples: 1,
          timeout: 10,
          maximumAge: 0,
          desiredAccuracy: 10,
          persist: false, // Don't persist this to database
        })
          .then(location => {
            console.log('[getCurrentPosition]', location);
            updatePosition(location.coords, 0, 'getCurrentPosition');
          })
          .catch(error => {
            console.warn('[getCurrentPosition] ERROR', error);
          });
      });
    } else if (isInitializingRef.current === false) {
      // Only stop if we're not in the middle of initializing
      BackgroundGeolocation.stop();
    }
  }, [isTimerStart, isPause]);

  useEffect(() => {
    if (route?.params) {
      const detail = route?.params?.detais;
      // this is for set Route for Trip..
      if (isEmpty(detail)) {
        dispatch(setDirectTripStart(true));
      }
      setDetails(detail);
    } else {
      setDetails({});
    }
  }, [route]);

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  const onMessage = event => {
    setEventData(JSON.stringify(event.nativeEvent.data));
    const data = JSON.parse(event.nativeEvent.data);
    setHeaderData(data.header);
  };

  const renderMap = () => {
    if (directTripStart) {
      const fallbackRegion = {
        latitude: 28.6139,
        longitude: 77.209,
        latitudeDelta: 0.1,
        longitudeDelta: 0.1,
      };
      return mapLoader ? (
        <ActivityIndicator
          style={{justifyContent: 'center', alignItems: 'center', flex: 1}}
        />
      ) : (
        <View style={{borderRadius: 10}}>
          <MapView
            ref={mapViewRef}
            key={`map-${isLandscape}`}
            provider={PROVIDER_GOOGLE}
            style={[
              styles.map,
              {height: isLandscape ? height * 0.5 : height / 2.4},
            ]}
            customMapStyle={mapStyle}
            region={region || fallbackRegion}
            pitchEnabled={false}
            rotateEnabled={false}
            loadingEnabled={true}
            loadingIndicatorColor="#666666"
            loadingBackgroundColor="#eeeeee"
            followsUserLocation={isLandscape}>
            {routeCoordinates.length > 0 && (
              <Polyline
                coordinates={routeCoordinates}
                strokeColor="#FA6912"
                strokeWidth={2}
              />
            )}

            {routeCoordinates?.length > 0 && (
              <Marker
                key={`start-marker-${routeCoordinates[0].latitude}-${routeCoordinates[0].longitude}`}
                coordinate={routeCoordinates[0]}
                title="Start Point"
                description="This is the starting location.">
                <View style={styles.startMarker}>
                  <Iicon name="location" size={25} color="#5255F1" />
                </View>
              </Marker>
            )}

            {routeCoordinates?.length > 1 && (
              <Marker
                key={`start-${isLandscape}`}
                coordinate={routeCoordinates[routeCoordinates.length - 1]}
                rotation={userHeading}>
                <View style={styles.markerImage}>
                  <FIcon name={'navigation-2'} color={'#6a8889'} size={20} />
                </View>
              </Marker>
            )}
          </MapView>
        </View>
      );
    } else {
      return (
        <GPXMapScreen
          uri={details?.file_url}
          type={details?.file_url ? 'camDash' : ''}
          isPause={isPause}
          details={details}
          mapLoader={mapLoader}
          getCurrentPositions={position => {
            if (isTimerStart) {
              setRouteCoordinates(position?.route);
              dispatch(setTripDistance(position?.distance));
              setAverageSpeed(position?.speed);
            }
          }}
          loading={loading}
          isLandscape={isLandscape}
          mapContaierStyle={{
            marginHorizontal: 0,
            height: isLandscape ? height / 2 : height / 2.4,
          }}
        />
      );
    }
  };

  const renderWebView = () => {
    return (
      <ViewShot
        ref={refS}
        startInLoadingState
        options={{
          fileName: 'qeridoo',
          format: 'jpg',
          quality: 0.9,
        }}
        style={{
          height: '100%',
          width: '100%',
        }}>
        <WebView
          style={{
            marginTop: !isLandscape && !tap && '20%',
          }}
          ref={ref => (webViewRef.current = ref)}
          source={{
            uri: 'http://***********/stream',
          }}
          pullToRefreshEnabled
          onError={e => {
            Alert.alert('error');
          }}
          onMessage={onMessage}
          javaScriptEnabled
          domStorageEnabled
          contentInset={{
            top: 0,
            right: 0,
            left: 0,
            bottom: 0,
          }}
          scrollEnabled
          renderLoading={() => displaySpinner()}
        />
      </ViewShot>
    );
  };

  useEffect(() => {
    if (showImage) {
      if (tap) {
        refM?.current?.capture()?.then(uri => {
          setImageUri(uri);
          setImgUriArr([
            ...imageUriArr,
            {
              save_image: uri,
              location_name: 'vadodara',
              date_time: moment.now(),
            },
          ]);
        });
      } else {
        refS?.current?.capture()?.then(uri => {
          setImageUri(uri);
          arrUri.push({
            save_image: uri,
            location_name: '-',
            date_time: moment.now(),
          });
          setArrUri(arrUri);
          imageUriArr.push({
            save_image: uri,
            location_name: '-',
            date_time: moment.now(),
          });
          setImgUriArr(imageUriArr);
        });
      }
    }
  }, [showImage]);

  useEffect(() => {
    if (showImage) {
      setTimeout(() => {
        setShowImage(false);
      }, 5000);
    }
  }, [showImage]);

  const convertDateTime = dateTime => {
    const currentDate = new Date();
    const itemDate = new Date(dateTime);

    const diffInMilliseconds = Math.abs(currentDate - itemDate);
    const diffInMinutes = Math.floor(diffInMilliseconds / 1000 / 60);

    const hours = Math.floor(diffInMinutes / 60);
    const minutes = diffInMinutes % 60;
    if (minutes === 0) {
      return 'Just Now';
    }

    const formattedDate = `${itemDate.getDate()}/${
      itemDate.getMonth() + 1
    }/${itemDate.getFullYear()}`;

    return `${hours} hr ${minutes} min | ${formattedDate}`;
  };

  const updatedIndexData = data => {
    if (!isUndefined(data)) {
      setTimeout(() => {
        imageUriArr.splice(data, 1);
        setImgUriArr(imageUriArr);
      }, 2000);
      deleteSnapVideo(data);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await fetchWeatherData(
          userData.latitude || userLatLong.latitude,
          userData.longitude || userLatLong.longitude,
        );
        setWeatherText(result);
      } catch (error) {
        console.error('Error fetching weather data: ', error);
      }
    };

    fetchData();
  }, [userLatLong]);

  const render = ({item, index}) => (
    <TouchableOpacity
      activeOpacity={0.8}
      style={[styles.imageSubContainer]}
      onPress={() => {
        navigation.navigate('snapShotView', {
          imageUri: item.save_image,
          loc: item.location_name,
          date: item.date_time,
          id: item.id || index,
          updatedIndex: updatedIndexData,
        });
      }}>
      <Image
        source={{uri: item.save_image}}
        style={[styles.snapShotView]}
        resizeMode="cover"
      />
      <View style={[styles.snapShotValue]}>
        <Text
          style={{
            fontFamily: FontFamily.regular,
            fontSize: 9,
            color: BaseColor.whiteColor,
          }}
          numberOfLines={1}>
          Snapshot - {item?.location_name}
        </Text>
        <Text style={[styles.snapShotValueText]}>
          {item?.date_time && convertDateTime(item.date_time)}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const timerRef = useRef(timer);

  useEffect(() => {
    timerRef.current = timer;
  }, [timer]);

  const saveTripTrack = async (time, type = '') => {
    try {
      const data = {
        trip_id: currentTripId,
        duration: time,
        routes: JSON.stringify(routeCoordinates),
      };
      sendErrorReport(data, 'data_saved_trip');
      const response = await getApiData(
        BaseSetting.endpoints.saveTrackDetail,
        'POST',
        data,
        '',
        true,
      );
      sendErrorReport(response, 'responce_saved_trip');
      if (response?.success) {
        if (type !== 'paused_trip') {
          stopTrip();
        }
      }
    } catch (error) {
      console.log('error for device list ===', error);
      sendErrorReport(error, 'saving_trip_name');
    }
  };

  // Stop Trip API Integration....
  const stopTrip = async () => {
    setLoading(true);
    try {
      // First check if BackgroundGeolocation is enabled
      const state = await BackgroundGeolocation.getState().catch(err => {
        console.log('Failed to get BackgroundGeolocation state:', err);
        return {enabled: false};
      });

      const data = {
        trip_id: currentTripId,
        distance: tripDistance / 1000,
        duration: currentTripDuration,
        end_lat: currentPosition?.latitude,
        end_lng: currentPosition?.longitude,
      };
      // Send API request to stop trip
      const response = await getApiData(
        BaseSetting.endpoints.stopTrip,
        'POST',
        data,
        '',
        true,
      );
      if (response?.success) {
        // Only try to stop BackgroundGeolocation if it's enabled
        if (state.enabled) {
          try {
            // First change pace to false to stop active tracking
            await BackgroundGeolocation.changePace(false);
            // Then stop the service
            await BackgroundGeolocation.stop();
            // Reset odometer
            await BackgroundGeolocation.resetOdometer();
          } catch (bgError) {
            console.error('Error stopping BackgroundGeolocation:', bgError);
            sendErrorReport(bgError, 'background_geolocation_stop_error');
            // Continue with cleanup even if BackgroundGeolocation fails
          }
        }

        // Reset all tracking-related state
        setAverageSpeed(0);
        setStartTime(null);
        lastSavedPosition.current = null;
        speedReadings.current = [];

        // Reset Redux state
        dispatch(setTripDistance(0));
        setDetails({});
        dispatch(setTimerState(false));
        dispatch(setIsPause(false));
        dispatch(setTripTimerValue(0));
        dispatch(setPlaceLocation({}));

        // Clear route data
        setRouteCoordinates([]);
        dispatch(setDirectTripStart(false));
        mmkv.delete(TRIP_DATA_KEY);

        // Navigate to appropriate screen
        if (connectedDeviceList?.camera_detail >= 1) {
          navigation.navigate('Bookmark', {
            tripEnd: true,
            tripName: placeLocation.tripName,
          });
        } else {
          navigation.navigate('SaveTrip');
        }

        setLoading(false);
        Toast.show(response?.message);
      } else {
        setLoading(false);
        Toast.show(response?.message || 'Failed to stop trip');
      }
    } catch (error) {
      setLoading(false);
      console.error('Error stopping trip:', error);
      sendErrorReport(error, 'stop_trip_error');
      Toast.show('Failed to stop trip. Please try again.');
    } finally {
      // Ensure loading state is reset and speed is set to zero
      setAverageSpeed(0);
    }
  };

  const startTrip = async () => {
    setStartLoader(true);
    const name = moment().toLocaleString();
    try {
      // Make sure BackgroundGeolocation is ready before starting
      const state = await BackgroundGeolocation.getState();
      if (!state.enabled) {
        await BackgroundGeolocation.start();
      }

      // Force immediate tracking
      await BackgroundGeolocation.changePace(true);

      // Get current position
      const location = await BackgroundGeolocation.getCurrentPosition({
        timeout: 30,
        maximumAge: 0,
        desiredAccuracy: 10,
        samples: 1,
        persist: false,
      });

      console.log('🚀 ~ startTrip ~ location:', location);
      handleLocationSuccess(location?.coords);
      await BackgroundGeolocation.resetOdometer();

      const data = {
        trip_type: 'random_trip',
        trip_name: name || '',
        status: 'inprogress',
        lat: location?.coords?.latitude,
        lng: location?.coords?.longitude,
      };

      console.log('🚀 ~ startTrip ~ data:', data);
      sendErrorReport(data, 'trip_start_location');

      const response = await getApiData(
        BaseSetting.endpoints.saveTripName,
        'POST',
        data,
        '',
        true,
      );

      console.log('🚀 ~ startTrip ~ response:', response);
      if (response?.success) {
        console.log('[BackgroundGeolocation] Started');
        dispatch(setDirectTripStart(true));
        dispatch(setTripId(response?.data?.id));
        dispatch(setTripPlaceName(name));
        dispatch(setTimerState(true));
        Toast.show(response?.message);
      }
      setStartLoader(false);
    } catch (error) {
      setStartLoader(false);
      console.log('error for device list ===', error);
      sendErrorReport(error, 'saving_trip_name');
    }
  };

  const pauseTrip = async () => {
    setLoading(true);
    await saveTripTrack(timerRef.current, 'paused_trip');
    try {
      const data = {
        trip_id: currentTripId,
      };
      const response = await getApiData(
        BaseSetting.endpoints.pauseTrip,
        'POST',
        data,
        '',
        true,
      );
      if (response?.success) {
        await cleanupBackgroundGeolocation();
        dispatch(setIsPause(true));
        Toast.show(response?.message);
        setLoading(false);
      } else {
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      console.log('error for device list ===', error);
      sendErrorReport(error, 'saving_trip_name');
    }
  };

  const focusMapOnUser = useCallback(() => {
    if (mapViewRef.current && currentPosition) {
      mapViewRef.current.animateToRegion(
        {
          latitude: currentPosition.latitude,
          longitude: currentPosition.longitude,
          latitudeDelta: 0.0005,
          longitudeDelta: 0.0005,
        },
        1000,
      );
    }
  }, [currentPosition]);

  useEffect(() => {
    if (isLandscape && currentPosition) {
      const timer = setTimeout(() => {
        focusMapOnUser();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isLandscape, currentPosition, focusMapOnUser]);

  useEffect(() => {
    const handleAppStateChange = nextAppState => {
      if (nextAppState === 'active') {
        setAppState(true);
        isInBackgroundRef.current = false;
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        setAppState(false);
        isInBackgroundRef.current = true;
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => subscription.remove();
  }, []);

  return (
    <View style={[styles.root]}>
      <StatusBar translucent />
      {!isLandscape && <View style={styles.headerMargin}></View>}
      {!isLandscape ? (
        <ScrollView
          contentContainerStyle={[
            {
              flexGrow: 1,
              marginHorizontal: 15,
            },
          ]}
          bounces={false}
          showsVerticalScrollIndicator={true}>
          <View style={styles.portraitHeader}>
            <Text style={styles.portraitHeaderText}>
              Please go to "Settings" to reconnect the device
            </Text>
            <TouchableOpacity
              onPress={() => {
                Orientation.lockToPortrait();
                navigation.navigate('SnapGallery');
              }}>
              <CustomIcon
                name="file-manager"
                size={22}
                color={BaseColor.blackColor}
              />
            </TouchableOpacity>
          </View>

          <View style={[styles.portraitViewMainContainer]}>
            {!isBleConnected ? (
              renderMap()
            ) : (
              <View style={[styles.bigViewContainer]}>
                <View
                  style={{
                    height: '100%',
                  }}>
                  {tap ? renderMap() : renderWebView()}
                </View>
                <TouchableOpacity
                  onPress={() => setTap(!tap)}
                  style={styles.smallViewContainer}>
                  {tap ? renderWebView() : renderMap()}
                </TouchableOpacity>
              </View>
            )}

            {showImage ? (
              <View
                style={[
                  styles.clipImageContainer,
                  {
                    borderColor: isBleConnected
                      ? BaseColor.whiteColor
                      : '#F8F8F8',
                  },
                  {
                    bottom: isLandscape ? 35 : 20,
                    left: isLandscape ? 20 : 20,
                  },
                ]}>
                <Image
                  source={{
                    uri: imageUri,
                  }}
                  style={styles.clipImage}
                  resizeMode="cover"
                />
                <View
                  style={{
                    position: 'absolute',
                    zIndex: 1,
                    top: '25%',
                  }}>
                  <CustomIcon
                    name="check-rounded"
                    size={16}
                    color={BaseColor.whiteColor}
                  />
                </View>
              </View>
            ) : (
              connectedDeviceList?.camera_detail >= 1 && (
                <TouchableOpacity
                  style={[
                    styles.saveImageBtn,
                    {
                      backgroundColor: isBleConnected
                        ? BaseColor.red
                        : '#DCE1E1',
                      borderColor: isBleConnected
                        ? BaseColor.whiteColor
                        : '#F8F8F8',
                      bottom: isLandscape ? 35 : 20,
                      left: isLandscape ? 20 : 20,
                    },
                  ]}
                  onPress={() => {
                    if (isBleConnected) {
                      setShowImage(true);
                    }
                  }}>
                  {isBleConnected && (
                    <CustomIcon
                      name="add-image"
                      size={16}
                      color={BaseColor.whiteColor}
                    />
                  )}
                </TouchableOpacity>
              )
            )}
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginVertical: 10,
            }}>
            <CustomIcon
              name="location-pin-outline"
              size={15}
              color={'#5E5F60'}
            />
            <Text
              style={{
                fontSize: 14,
                color: '#5E5F60',
                marginLeft: 5,
              }}>
              #
              {`${
                userLocalAddress?.localAddress1
                  ? userLocalAddress?.localAddress1
                  : userLocalAddress?.localAddress2
                  ? userLocalAddress?.localAddress2
                  : userLocalAddress?.localAddress3
                  ? userLocalAddress?.localAddress3
                  : ''
              }`}
            </Text>
          </View>
          <Text
            style={{
              fontFamily: FontFamily.regular,
              color: BaseColor.blackColor,
              fontSize: 13,
              lineHeight: 20,
            }}>
            {weatherText}
          </Text>
          <View style={[styles.landscapeValueContainer]}>
            <View style={styles.valContent}>
              <Text style={[styles.placeHolder]}>{`Distance \n (Km)`}</Text>
              <Text style={styles.values}>
                {(tripDistance / 1000).toFixed(3) || 0}
              </Text>
            </View>
            <View style={styles.valContent}>
              <Text style={[styles.placeHolder]}>{`Speed \n `}</Text>
              <Text style={styles.values}>{`${averageSpeed.toFixed(
                2,
              )} km/h`}</Text>
            </View>
            <View style={styles.valContent}>
              <Text style={[styles.placeHolder]}>{`Time \n (minutes)`}</Text>
              <TimerComponent appState={appState} />
            </View>
          </View>
          {!isEmpty(imageUriArr) ? (
            <>
              <Text style={styles.snapShotPlaceHolder}>Saved Snapshots</Text>
              <View style={[styles.imagesContainer]}>
                <FlatList
                  data={imageUriArr}
                  renderItem={render}
                  keyExtractor={(item, index) => index.toString()}
                  showsVerticalScrollIndicator={false}
                  numColumns={3}
                  nestedScrollEnabled
                  maxHeight={height / 2}
                />
              </View>
            </>
          ) : null}
          {isPause ? (
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-evenly',
                marginTop: isEmpty(imageUriArr) ? height / 10 : 20,
                marginBottom: height / 10,
              }}>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => {
                  dispatch(setIsPause(false));
                }}
                style={{
                  borderRadius: 10,
                  shadowColor: '#000',
                  height: 40,
                  width: '30%',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.25,
                  shadowRadius: 3.84,
                  elevation: 2,
                  backgroundColor: BaseColor.whiteColor,
                  alignItems: 'center',
                  justifyContent: 'center',
                  alignSelf: 'center',
                }}>
                <Iicon
                  name="play-outline"
                  size={35}
                  color={BaseColor.primary}
                />
              </TouchableOpacity>
              <CButton
                title={'Stop Trip'}
                style={{
                  width: '50%',
                }}
                titleStyle={{
                  fontFamily: FontFamily.bold,
                }}
                loader={loading}
                onPress={() => {
                  saveTripTrack(timerRef.current);
                }}
              />
            </View>
          ) : isTimerStart ? (
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => pauseTrip()}
              style={{
                borderRadius: 10,
                shadowColor: '#000',
                height: 40,
                width: '30%',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
                elevation: 2,
                backgroundColor: BaseColor.whiteColor,
                alignItems: 'center',
                alignSelf: 'center',
                justifyContent: 'center',
                marginTop: isEmpty(imageUriArr) ? height / 12 : 20,
                marginBottom: height / 10,
              }}>
              {loading ? (
                <ActivityIndicator color={BaseColor.primary} />
              ) : (
                <Iicon name="pause" size={30} color={BaseColor.primary} />
              )}
            </TouchableOpacity>
          ) : (
            <CButton
              title={'Start Trip'}
              style={{
                width: '60%',
                marginTop: isEmpty(imageUriArr) ? height / 10 : 20,
                marginBottom: height / 10,
              }}
              titleStyle={{
                fontFamily: FontFamily.bold,
              }}
              loader={startLoader}
              onPress={() => {
                startTrip();
              }}
            />
          )}
        </ScrollView>
      ) : (
        <View>
          {renderMap()}
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'row',
            }}>
            <DistanceWithTimerComponent
              distance={isTimerStart ? tripDistance : 0}
              ir={averageSpeed}
              unit={'Km'}
              isPause={isPause}
              appState={appState}
              isTimerStart={isTimerStart}
            />
            {isPause ? (
              <>
                <TouchableOpacity
                  activeOpacity={0.7}
                  onPress={() => {
                    dispatch(setIsPause(false));
                  }}
                  style={[styles.landscapeButtonContainer]}>
                  <Iicon
                    name="play-outline"
                    size={40}
                    color={BaseColor.primary}
                  />
                </TouchableOpacity>
                <CButton
                  title={'Stop \nTrip'}
                  style={styles.landscapStopButton}
                  titleStyle={{
                    fontFamily: FontFamily.bold,
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    fontSize: 18,
                  }}
                  loader={loading}
                  onPress={() => {
                    saveTripTrack(timerRef.current);
                  }}
                />
              </>
            ) : isTimerStart ? (
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => pauseTrip()}
                style={[styles.landscapeButtonContainer]}>
                {loading ? (
                  <ActivityIndicator color={BaseColor.primary} size={40} />
                ) : (
                  <Iicon name="pause" size={40} color={BaseColor.primary} />
                )}
              </TouchableOpacity>
            ) : null}
          </View>
        </View>
      )}
    </View>
  );
}
export default CamDash;

const TimerComponent = props => {
  const {appState} = props;
  const {isTimerStart, isPause} = useSelector(state => state.place);
  const {timer} = useSelector(state => state.timer);
  const {setStartCountDown} = TimerAction;
  const {setTripDuration} = TripAction;
  const dispatch = useDispatch();

  const startBackgroundTimer = useCallback(() => {
    let xyz = timer;

    return BackgroundTimer.setInterval(() => {
      xyz += 1;
      dispatch(setStartCountDown(xyz));
      dispatch(setTripDuration(xyz));
    }, 1000);
  }, [dispatch, timer]);

  useEffect(() => {
    let timerInterval;
    let backgroundInterval = null;
    let xyz = timer;
    if (!isPause) {
      if (isTimerStart) {
        if (Platform.OS === 'android' && !appState) {
          backgroundInterval = startBackgroundTimer();
        } else {
          timerInterval = setInterval(() => {
            xyz += 1;
            dispatch(setStartCountDown(xyz));
            dispatch(setTripDuration(xyz));
          }, 1000);
        }
      } else {
        dispatch(setStartCountDown(0));
      }
    }

    return () => {
      if (backgroundInterval) {
        BackgroundTimer.clearInterval(backgroundInterval);
      }
      if (timerInterval) {
        clearInterval(timerInterval);
      }
    };
  }, [isTimerStart, dispatch, isPause, appState]);

  const formatTime = time => {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = time % 60;

    return `${hours.toString().padStart(2, '0')} : ${minutes
      .toString()
      .padStart(2, '0')} : ${seconds.toString().padStart(2, '0')}`;
  };

  const formattedTime = formatTime(timer);

  return <Text style={styles.values}>{formattedTime}</Text>;
};

const DistanceWithTimerComponent = ({
  distance,
  ir,
  unit,
  isPause,
  appState,
  isTimerStart,
}) => {
  const speedVal = isTimerStart ? ir : 0;
  return (
    <View style={[styles.landscapeComponentContainer]}>
      <View style={styles.valContent}>
        <Text style={[styles.placeHolder]}>{`Distance (${unit})`}</Text>
        <Text style={styles.values}>{(distance / 1000).toFixed(3) || 0}</Text>
      </View>
      <View style={styles.valContent}>
        <Text style={[styles.placeHolder]}>Speed</Text>
        <Text style={styles.values}>{`${ir.toFixed(2)} km/h`}</Text>
      </View>
      <View style={styles.valContent}>
        <Text style={[styles.placeHolder]}>Time (minutes)</Text>
        <TimerComponent appState={appState} />
      </View>
    </View>
  );
};
