import {Dimensions, Platform, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const {width, height} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';
const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
  },

  headerMargin: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    height: '7%',
    alignContent: 'center',
    paddingHorizontal: 16,
    backgroundColor: BaseColor.whiteColor,
    // borderWidth: 1,
  },
  portraitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  portraitHeaderText: {
    color: BaseColor.primary,
    fontSize: 10,
  },
  portraitViewMainContainer: {
    marginVertical: 10,
    borderRadius: 10,
    overflow: 'hidden',
    height: Dimensions.get('window').height / 2.4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 2,
    backgroundColor: BaseColor.whiteColor,
    position: 'relative',
  },

  portraitValueContainer: {
    backgroundColor: BaseColor.whiteColor,
    margin: 10,
    height: 60,
    flexDirection: 'row',
    borderRadius: 10,
    justifyContent: 'space-between',
    paddingHorizontal: 30,
    borderWidth: 0.3,
    borderColor: '#D6EBED',

    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 1.84,
  },

  bigViewContainer: {
    position: 'relative',
    // backgroundColor: BaseColor.whiteColor,
  },

  smallViewContainer: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    height: '25%',
    width: '35%',
    overflow: 'hidden',
    borderRadius: 10,
    backgroundColor: BaseColor.whiteColor,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },

  clipImageContainer: {
    backgroundColor: BaseColor.whiteColor,
    position: 'absolute',

    zIndex: 99999999,
    justifyContent: 'space-evenly',
    alignItems: 'center',
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 5,
  },

  clipImage: {
    height: 35,
    width: 35,
    borderColor: BaseColor.blackColor,
    borderRadius: 30,
  },

  saveImageBtn: {
    position: 'absolute',
    zIndex: 99999999,
    justifyContent: 'space-evenly',
    alignItems: 'center',
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 5,
  },

  placeHolder: {
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
    fontSize: 14,
    textAlign: 'center',
  },

  values: {
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    fontSize: 16,
    marginVertical: 10,
  },

  snapShotPlaceHolder: {
    fontFamily: FontFamily.regular,
    fontSize: 16,
    color: '#333333',
    marginTop: 20,
    marginLeft: 10,
  },

  imagesContainer: {
    paddingHorizontal: 5,
  },

  imageSubContainer: {
    width: '28%',
    margin: 10,
    overflow: 'hidden',
    backgroundColor: BaseColor.whiteColor,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderRadius: 15,
  },

  snapShotView: {
    width: '100%',
    height: 129,
    borderRadius: 15,
  },

  gradientImageStyle: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: '50%',
    borderBottomLeftRadius: 15,
    borderBottomRightRadius: 15,
  },

  snapShotValue: {
    position: 'absolute',

    bottom: 0,

    paddingLeft: 5,

    width: '100%',
    height: '20%',
    borderBottomEndRadius: 15,
    borderBottomStartRadius: 15,
  },

  snapShotValueText: {
    fontSize: 6,

    color: BaseColor.whiteColor,
  },

  // landscape

  landscapeViewMainContainer: {
    backgroundColor: BaseColor.whiteColor,
  },

  landscapeSmallViewContainer: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    height: '30%',
    width: '20%',
    overflow: 'hidden',
    borderRadius: 10,
    backgroundColor: BaseColor.whiteColor,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },

  landscapeValueContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 10,
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderWidth: 0.5,
    borderColor: '#D6EBED',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    marginTop: 30,
    shadowOpacity: 0.05,
    shadowRadius: 1.84,
  },
  landscapeComponentContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    position: 'absolute',
    bottom: IOS ? height / 12 : height / 13,
    borderRadius: 10,
    width: width / 1,
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderWidth: 0.5,
    borderColor: '#D6EBED',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 1.84,
    backgroundColor: BaseColor.whiteColor,
  },
  landscapeButtonContainer: {
    position: 'absolute',
    bottom: IOS ? height / 12 : height / 13,
    borderRadius: 10,
    right: width / 2.5,
    paddingHorizontal: 10,
    paddingVertical: 23,
    borderWidth: 0.5,
    borderColor: '#D6EBED',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 1.84,
    backgroundColor: BaseColor.whiteColor,
  },
  landscapStopButton: {
    position: 'absolute',
    height: width / 4.5,
    width: IOS ? '15%' : '12%',
    bottom: IOS ? height / 12 : height / 13,
    borderRadius: 10,
    right: width / (IOS ? 17 : 19),
    paddingVertical: 13,
    borderWidth: 0.5,
    borderColor: '#D6EBED',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 1.84,
  },
  valContent: {alignItems: 'center'},
  map: {width: '100%'},
  startMarker: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#bfbdfc',
  },
  markerImage: {
    width: 30,
    height: 30,
    borderRadius: 30,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default styles;
