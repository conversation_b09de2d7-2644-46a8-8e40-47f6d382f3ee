import React from "react";
import { Text, TouchableOpacity } from "react-native";
import { View } from "react-native-animatable";
import { ScrollView } from "react-native-gesture-handler";
import CustomHeader from "../../components/CustomHeader/CustomHeader";
import styles from "./styles";
import FIcon from "react-native-vector-icons/Feather";
import BaseColor from "../../config/colors";
import CButton from "../../components/CButton";
import { translate } from "../../lang/Translate";
import { FontFamily } from "../../config/typography";
import { useDispatch } from "react-redux";
import authActions from "../../redux/reducers/auth/actions";

function CampaignQuiz({ navigation }) {
  const quizData = [
    {
      id: 1,
      title: "What's your favourite outdoor activity?",
      options: [
        { id: 1, name: "Hiking", selected: false },
        { id: 2, name: "Nature Walks", selected: false },
        { id: 3, name: "Jogging/Running", selected: false },
        { id: 4, name: "Pickniking", selected: false },
        { id: 5, name: "Urban Strolls", selected: false },
        { id: 6, name: "Community Meet-ups", selected: false },
      ],
    },
  ];

  const [data, setData] = React.useState(quizData);
  const dispatch = useDispatch();
  const { setWalkthrough } = authActions;

  const handleChoose = (option) => {
    const newData = data.map((item) => {
      const selectedOption = item.options.find((opt) => opt.id === option.id);
      return {
        ...item,
        options: item.options.map((opt) =>
          opt.id === selectedOption.id
            ? { ...opt, selected: true }
            : { ...opt, selected: false }
        ),
      };
    });
    setData(newData);
  };

  const hanndleNext = () => {
    navigation.navigate("RedirectLS");
    dispatch(setWalkthrough(false));
  };
  return (
    <View style={styles.root}>
      <View style={{ marginTop: "3%" }}>
        <CustomHeader
          transparentView
          leftIconName="left-arrow"
          //   borderCircular
          backgroundColor={"#F1F5F9"}
          onLeftPress={() => {
            navigation.goBack();
          }}
          backBtn
        />
      </View>
      <ScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <View
          style={{
            flex: 1,
            padding: 20,
            alignItems: "center",
            marginTop: "5%",
          }}
        >
          {data.map((item, index) => (
            <View style={styles.container} key={index}>
              <View style={styles.questionContainer}>
                <Text style={styles.question}>{item.title}</Text>
              </View>
              {item.options.map((option, index) => (
                <TouchableOpacity
                  style={[
                    styles.buttonContainer,
                    option.selected && styles.selectedButton,
                  ]}
                  key={index}
                  onPress={() => handleChoose(option)}
                >
                  <Text
                    style={[
                      styles.buttonText,
                      option.selected
                        ? styles.selectedButtonText
                        : styles.unSelectedButtonText,
                    ]}
                  >
                    {option.name}
                  </Text>
                  {option.selected && (
                    <FIcon name="check" size={24} color={BaseColor.primary3} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          ))}
          <View
            style={{ width: "100%", alignItems: "center", marginTop: "15%" }}
          >
            <CButton
              style={{
                borderWidth: 1,
                borderColor: BaseColor.primary,
                backgroundColor: BaseColor.primary,
                height: 44,
                borderRadius: 29,
                width: "50%",
              }}
              title={translate("Next")}
              titleStyle={{
                fontFamily: FontFamily.robotoLight,
                fontWeight: "100",
                color: BaseColor.whiteColor,
                fontSize: 20,
                lineHeight: 20,
                letterSpacing: 0.75,
              }}
              onPress={hanndleNext}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

export default CampaignQuiz;
