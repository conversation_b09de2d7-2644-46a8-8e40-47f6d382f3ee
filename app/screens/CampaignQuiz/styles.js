import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
  },
  questionContainer: {
    marginBottom: '15%',
  },
  question: {
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
    fontSize: 18,
  },

  buttonContainer: {
    // borderWidth: 1,
    padding: '5%',
    marginBottom: '2.5%',
    borderRadius: 10,
    backgroundColor: 'rgba(135, 181, 179 , 0.15)',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  selectedButton: {
    borderWidth: 2,
    alignItems: 'center',
    padding: '3%',
    borderColor: BaseColor.primary3,
    backgroundColor: BaseColor.whiteColor,
  },
  buttonText: {
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
    fontSize: 16,
  },
});
export default styles;
