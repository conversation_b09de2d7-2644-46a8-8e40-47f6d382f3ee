/* eslint-disable indent */
/* eslint-disable quotes */
/* eslint-disable react/jsx-indent */
/* eslint-disable no-nested-ternary */
import React, {useEffect, useRef, useState} from 'react';
import {
  ActivityIndicator,
  BackHandler,
  FlatList,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  ImageBackground,
  Dimensions,
} from 'react-native';
import ActionButton from 'react-native-action-button';
import IoIcon from 'react-native-vector-icons/Ionicons';
import {isEmpty, isObject, isString, isArray} from 'lodash';
import {useDispatch, useSelector} from 'react-redux';
import {useTheme} from '@react-navigation/native';
import ImagePicker from 'react-native-image-crop-picker';
import DocumentPicker from 'react-native-document-picker';
import Toast from 'react-native-simple-toast';
import CHeader from '../../components/CHeader';
import GradientBack from '../../components/gradientBack';
import {CustomIcon} from '../../config/LoadIcons';
import {FontFamily, FontWeight} from '../../config/typography';
import styles from './styles';
import {translate} from '../../lang/Translate';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import {openInAppBrowser, sendErrorReport} from '../../utils/commonFunction';
import socketAction from '../../redux/reducers/socket/actions';
import AuthAction from '../../redux/reducers/auth/actions';
import CustomHeader from '../../components/CustomHeader/CustomHeader';
import {Images} from '../../config/Images';
import moment from 'moment';
import SubHeader from '../../components/SubHeader';

/**
 * @module Chat
 */
const ChatScreen = ({navigation}) => {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const {onReceive, emit} = socketAction;
  const {setLeadId, setNotiCount} = AuthAction;

  const dispatch = useDispatch();
  const accessToken = useSelector(state => state.auth.accessToken);
  const darkmode = useSelector(state => state.auth.darkmode);

  const [msgtext, setmsgtext] = useState('');
  const [chatArr, setchatArr] = useState([]);
  const [uploadLoader, setUploadLoader] = useState(false);
  const [inputFocus, setInputFocus] = useState(false);
  const [chatClosed, setChatClosed] = useState(false);
  const [chatLoader, setChatLoader] = useState(false);
  const chatFlatlistRef = useRef();
  const msgInputRef = useRef();

  const {chatData, typing, typingData} = useSelector(state => state.socket);

  const userData = useSelector(state => state.auth.userData);

  const leadId = useSelector(state => state.auth.leadId);

  useEffect(() => {
    // {"close": true}
    const tempArr = chatArr;
    setChatClosed(false);
    if (chatData.close) {
      setChatClosed(true);
      dispatch(setLeadId(null));
    } else {
      setChatClosed(false);
      chatArr.push(chatData);
    }
  }, [chatData]);

  useEffect(() => {
    console.log('TYPING****', typing);
    console.log('typingData', typingData);
  }, [typingData]);

  const userID = userData.id;
  const renderData = ({item, index}) => {
    return (
      <View style={{marginVertical: 12, marginRight: 12}}>
        <View
          style={{
            flexDirection: item?.sender_id == userID ? 'row-reverse' : 'row',
          }}>
          {item?.sender_id != userID ? (
            <Image
              source={
                item?.user?.avatar
                  ? item?.user?.avatar
                  : require('../../assets/images/6.jpg')
              }
              style={{height: 40, width: 40, borderRadius: 40}}
            />
          ) : null}
          <View
            style={{
              maxWidth: '85%',
              flexDirection: 'row',
              marginHorizontal: 20,
            }}>
            <Text
              style={{
                fontSize: 10,
                color: BaseColor.greyColor,
                alignSelf: 'flex-end',
              }}>
              {moment(item?.createdAt).format('hh:mma')}
            </Text>
            <View
              style={{
                padding: 12,
                backgroundColor:
                  item?.sender_id === userID ? '#EEF5F5' : BaseColor.primary,
                borderRadius: 16,
                borderBottomStartRadius: item?.sender_id === userID ? 16 : 0,
                borderBottomEndRadius: item?.sender_id === userID ? 0 : 16,
              }}>
              {item?.type === 'text' ? (
                <Text
                  style={{
                    fontFamily: FontFamily.regular,
                    fontSize: 17,
                    color: BaseColor.blackColor,
                    lineHeight: 20,
                  }}>
                  {item?.message}
                </Text>
              ) : item?.fileType &&
                isString(item?.fileType) &&
                !item?.fileType.includes('image') ? (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}>
                  <Text
                    style={{
                      fontFamily: FontFamily.default,
                      fontSize: 16,
                      color: BaseColor.blackColor,
                      flex: 1,
                      fontWeight: 'bold',
                      marginEnd: 4,
                    }}>
                    {item?.file_name}
                  </Text>
                  <TouchableOpacity
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    onPress={() => {
                      if (item?.file_url) {
                        openInAppBrowser(item?.file_url);
                      } else {
                        Toast.show("Can't open file");
                      }
                    }}>
                    <IoIcon
                      name="download-outline"
                      size={22}
                      color={BaseColor.blueDark}
                    />
                  </TouchableOpacity>
                </View>
              ) : (
                <TouchableOpacity
                  onPress={() => {
                    if (item?.file_url) {
                      openInAppBrowser(item?.file_url);
                    } else {
                      Toast.show("Can't open file");
                    }
                  }}
                  style={{
                    height: 120,
                    width: 100,
                    margin: -10,
                    borderRadius: 16,
                    overflow: 'hidden',
                    borderBottomStartRadius:
                      item?.sender_id === userID ? 16 : 0,
                    borderBottomEndRadius: item?.sender_id === userID ? 0 : 16,
                  }}>
                  <Image
                    source={{uri: item?.file_url}}
                    resizeMode="cover"
                    style={{height: '100%', width: '100%'}}
                  />
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
        {/* {item.type !== "msg" ? null : (
          <View
            style={{
              alignItems: "center",
              justifyContent: "center",
              flexDirection: "row",
            }}
          >
            <View
              style={{
                height: 2,
                flex: 1,
                backgroundColor: BaseColor.white40,
                marginHorizontal: 32,
              }}
            />
            <Text
              style={{
                backgroundColor: BaseColor.white50,
                color: BaseColor.whiteColor,
                borderRadius: 16,
                borderWidth: 1,
                borderColor: BaseColor.whiteColor,
                fontSize: 12,
                padding: 2,
                paddingHorizontal: 8,
                overflow: "hidden",
              }}
            >
              {item.time}
            </Text>
            <View
              style={{
                height: 2,
                flex: 1,
                backgroundColor: BaseColor.white40,
                marginHorizontal: 32,
              }}
            />
          </View>
        )} */}
      </View>
    );
  };

  useEffect(() => {
    // setchatArr([]);
    getChat();
  }, []);

  /** this function for get Badge Count
   * @function getBadgeCount
   * @param {object} data {}
   */
  async function getBadgeCount() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getUserAlertCount,
        'POST',
        {},
        headers,
      );

      if (response.success) {
        dispatch(setNotiCount(response));
      } else {
        console.log(
          '🚀 ~ file: index.js ~ line 15ewe ~ getBadgeCount ~ response',
          response,
        );
      }
    } catch (error) {
      console.log('error for device list ===', error);
      sendErrorReport(error, 'badge error chat screen');
    }
  }

  /** this function for get Chat
   * @function getChat
   * @param {object} data lead_id, page, per_page
   */
  const getChat = () => {
    const nData = leadId
      ? {
          lead_id: leadId,
          page: 1,
          per_page: 150,
        }
      : {
          page: 1,
          per_page: 150,
        };
    const queryString = new URLSearchParams(nData).toString();
    const url = BaseSetting.endpoints.getAppChat + `?${queryString}`;
    setChatLoader(true);
    getApiData(url, 'GET')
      .then(respose => {
        console.log('sendAttachment Response ====>>>>', respose);
        if (respose?.data) {
          setchatArr(respose?.data);
          getBadgeCount();
          setChatLoader(false);
        } else {
          setChatLoader(false);
          setchatArr([]);
        }
      })
      .catch(err => {
        setChatLoader(false);
        sendErrorReport(err, 'get_chat');
        console.log('sendAttachment ERROR', err);
      });
  };

  /** this function for send Typing
   * @function sendTyping
   * @param {object} data lead_id, receiver_id, stop
   */
  const sendTyping = stop => {
    const data = leadId
      ? {
          lead_id: leadId,
          receiver_id: userData.brand_id,
          stop,
        }
      : {
          receiver_id: userData.brand_id,
          stop,
        };
    dispatch(
      emit('/api/chat/chat-typing', data, callBackD => {
        const callBackData = callBackD;
        console.log('chat-typing ===', callBackData);
      }),
    );
  };

  /** this function for send Message
   * @function sendMsg
   * @param {object} data message, receiver_id
   */
  const sendMsg = () => {
    Keyboard.dismiss();
    if (!isEmpty(msgtext)) {
      const data = {
        message: msgtext,
        receiver_id: userData.brand_id,
      };
      console.log('sendMsg -> data', data);

      dispatch(
        emit('/api/chat/send-message', data, callBackD => {
          const callBackData = callBackD;
          console.log(
            'chillbaby ~ file: index.js ~ line 394 ~ sendMsg ~ data',
            data,
          );
          dispatch(setLeadId(callBackData?.data?.lead_id));
          isArray(chatArr) && chatArr.push(callBackData.data);
          // console.log(
          //   "chillbaby ~ file: index.js ~ line 282 ~ emit ~ tempArr",
          //   tempArr
          // );
          setmsgtext('');
        }),
      );
    }
  };

  function handleBackButtonClick() {
    if (chatData.close) {
      dispatch(onReceive({}));
    }
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  const clickImage = () => {
    ImagePicker.openCamera({
      cropping: true,
    }).then(image => {
      sendAttachment(image);
    });
  };

  const pickImage = () => {
    ImagePicker.openPicker({
      cropping: true,
    }).then(image => {
      sendAttachment(image);
    });
  };

  const pickDocument = async () => {
    try {
      const res = await DocumentPicker.pick({
        type: [DocumentPicker.types.pdf, DocumentPicker.types.doc],
      });
      console.log(
        res.uri,
        res.type, // mime type
        res.name,
        res.size,
      );
      const fileObj = {
        path: res.uri,
        mime: res.type,
      };

      sendAttachment(fileObj);
    } catch (err) {
      console.log('DocumentPicker Err', err);
      sendErrorReport(err, 'pick_doc');
      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker, exit any dialogs or menus and move on
      } else {
        throw err;
      }
    }
  };

  /** this function for upload Attachments
   * @function uploadAttachment
   * @param {object} data receiver_id
   */
  async function uploadAttachment(response) {
    console.log(
      'chillbaby ~ file: index.js ~ line 330 ~ uploadAttachment ~ response',
      response,
    );
    if (response && response.success) {
      const resParamsData = {
        receiver_id: userData.brand_id,
        // sender_id: userID,
        ...response,
      };
      console.log(
        'chillbaby ~ file: index.js ~ line 420 ~ uploadAttachment ~ resParamsData',
        resParamsData,
      );

      const headers = {
        'Content-Type': 'application/json',
        authorization: `Bearer ${accessToken}`,
      };

      try {
        const paramRes = await getApiData(
          BaseSetting.endpoints.insertAttachment,
          'post',
          resParamsData,
          headers,
        );
        console.log(
          'chillbaby ~ file: index.js ~ line 342 ~ uploadAttachment ~ paramRes',
          paramRes.data,
        );
        if (paramRes && paramRes.success) {
          getChat();
          setUploadLoader(false);
        } else {
          // notification('error', 'Unable to upload file');
        }
      } catch (error) {
        console.log('error upload attach ===', error);
        sendErrorReport(error, 'unable_upload_attachment');
      }
    } else {
      console.log('Unable to upload file');
      sendErrorReport(
        JSON.stringify({err: 'unable_upload_attachment'}),
        'unable_upload_attachment_else',
      );
    }
  }

  /** this function for send Attachment
   * @function sendAttachment
   * @param {object} data uri, name, type
   */
  const sendAttachment = async image => {
    setUploadLoader(true);
    const name =
      isObject(image) && image.path
        ? image.path.substring(image.path.lastIndexOf('/') + 1)
        : '';

    const formdata = new FormData();

    formdata.append('attachment', {
      uri: image.path,
      name,
      type: image.mime,
    });

    try {
      fetch(BaseSetting.api + BaseSetting.endpoints.uploadChatAttachment, {
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
        },
        body: formdata,
      })
        .then(response => response.json())
        .then(responseJson => {
          console.log(
            'chillbaby ~ file: index.js ~ line 368 ~ .then ~ responseJson',
            responseJson,
          );
          uploadAttachment(responseJson);
        })
        .catch(err => {
          sendErrorReport(err, 'upload_attachment');
          console.log('err ===', err);
        });
    } catch (err) {
      console.log(err);
      sendErrorReport(err, 'send_attach_catch');
    }
  };

  return (
    <>
      <StatusBar barStyle="dark-content" />
      <View style={[styles.root, {backgroundColor: BaseColor.whiteColor}]}>
        <SubHeader
          title={'Customer Support'}
          leftIconName="left-arrow"
          onLeftPress={() => {
            navigation.goBack();
          }}
          chat
        />
        <KeyboardAvoidingView
          style={{
            flex: 1,
            overflow: 'hidden',
          }}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
          <Image
            source={Images.chatBackground}
            style={{
              position: 'absolute',
              zIndex: -1,
              width: '100%',
              marginTop: Dimensions.get('window').height / 4,
              resizeMode: 'contain',
            }}
          />
          {chatLoader ? (
            <ActivityIndicator
              style={{flex: 1, justifyContent: 'center'}}
              color={BaseColor.primary}
            />
          ) : (
            <FlatList
              ref={chatFlatlistRef}
              data={chatArr}
              keyExtractor={item => item?.id}
              renderItem={renderData}
              contentContainerStyle={{
                flexGrow: 1,
                justifyContent: 'flex-end',
              }}
              showsVerticalScrollIndicator={false}
              onContentSizeChange={() =>
                chatFlatlistRef.current.scrollToEnd({animated: true})
              }
              onLayout={() =>
                chatFlatlistRef.current.scrollToEnd({animated: true})
              }
              maintainVisibleContentPosition={{
                minIndexForVisible: 0,
                autoscrollToTopThreshold: 10,
              }}
              removeClippedSubviews={false}
              maxToRenderPerBatch={10}
              updateCellsBatchingPeriod={50}
              initialNumToRender={10}
              onEndReachedThreshold={0.5}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>No messages yet</Text>
                </View>
              }
            />
          )}
          {uploadLoader ? (
            <View
              style={{
                width: '62%',
                alignSelf: 'flex-end',
                marginVertical: 12,
                paddingHorizontal: 12,
              }}>
              <View
                style={{
                  padding: 16,
                  backgroundColor: BaseColor.whiteColor,
                  marginHorizontal: 16,
                  borderRadius: 16,
                  borderBottomStartRadius: 16,
                  borderBottomEndRadius: 0,
                  flexDirection: 'row',
                }}>
                <Text>{'Sending...    '}</Text>
                <ActivityIndicator size={16} color={BaseColor.blueDark} />
              </View>
            </View>
          ) : null}
          {!isEmpty(typingData) && !typingData.stop ? (
            <Text
              style={{
                color: BaseColor.typingText,
                alignSelf: 'center',
                marginBottom: 8,
                fontSize: 18,
                fontFamily: FontFamily.default,
                fontWeight: FontWeight.bold,
                // marginEnd: 24,
              }}>
              {typingData.user_name} is typing...
            </Text>
          ) : null}
          {!chatClosed ? (
            <>
              <View
                style={{
                  flexDirection: 'row',
                  paddingHorizontal: 16,
                  marginBottom: 15,
                }}>
                <View
                  style={{
                    flex: 1,
                    marginStart: 48,
                    backgroundColor: 'rgba(35, 35, 35, 0.05)',
                    borderRadius: 30,
                  }}>
                  <TextInput
                    ref={msgInputRef}
                    placeholder={translate('chatInputText')}
                    placeholderTextColor={BaseColor.blackColor}
                    multiline
                    style={{
                      textAlignVertical: 'center',
                      paddingLeft: 20,
                      paddingRight: 50,
                      fontFamily: FontFamily.regular,
                      height: 55,
                      ...Platform.select({
                        ios: {
                          paddingTop: 20, // Adjust this as per your needs for iOS
                        },
                      }),
                    }}
                    maxLength={500}
                    value={msgtext}
                    onChangeText={val => {
                      setmsgtext(val);
                      sendTyping(false);
                      setTimeout(() => {
                        sendTyping(true);
                      }, 700);
                    }}
                  />
                </View>

                <TouchableOpacity
                  activeOpacity={0.7}
                  onPress={sendMsg}
                  style={{
                    position: 'absolute',
                    right: 25,
                    top: 8,
                    backgroundColor: BaseColor.primary,
                    borderRadius: 40,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <CustomIcon
                    name="send"
                    color={BaseColor.whiteColor}
                    size={20}
                    style={{
                      padding: 10,
                    }}
                  />
                </TouchableOpacity>
              </View>
              {!uploadLoader ? (
                <ActionButton
                  buttonColor={'#ffffff00'}
                  buttonTextStyle={{
                    color: BaseColor.primary,
                    width: 35,
                    height: 35,
                    borderWidth: 1.5,
                    borderRadius: 18,
                    borderColor: BaseColor.primary,
                    textAlign: 'center',
                    marginBottom: 10,
                  }}
                  size={35}
                  position="left"
                  offsetX={25}
                  offsetY={20}>
                  <ActionButton.Item
                    buttonColor={BaseColor.primary}
                    onPress={clickImage}>
                    <IoIcon
                      name="camera-outline"
                      color={BaseColor.whiteColor}
                      size={20}
                    />
                  </ActionButton.Item>
                  <ActionButton.Item
                    buttonColor={BaseColor.primary}
                    onPress={pickImage}>
                    <IoIcon
                      name="images-outline"
                      color={BaseColor.whiteColor}
                      size={20}
                    />
                  </ActionButton.Item>
                  <ActionButton.Item
                    buttonColor={BaseColor.primary}
                    onPress={pickDocument}>
                    <IoIcon
                      name="documents-outline"
                      color={BaseColor.whiteColor}
                      size={20}
                    />
                  </ActionButton.Item>
                </ActionButton>
              ) : (
                <ActivityIndicator
                  style={{position: 'absolute', bottom: 24, left: 12}}
                  color={BaseColor.alertRed}
                  size={32}
                />
              )}
            </>
          ) : (
            <View
              style={{
                flexDirection: 'row',
                padding: 8,
                paddingHorizontal: 16,
                backgroundColor: BaseColor.whiteColor,
                alignItems: 'center',
                borderTopEndRadius: 48,
                paddingTop: 22,
              }}>
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: FontFamily.default,
                  textAlign: 'center',
                  textAlignVertical: 'center',
                  flex: 1,
                  marginHorizontal: 16,
                  fontWeight: 'bold',
                  marginBottom: 8,
                }}>
                {translate('ticketClosed')}
              </Text>
            </View>
          )}
        </KeyboardAvoidingView>
      </View>
    </>
  );
};

export default ChatScreen;
