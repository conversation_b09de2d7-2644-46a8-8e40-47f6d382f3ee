/* eslint-disable max-len */
/* eslint-disable arrow-body-style */
/* eslint-disable eqeqeq */
/* eslint-disable global-require */
/* eslint-disable operator-linebreak */
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  BackHandler,
  Platform,
} from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import Toast from 'react-native-simple-toast';
import {useDispatch, useSelector} from 'react-redux';
import RNFetchBlob from 'react-native-blob-util';
import {findIndex, isEmpty, isObject} from 'lodash';
import {useFocusEffect, useTheme} from '@react-navigation/native';
import CInput from '../../components/CInput';
import styles from './styles';
import {CustomIcon} from '../../config/LoadIcons';
import {translate} from '../../lang/Translate';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {
  checkFileVal,
  enableAnimateInEaseOut,
  sendErrorReport,
} from '../../utils/commonFunction';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';
import AuthAction from '../../redux/reducers/auth/actions';
import ChildProfilePopup from '../../components/ChildProfilePopup';
import SubHeader from '../../components/SubHeader';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import CButton from '../../components/CButton';
import moment from 'moment';

/**
 *
 *@module ChildDetail
 *
 */
const ChildInfo = ({navigation, route}) => {
  const childID = route?.params?.item?.id;
  const {type, item} = route?.params;
  const dispatch = useDispatch();
  const {characteristicID, serviceID, connectedDeviceDetails} = useSelector(
    state => state.bluetooth,
  );
  const languageData = useSelector(state => state.language);
  const {setDeviceSettingOpen, setStep7, setOnboardingDone} = AuthAction;
  const userData = useSelector(state => state.auth.userData);
  const step7ChildInfo = useSelector(state => state.auth.step7ChildInfo);
  const {closeOnboarding} = useSelector(state => state.auth);
  const colors = useTheme();
  const BaseColor = colors.colors;
  const token = useSelector(state => state.auth.accessToken);
  const [state, setstate] = useState({
    isImage: item?.child_profile || '',
    nickName: item?.nick_name || '',
    height: '',
    weight: '',
    dob: item?.date_of_birth
      ? new Date(item?.date_of_birth).toLocaleString()
      : new Date(),
    id: item?.id || null,
    conatactname: '',
    conatactnumber: '',
    secondConatactnumber: '',
    selectedCountry: userData?.country_code || 'US',
    secondSelectedCountry: userData?.country_code || 'US',
    countryCode: userData?.phone_code || '1',
    secondCountryCode: userData?.phone_code || '1',
    imageBase64: '',
  });
  const [selectDate, setSelectDate] = useState(
    item?.date_of_birth ? item?.date_of_birth : new Date(),
  );
  const stateRef = useRef(null);
  stateRef.current = state;
  const [cardData, setCardData] = useState([
    {
      type: 'add',
    },
  ]);

  const [nickNameError, setNickNameError] = useState(false);
  const [dobError, setDobError] = useState(false);
  const [photoLoading, setphotoLoading] = useState(false);
  const [nickNameErrorTxt, setNickNameErrorTxt] = useState('');
  const [dobErrorTxt, setDobErrorTxt] = useState('');
  const [loader, setloader] = useState(false);
  const nameRef = useRef();
  const [onbordingPopup, setonbordingPopup] = useState(false);

  useEffect(() => {
    if (
      type === 'connect' &&
      !step7ChildInfo &&
      !closeOnboarding &&
      isEmpty(connectedDeviceDetails)
    ) {
      sendErrorReport('step7', 'step7_child_info');
      // setShowStep7(true); // m
    }
  }, []);
  useEffect(() => {
    if (type === 'Otp') {
      // setonbordingPopup(true);
    } else if (
      type === 'connect' &&
      !step7ChildInfo &&
      !closeOnboarding &&
      isEmpty(connectedDeviceDetails)
    ) {
      sendErrorReport('step7', 'step7_child_info2');
      // setShowStep7(true); // m
    }
  }, []);
  const Validation = () => {
    enableAnimateInEaseOut();
    const numVal = /^[0-9]+$/;
    if (isEmpty(state.nickName)) {
      allErrorFalse();
      setNickNameError(true);
      setNickNameErrorTxt(translate('enterNickname'));
    } else if (selectDate == '') {
      allErrorFalse();
      setDobError(true);
      setDobErrorTxt(translate('selectDOB'));
    } else if (state.isImage == '') {
      Toast.show(translate('addImage'));
    } else {
      allErrorFalse();
      childProfile();
    }
  };

  const allErrorFalse = () => {
    setNickNameError(false);
    setDobError(false);
  };

  // this function for add use product
  /** this function for add use product
   * @function addUserProduct
   * @param {object} data child_id, device_id, product_id, service_id, characteristic_id, type, platform
   */
  async function addUserProduct(data) {
    const obj = {
      child_id: data?.id,
      device_id: route?.params?.device_id,
      product_id: route?.params?.product_id,
      device_data: route?.params?.device_data,
      device_ssid: route?.params?.device_ssid,
      service_id: serviceID,
      characteristic_id: characteristicID,
      type: 'type 1',
      platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
    };
    sendErrorReport(obj, 'add_user_prod_obj');
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.addUserProduct,
        'POST',
        obj,
        headers,
      );
      sendErrorReport(response, 'add_user_prod_response');
      if (response.success) {
        setstate({id: null});
        dispatch(setOnboardingDone(true));
        sendErrorReport(response, 'add_user_prod_response2');
        navigation.navigate('DrawerNav');
      }
    } catch (error) {
      console.log('error ===', error);
      sendErrorReport(error, 'add_user_prod');
    }
  }

  /** this function for add/update child Profile
   * @function childProfile
   * @param {object} data nick_name, date_of_birth, height, weight, gender, child_profile, emergency_name, emergency_phone, emergency_phone_code, country
   */
  const childProfile = () => {
    setloader(true);
    const data = {
      nick_name: state.nickName,
      date_of_birth: moment(selectDate).format('DD-MM-YYYY') || '',
      child_profile: state?.imageBase64 || state?.isImage,
      platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
      lang_code: languageData?.languageData || 'en',
      brand_name: 'Qeridoo',
      app_name: 'qeridoo',
    };

    if (state?.id) {
      data.child_id = state?.id;
    }

    if (route?.params?.device_id) {
      data.device_id = route?.params?.device_id;
    }
    if (route?.params?.product_id) {
      data.product_id = route?.params?.product_id;
    }
    if (route?.params?.device_data) {
      data.device_data = route?.params?.device_data;
    }

    if (route?.params?.device_ssid) {
      data.device_ssid = route?.params?.device_ssid;
    }

    let url = BaseSetting.endpoints.childProfile;
    if (state?.id) {
      url = BaseSetting.endpoints.updateChildProfile;
    }
    getApiData(url, 'POST', data, '', true)
      .then(response => {
        if (response.success) {
          setstate({
            isImage: '',
            nickName: '',
            age: '',
            about: '',
            height: '',
            weight: '',
            imageBase64: '',
            conatactname: '',
            conatactnumber: '',
            secondConatactnumber: '',
            selectedCountry: userData?.country_code || 'US',
            secondSelectedCountry: userData?.country_code || 'US',
            countryCode: userData?.phone_code || '1',
            secondCountryCode: userData?.phone_code || '1',
          });
          if (isObject(response.data) && !isEmpty(response.data)) {
            dispatch(BluetoothAction.setActiveChildDetail(response.data));
          }

          if (route?.params?.product_id && type === 'connect') {
            if (isObject(response.data) && !isEmpty(response.data)) {
              addUserProduct(response.data);
              // dispatch(BluetoothAction.setActiveDeviceId(response.data));
            }
          } else {
            if (type === 'Otp') {
              dispatch(setDeviceSettingOpen(true));
            }
            navigation.replace('Devices', {type: type});
          }
          Toast.show(response.message);
        } else {
          Toast.show(response.message);
        }
        setloader(false);
      })
      .catch(err => {
        console.log('ERRR', err);
        Toast.show('Something went wrong! Unable to save child profile');
        sendErrorReport(err, 'add_update_child_profile_1');
        setloader(false);
      });
  };

  useFocusEffect(
    useCallback(() => {
      setCardData([
        {
          type: 'add',
        },
      ]);
      // getChildInfo();
    }, []),
  );

  const image = () => {
    setphotoLoading(true);
    ImagePicker.openPicker({
      width: 250,
      height: 250,
      cropping: true,
    })
      .then(img => {
        const fType = img?.mime || '';
        const isValidFile = checkFileVal(fType, img.size);
        if (isValidFile) {
          RNFetchBlob.fs
            .readFile(img.path, 'base64')
            .then(data => {
              const newstate = {
                ...stateRef?.current,
                isImage: img.path,
                imageBase64: `data:image/png;base64,${data}`,
              };
              setstate(newstate);
              setTimeout(() => {
                setphotoLoading(false);
              }, 2000);
            })
            .catch(err => {
              sendErrorReport(err, 'image_fs');
              setphotoLoading(false);
            });
        }
      })
      .catch(() => {
        setphotoLoading(false);
      });
  };

  function handleBackButtonClick() {
    if (type !== 'Otp') {
      navigation.navigate('DrawerNav');
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={{flex: 1, backgroundColor: BaseColor.whiteColor}}>
      <SubHeader
        title={
          type === 'edit' ? translate('editProfile') : translate('addNewChild')
        }
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <KeyboardAwareScrollView
        bounces={false}
        contentContainerStyle={{
          flexGrow: 1,
          paddingHorizontal: 20,
        }}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={false}
        showsVerticalScrollIndicator={false}>
        <View style={{paddingBottom: 24}}>
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              alignSelf: 'center',
              width: 128,
              height: 128,
              borderRadius: 100,
              backgroundColor: '#D0D8D8',
            }}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={image}
              style={{
                width: 40,
                height: 40,
                backgroundColor: BaseColor.primary,
                position: 'absolute',
                zIndex: 1,
                right: 10,
                bottom: -5,
                borderRadius: state?.isImage ? 15 : 30,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <CustomIcon
                name={state?.isImage ? 'pencil-2' : 'camera'}
                size={state?.isImage ? 20 : 24}
                color={BaseColor.whiteColor}
              />
            </TouchableOpacity>
            {state?.isImage && (
              <Image
                style={styles.selectedImage}
                source={{uri: state?.isImage}}
              />
            )}
          </View>
        </View>
        <View style={[styles.infoView]}>
          <Text style={[styles.infoText]}>{translate('Details')}</Text>
          <View
            style={{
              borderBottomWidth: 1,
              borderColor: '#E8E8E8',
              marginBottom: 10,
            }}>
            <CInput
              ref={nameRef}
              placeholder={translate('Enter Name Here')}
              placeholderTextColor={BaseColor.textGrey}
              textInputWrapper={{
                borderWidth: 0,
              }}
              inputStyle={{}}
              value={state.nickName}
              onChangeText={val => {
                setstate({...state, nickName: val});
              }}
              showError={nickNameError}
              errorMsg={nickNameErrorTxt}
            />
          </View>
          <View style={{borderBottomWidth: 1, borderColor: '#E8E8E8'}}>
            <CInput
              datePicker
              ref={nameRef}
              placeholderTextColor={BaseColor.textGrey}
              textInputWrapper={{
                borderWidth: 0,
              }}
              inputStyle={{
                justifyContent: 'center',
                paddingHorizontal: 10,
              }}
              maxDate={new Date()}
              mode="date"
              value={selectDate ? selectDate : new Date()}
              onDateChange={val => {
                setSelectDate(val);
              }}
              showError={dobError}
              errorMsg={dobErrorTxt}
            />
          </View>
          <View style={{marginTop: '30%'}}>
            <CButton
              style={{width: '80%'}}
              title={translate('saveChanges')}
              onPress={() => {
                Validation();
              }}
              loader={loader}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>

      <Modal
        visible={loader}
        transparent
        style={{
          flex: 1,
        }}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: BaseColor.black30,
          }}>
          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              justifyContent: 'center',
              alignItems: 'center',
              padding: 24,
              paddingHorizontal: 32,
              borderRadius: 24,
            }}>
            <ActivityIndicator size={24} color={BaseColor.blueDark} />
            <Text
              style={{
                color: BaseColor.blackColor,
                fontWeight: 'bold',
                marginTop: 8,
              }}>
              Loading
            </Text>
          </View>
        </View>
      </Modal>
      <ChildProfilePopup
        visible={onbordingPopup}
        image={require('../../assets/images/childPopup.png')}
        onClose={() => {
          setonbordingPopup(false);
        }}
      />
    </View>
  );
};

export default ChildInfo;
