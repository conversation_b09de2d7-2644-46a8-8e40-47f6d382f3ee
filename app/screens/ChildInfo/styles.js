import {StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  chooseProfile: {
    // paddingBottom: 20,
    fontSize: 12,
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.default,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  imageLastShadow: {
    marginTop: 10,
    backgroundColor: BaseColor.card2,
    justifyContent: 'center',
    width: 172,
    height: 268,
    alignSelf: 'center',
    borderRadius: 20,
  },
  imagemiddleShadow: {
    marginBottom: 20,
    backgroundColor: BaseColor.card1,
    justifyContent: 'center',
    width: 212,
    height: 260,
    alignSelf: 'center',
    borderRadius: 20,
  },
  imageView: {
    backgroundColor: BaseColor.whiteColor,
    width: 250,
    height: 250,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    // borderColor: BaseColor.textGrey,
    // borderWidth: 1
  },
  selectedImage: {
    width: '100%',
    height: '100%',
    borderRadius: 100,
  },
  imageText: {
    color: BaseColor.blueDark,
    fontSize: 18,
    textAlign: 'center',
    paddingTop: 26,
    fontWeight: 'bold',
    fontFamily: FontFamily.default,
  },
  imageText1: {
    color: BaseColor.textGrey,
    fontSize: 12,
    textAlign: 'center',
  },
  infoView: {
    backgroundColor: BaseColor.whiteColor,
    borderTopLeftRadius: 22,
    borderTopRightRadius: 22,
    paddingHorizontal: 10,
  },
  horizontalLine: {
    width: 40,
    height: 4,
    backgroundColor: BaseColor.black30,
    position: 'absolute',
    alignSelf: 'center',
    top: 18,
    borderRadius: 3,
  },
  infoText: {
    color: BaseColor.blackColor,
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 20,
    fontFamily: FontFamily.bold,
  },
  textInputView: {
    marginBottom: 8,
  },
  iconView: {
    alignSelf: 'center',
    borderRadius: 150,
    justifyContent: 'center',
    alignItems: 'center',
  },
  genderIcon: {
    textAlign: 'center',
    alignSelf: 'center',
    textAlignVertical: 'center',
    borderRadius: 75,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: BaseColor.whiteColor,
    padding: 16,
    borderColor: BaseColor.whiteColor,
    borderWidth: 4,
  },
  selectedCheck: {
    width: 24,
    height: 24,
    position: 'absolute',
    bottom: 20,
    right: 0,
    borderColor: BaseColor.whiteColor,
    borderWidth: 3,
    textAlign: 'center',
    alignSelf: 'center',
    textAlignVertical: 'center',
    borderRadius: 75,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    padding: 4,
  },
  genderName: {
    fontSize: 14,
    textAlign: 'center',
    fontFamily: FontFamily.default,
    marginTop: 4,
    color: BaseColor.whiteColor,
    fontWeight: 'bold',
  },
  cardStyle: {
    flexWrap: 'wrap',
    overflow: 'hidden',
    height: 250,
    width: 250,
    borderRadius: 20,
    backgroundColor: BaseColor.blueDark,
  },
  containerStyle: {
    backgroundColor: BaseColor.red,
    borderRadius: 20,
  },
});

export default styles;
