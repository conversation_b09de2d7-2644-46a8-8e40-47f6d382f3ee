/* eslint-disable quotes */
import React, { useEffect, useState } from "react";
import {
  Text,
  View,
  StatusBar,
  TouchableOpacity,
  BackHandler,
  Platform,
  ActivityIndicator,
} from "react-native";
import Toast from "react-native-simple-toast";
import { useTheme } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import styles from "./styles";
import { CustomIcon } from "../../config/LoadIcons";
import { translate } from "../../lang/Translate";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import { sendErrorReport } from "../../utils/commonFunction";
import AuthAction from "../../redux/reducers/auth/actions";

const Connect = ({ navigation, route }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const dispatch = useDispatch();
  const token = useSelector((state) => state.auth.accessToken);
  const { isConnecting } = useSelector((state) => state.bluetooth);
  const [showStep6, setShowStep6] = useState(false);

  const { setStep6Done } = AuthAction;
  const { step6Done, closeOnboarding } = useSelector((state) => state.auth);

  function handleBackButtonClick() {
    Toast.show("Can't go back!");
    return true;
  }

  useEffect(() => {
    if (!step6Done && !closeOnboarding) {
      setShowStep6(true);
    }
  }, []);

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    // getChildInfo();
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  const [devices, setDevices] = useState([
    {
      type: "add",
    },
  ]);

  /** this function for get Child Information
   * @function getChildInfo
   * @param {object} data {}
   */
  const getChildInfo = () => {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };
    console.log("getChildInfo -> headers", headers);

    getApiData(
      BaseSetting.endpoints.getUserChild,
      "POST",
      {
        platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
      },
      headers
    )
      .then((response) => {
        if (response.success) {
          const tempArr = [
            {
              type: "add",
            },
          ];
          const childArr = response.data;
          childArr.map((item) => {
            tempArr.unshift(item);
          });

          setDevices(tempArr);
        } else {
          Toast.show(response.message);
        }
      })
      .catch((err) => {
        // console.log("ERRR", err);
        Toast.show("Something went wrong while getting child detail");
        sendErrorReport(err, "get_child_in_device2");
      });
  };

  return (
    <View style={styles.root}>
      {/* <GradientBack /> */}
      <View style={styles.root}>
        <StatusBar backgroundColor="transparent" barStyle="dark-content" />
        <View
          style={[styles.imageView, { backgroundColor: BaseColor.whiteColor }]}
        >
          <CustomIcon
            name="smartphone"
            size={100}
            color={BaseColor.primary}
            style={styles.deviceIcon}
          />
        </View>

        <Text style={[styles.connectText, { color: BaseColor.blackColor }]}>
          {isConnecting ? translate("connecting") : translate("connectScreen")}
        </Text>
        <Text style={[styles.otherText, { color: BaseColor.blackColor }]}>
          {isConnecting ? "" : translate("connectSuccessText")}
        </Text>

        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => {
            if (!isConnecting) {
              // navigation.navigate("ChildInfo", {
              //   type: "connect",
              //   product_id: route?.params?.product_id,
              //   device_id: route?.params?.device_id,
              //   item: devices[0],
              //   device_data: route?.params?.device_data,
              //   device_ssid: route?.params?.device_ssid,
              // });

              navigation.navigate(translate("home"));
            }
          }}
          style={[styles.checkIconView, { backgroundColor: BaseColor.primary }]}
        >
          {isConnecting ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <CustomIcon
              name="check"
              size={20}
              color={BaseColor.whiteColor}
              style={styles.checkIcon}
            />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default Connect;
