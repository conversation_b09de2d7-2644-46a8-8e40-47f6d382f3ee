import React, {useState} from 'react';
import {useEffect} from 'react';
import {
  BackHandler,
  Dimensions,
  Image,
  Keyboard,
  Modal,
  Text,
  TouchableOpacity,
} from 'react-native';
import {View} from 'react-native-animatable';
import styles from './styles';
import {FontFamily} from '../../config/typography';
import Toast from 'react-native-simple-toast';
import {CustomIcon} from '../../config/LoadIcons';
import BaseColor from '../../config/colors';
import CInput from '../../components/CInput';
import CButton from '../../components/CButton';
import {translate} from '../../lang/Translate';
import CustomHeader from '../../components/CustomHeader/CustomHeader';
import Swiper from 'react-native-swiper';
import {Images} from '../../config/Images';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DocumentPicker from 'react-native-document-picker';
import {sendErrorReport} from '../../utils/commonFunction';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {FlatList} from 'react-native-gesture-handler';
let backPressed = 0;

const {width, height} = Dimensions.get('window');
function ConnectedProductInfo({navigation, route}) {
  const {type} = route?.params;
  const [barcodeVal, setBarcodeVal] = useState('');
  const [loader, setLoader] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  // this for Swipeable Error.
  const staticArr = [
    {
      id: 1,
      image: Images.garantiekarte,
      title: translate('WarrantyCard'),
      content: translate('WarrantyContent'),
    },
    {
      id: 2,
      image: Images.karton,
      title: translate('Packaging'),
      content: translate('PackageContent'),
    },
    {
      id: 3,
      image: Images.rahmen,
      title: translate('FrameBody'),
      content: translate('FrameBodyContent'),
    },
    {
      id: 4,
      image: Images.warninglabel,
      title: translate('SafetyInstructions'),
      content: translate('SafetyContent'),
    },
  ];

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  const uploadDoc = file => {
    const fileData = {
      type: file.type,
      name: file.name,
      uri: file.uri,
    };
  };

  const pickDocument = async () => {
    try {
      const res = await DocumentPicker.pick({
        type: [DocumentPicker.types.pdf],
      });
      uploadDoc(...res);
    } catch (err) {
      console.log('DocumentPicker Err', err);
      sendErrorReport(err, 'pick_doc');
      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker, exit any dialogs or menus and move on
      } else {
        throw err;
      }
    }
  };

  const onGetTrackerDetail = async () => {
    setLoader(true);
    try {
      const response = await getApiData(
        BaseSetting.endpoints.getTrailerDetail + `?ean_barcode=${barcodeVal}`,
        'GET',
      );
      if (response.success) {
        setLoader(false);
        navigation.navigate('ProductDetails', {
          type: response?.data?.trailer_type,
          detail: response?.data,
        });
      } else {
        setLoader(false);
        Toast.show(response?.message);
      }
    } catch (err) {
      setLoader(false);
      console.log('ERRR==', err);
    }
  };

  const renderModal = ({item, index}) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          backgroundColor: BaseColor.whiteColor,
          margin: 3,
          width: width / 2.3,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: 10,
        }}>
        <Image
          source={item.image}
          style={{width: width / 6, height: height / 7}}
          resizeMode="contain"
        />
        <View style={{width: width / 4}}>
          <Text
            style={{
              fontSize: 14,
              fontFamily: FontFamily.regular,
              color: BaseColor.blackColor,
              paddingVertical: 10,
            }}>
            {item.title}
          </Text>
          <Text
            style={{
              fontSize: 10,
              fontFamily: FontFamily.regular,
              color: BaseColor.blackColor,
              lineHeight: 13,
              padding: 4,
            }}>
            {item.content}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.root]}>
      <CustomHeader
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
        leftIconColor={BaseColor.blackColor}
        backBtn
        title={
          type === 'camera'
            ? translate('CameraInfo')
            : type === 'tracker'
            ? translate('TrackerInfo')
            : translate('TrailerInfo')
        }
      />
      <KeyboardAwareScrollView style={{flexGrow: 1}}>
        <View style={{marginTop: 20, marginHorizontal: 20}}>
          <Text style={styles.guidelinetxt}>
            Open guidelines and references to locate serial number at ease
          </Text>
          <View style={{marginTop: 20}}>
            <Text style={{fontSize: 12, fontFamily: FontFamily.regular}}>
              Enter Serial Number or Scan Barcode image
            </Text>
            <View style={{flexDirection: 'row'}}>
              <View style={{width: '80%'}}>
                <CInput
                  textInputWrapper={styles.textInputStyle}
                  placeholder={'Type number or Scan code'}
                  onChangeText={val => setBarcodeVal(val)}
                  value={barcodeVal}
                  placeholderTextColor={'#CEE3E3'}
                  inputStyle={{
                    color: BaseColor.primary,
                  }}
                />
              </View>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() =>
                  navigation.navigate('QRScanner', {from: 'productInfo'})
                }
                style={styles.cameraContainer}>
                <CustomIcon name="camera" size={35} color={BaseColor.primary} />
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.swipableContainer}>
            <Swiper
              showsButtons={true}
              activeDotColor={'#87B5B3'}
              buttonWrapperStyle={{
                paddingHorizontal: 30,
              }}
              prevButton={
                <CustomIcon
                  name="arrow-right"
                  style={{transform: [{rotate: '180deg'}]}}
                  size={20}
                  color={BaseColor.primary}
                />
              }
              nextButton={
                <CustomIcon
                  name="arrow-right"
                  size={20}
                  color={BaseColor.primary}
                />
              }>
              {staticArr &&
                staticArr.map(li => {
                  return (
                    <TouchableOpacity
                      activeOpacity={0.7}
                      onPress={() => setModalOpen(true)}
                      style={{
                        flexDirection: 'row',
                      }}>
                      <Image
                        source={li.image}
                        style={{width: width / 2, height: height / 4}}
                        resizeMode="contain"
                      />
                      <View style={{width: '45%', padding: 10}}>
                        <Text style={styles.header}>{li.title}</Text>
                        <Text style={styles.containt}>{li.content}</Text>
                      </View>
                    </TouchableOpacity>
                  );
                })}
            </Swiper>
          </View>
          {type === 'trailer' && (
            <>
              <View style={{marginTop: 20}}>
                <Text style={{fontSize: 12, fontFamily: FontFamily.regular}}>
                  Upload Invoice
                </Text>
                <View>
                  <CInput
                    editable={false}
                    textInputWrapper={styles.textInputStyle}
                    placeholder={'Choose File'}
                    placeholderTextColor={'#CEE3E3'}
                    onFocus={() => {
                      Keyboard.dismiss();
                      pickDocument();
                    }}
                    inputStyle={{
                      color: BaseColor.primary,
                    }}
                  />
                </View>
              </View>
              <View style={{marginTop: 10}}>
                <Text style={{fontSize: 12, fontFamily: FontFamily.regular}}>
                  Choose Bike model
                </Text>
                <View>
                  <CInput
                    editable={false}
                    textInputWrapper={styles.textInputStyle}
                    placeholder={'Search Model Name Here'}
                    placeholderTextColor={'#CEE3E3'}
                    inputStyle={{
                      color: BaseColor.primary,
                    }}
                  />
                </View>
              </View>
            </>
          )}
        </View>
        <View
          style={{
            marginTop: type === 'trailer' ? height / 12 : height / 4,
          }}>
          <CButton
            loader={loader}
            style={{width: '80%'}}
            title={translate('+ Add Product')}
            onPress={() => {
              onGetTrackerDetail();
            }}
          />
        </View>
      </KeyboardAwareScrollView>
      <Modal
        style={{flex: 1}}
        visible={modalOpen}
        transparent
        animationType="slide"
        onRequestClose={() => {
          setModalOpen(false);
        }}>
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => {
            setModalOpen(false);
          }}
          style={{
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.83)',
            justifyContent: 'center',
          }}>
          <View
            style={{
              alignSelf: 'center',
              width: '90%',
              alignItems: 'center',
              backgroundColor: '#EEEFF1',
              paddingVertical: 10,
            }}>
            <Text
              style={{
                color: BaseColor.primary,
                fontFamily: FontFamily.regular,
                fontSize: 20,
                paddingBottom: 10,
              }}>
              Where can I find the serial number?
            </Text>
            <FlatList
              data={staticArr}
              renderItem={renderModal}
              numColumns={2}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

export default ConnectedProductInfo;
