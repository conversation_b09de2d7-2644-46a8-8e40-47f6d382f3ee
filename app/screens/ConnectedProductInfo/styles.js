import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const {width, height} = Dimensions.get('window');
const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
  },
  header: {
    fontSize: 16,
    fontFamily: FontFamily.bold,
    paddingBottom: 5,
    color: BaseColor.blackColor,
  },
  containt: {
    fontSize: 12,
    fontFamily: FontFamily.regular,
    lineHeight: 18,
    color: BaseColor.blackColor,
  },
  swipableContainer: {
    width: width / 1.11,
    height: height / 4,
    borderWidth: 2,
    borderColor: '#E1F1F1',
    borderRadius: 8,
    marginTop: 10,
  },
  guidelinetxt: {
    fontSize: 12,
    fontFamily: FontFamily.default,
    lineHeight: 15,
    color: '#87B5B3',
    textDecorationLine: 'underline',
  },
  cameraContainer: {
    height: 57,
    borderWidth: 1,
    width: '17%',
    marginLeft: 10,
    marginTop: 10,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    borderColor: '#CEE3E3',
  },
  textInputStyle: {
    borderRadius: 6,
    height: 57,
    justifyContent: 'center',
  },
});
export default styles;
