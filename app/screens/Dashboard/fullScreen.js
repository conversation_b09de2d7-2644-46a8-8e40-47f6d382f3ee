/* eslint-disable quotes */
/* eslint-disable no-unused-expressions */
import React, {useRef, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Alert,
} from 'react-native';

import {useTheme} from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import WebView from 'react-native-webview';
import Icon from 'react-native-vector-icons/FontAwesome5';
import {OrientationLocker, LANDSCAPE} from 'react-native-orientation-locker';
import MapView from 'react-native-maps';
import {FontFamily} from '../../config/typography';
import {isLandscape} from 'react-native-device-info';
import BaseColors from '../../config/colors';
/**
 *
 *@module FullScreen
 *
 */
function FullScreen({route, navigation}) {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const [onTap, setOnTap] = useState(false);
  const [onFirstTap, setFirstTap] = useState(true);
  const loc = route?.params?.loc;
  const {height: dHeight, width: dWidth} = Dimensions.get('window');
  const webViewRef = useRef();
  return (
    <View style={{}}>
      <View
        style={{
          height: Dimensions.get('window').width,
          backgroundColor: BaseColor.whiteColor,
        }}>
        <OrientationLocker orientation={LANDSCAPE} />
        <View
          style={{
            backgroundColor: BaseColor.alertRed,
            width: 73,
            height: 22,
            borderRadius: 17,
            position: 'absolute',
            top: onTap
              ? dHeight * 0.47
              : onFirstTap
              ? dWidth * 0.47
              : dHeight * 0.47,
            right: 28,
            zIndex: 99999999999,
            flexDirection: 'row',
            justifyContent: 'space-evenly',
            alignItems: 'center',
          }}>
          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              height: 10,
              width: 10,
              borderRadius: 5,
            }}
          />
          <Text style={{color: BaseColor.whiteColor}}>Live</Text>
        </View>
        <View
          style={{
            backgroundColor: 'rgba(119, 117, 117, 0.7)',
            height: 84,
            opacity: 0.9,
            flexDirection: 'row',
            justifyContent: 'space-between',
            position: 'absolute',
            top: onTap
              ? dHeight * 0.7
              : onFirstTap
              ? dWidth * 0.7
              : dHeight * 0.7,
            // bottom: 20,
            right: 0,
            zIndex: 99999999,
            paddingHorizontal: 20,
            alignItems: 'center',
            borderTopStartRadius: 30,
            borderBottomStartRadius: 30,
            borderWidth: 2,
            borderColor: BaseColor.primary,
          }}>
          <View style={{alignItems: 'center'}}>
            <Text
              style={{
                fontFamily: FontFamily.regular,
                color: BaseColor.whiteColor,
              }}>
              Distance (km)
            </Text>
            <Text
              style={{
                fontFamily: FontFamily.regular,
                color: BaseColor.whiteColor,
                marginTop: 15,
              }}>
              -
            </Text>
          </View>
          <View style={{alignItems: 'center', margin: 20}}>
            <Text
              style={{
                fontFamily: FontFamily.regular,
                color: BaseColor.whiteColor,
              }}>
              IR Value
            </Text>
            <Text
              style={{
                fontFamily: FontFamily.regular,
                color: BaseColor.whiteColor,
                marginTop: 15,
              }}>
              -
            </Text>
          </View>
          <View style={{alignItems: 'center'}}>
            <Text
              style={{
                fontFamily: FontFamily.regular,
                color: BaseColor.whiteColor,
              }}>
              Time (mins)
            </Text>
            <Text
              style={{
                fontFamily: FontFamily.regular,
                color: BaseColor.whiteColor,
                marginTop: 15,
              }}>
              -
            </Text>
          </View>
        </View>
        <TouchableOpacity
          style={{
            backgroundColor: BaseColor.primary,
            position: 'absolute',
            top: onTap
              ? dHeight * 0.75
              : onFirstTap
              ? dWidth * 0.75
              : dHeight * 0.75,
            left: 25,
            zIndex: 99999999,
            flexDirection: 'row',
            justifyContent: 'space-evenly',
            alignItems: 'center',
            width: 60,
            height: 60,
            borderRadius: 30,
            borderWidth: 2,
            borderColor: BaseColor.whiteColor,
          }}
          onPress={() => {
            // navigation.goBack();
          }}>
          <MaterialCommunityIcons
            name="image"
            color={BaseColors.whiteColor}
            size={25}
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={{
            backgroundColor: BaseColor.alertRed,

            position: 'absolute',
            top: 30,
            left: 30,
            zIndex: 99999999,
            flexDirection: 'row',
            justifyContent: 'space-evenly',
            alignItems: 'center',
          }}
          onPress={() => {
            navigation.goBack();
          }}>
          <MaterialCommunityIcons
            name="fullscreen-exit"
            color={BaseColors.whiteColor}
            size={40}
          />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            setOnTap(!onTap);
          }}
          style={{
            height: onTap ? 170 : onFirstTap ? dWidth : dHeight,
            width: onTap ? 170 : onFirstTap ? dHeight : dWidth,
            position: 'absolute',
            borderRadius: onTap ? 30 : 0,
            overflow: 'hidden',
            zIndex: onTap ? 999999 : 9,
            right: onTap ? 20 : 0,
            top: onTap ? 20 : 0,
            justifyContent: 'center',
          }}>
          <WebView
            ref={ref => (webViewRef.current = ref)}
            source={{
              uri: 'http://192.168.4.1/',
            }}
            style={{
              height: '100%',
              width: '100%',
              alignSelf: 'center',
            }}
            pullToRefreshEnabled
            onError={e => {
              console.log('error----bbbrr', e);
              Alert.alert('error');
            }}
            renderLoading={() => (
              <ActivityIndicator color={BaseColor.blackColor} />
            )}
            mixedContentMode="always"
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={{
            height: onTap ? (onFirstTap ? dWidth : dHeight) : 170,
            width: onTap ? (onFirstTap ? dHeight : dWidth) : 170,
            right: onTap ? 0 : 20,
            borderRadius: 30,
            top: onTap ? 0 : 20,
            bottom: 10,
            position: 'absolute',
            zIndex: onTap ? 9 : 999,
            backgroundColor: 'red',
            overflow: 'hidden',
          }}>
          <TouchableOpacity
            style={{
              height: onTap ? dHeight - 20 : '50%',
              width: onTap ? dWidth - 40 : '58%',
              position: 'absolute',
              borderRadius: 30,
              overflow: 'hidden',
              zIndex: onTap ? 999 : 9,
              right: onTap ? 0 : 20,
              top: onTap ? 10 : 20,
              bottom: 10,
            }}
            onPress={() => {
              console.log('dddddkjjkjkjjhjhj');
              setFirstTap(false);
              setOnTap(!onTap);
              //   if (!onTap) {
              //     setOnTap(!onTap);
              //   }
            }}
          />

          <MapView
            style={{
              height: '100%',
              width: '100%',
            }}
            region={{
              latitude: loc?.latitude || 0,
              longitude: loc?.longitude || 0,
              latitudeDelta: 0.015,
              longitudeDelta: 0.0121,
            }}
            pitchEnabled={false}
            rotateEnabled={false}
            scrollEnabled={false}
            zoomEnabled={false}>
            <MapMarker
              coordinate={{
                latitude: loc?.latitude || 0,
                longitude: loc?.longitude || 0,
              }}>
              <View
                style={{
                  backgroundColor: BaseColor.whiteColor,
                  height: 30,
                  width: 30,
                  borderRadius: 15,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <View
                  style={{
                    backgroundColor: BaseColor.alertRed,
                    height: 20,
                    width: 20,
                    borderRadius: 10,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Icon
                    name="location-arrow"
                    size={10}
                    color={BaseColor.whiteColor}
                  />
                </View>
              </View>
            </MapMarker>
          </MapView>
        </TouchableOpacity>
      </View>
    </View>
  );
}

export default FullScreen;
