/* eslint-disable no-return-assign */
/* eslint-disable max-len */
/* eslint-disable no-unused-expressions */
/* eslint-disable indent */
/* eslint-disable no-nested-ternary */
/* eslint-disable implicit-arrow-linebreak */
/* eslint-disable no-console */
/* eslint-disable global-require */
/* eslint-disable quotes */
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {
  ActivityIndicator,
  BackHandler,
  Image,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  Animated,
  Dimensions,
  Alert,
  AppState,
  RefreshControl,
  FlatList,
} from 'react-native';
import Toast from 'react-native-simple-toast';
import {findIndex, isEmpty, isObject, isUndefined} from 'lodash';
import {useFocusEffect, useTheme} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import BleManager from 'react-native-ble-manager';
import BluetoothStateManager from 'react-native-bluetooth-state-manager';
import WebView from 'react-native-webview';
// import InAppBrowser from "react-native-inappbrowser-reborn";
import MapView, {Callout} from 'react-native-maps';
// import GetLocation from 'react-native-get-location';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import ViewShot from 'react-native-view-shot';
import moment from 'moment';
import {CustomIcon} from '../../config/LoadIcons';
import {FontFamily} from '../../config/typography';
import {translate} from '../../lang/Translate';
import styles from './styles';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';
import bluetoothActions from '../../redux/reducers/bluetooth/actions';
import {getSWVal, sendErrorReport} from '../../utils/commonFunction';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import AuthAction from '../../redux/reducers/auth/actions';
import BaseColors from '../../config/colors';
import CButton from '../../components/CButton';
import Orientation, {
  useDeviceOrientationChange,
} from 'react-native-orientation-locker';
import LinearGradient from 'react-native-linear-gradient';
import MapViewDirections from 'react-native-maps-directions';
import environment from '../../config/environment';
import Iconin from 'react-native-vector-icons/Ionicons';
import PlaceAction from '../../redux/reducers/place/actions';
let backPressed = 0;

/**
 *
 *@module Dashboard
 *
 */
const Dashboard = ({navigation}) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {height: dHeight, width: dWidth} = Dimensions.get('window');
  const dispatch = useDispatch();
  const {placeLocation, isTimerStart} = useSelector(state => state.place);
  const {setTimerState, setTripTimerValue} = PlaceAction;

  const {
    isBleConnected,
    connectedDeviceDetail,
    activeChildDetail,
    deviceDetail,
    isCurrentActiveDevice,
    activeDeviceId,
    path,
    // isConnected
  } = useSelector(state => state.bluetooth);

  const {} = bluetoothActions;
  const {setNotiCount} = AuthAction;
  const {isFarenheit} = useSelector(state => state.auth);
  const accessToken = useSelector(state => state.auth.accessToken);
  const animatedValue = useRef(new Animated.Value(0)).current;
  const animatedValue1 = useRef(new Animated.Value(0)).current;
  const [isChildActive, setIsChildActiveDevice] = useState({
    ...activeDeviceId,
  });

  const [loader, setLoader] = useState(false);

  const webViewRef = useRef();
  const [onTap, setOnTap] = useState(false);
  const [tapLoader, setTapLoader] = useState(false);
  const [appState, setAppState] = useState(true);
  const [loc, setLoc] = useState({});
  const [isLandscape, setIsLandscape] = useState(false);
  const [showImage, setShowImage] = useState(false);
  const [videoUrl, setVideoUrl] = useState();
  // "file:///var/mobile/Containers/Data/Application/85C31A7D-1615-4B14-87F0-20FFB2438A78/Documents/891.mp4"
  const navigationOptions = {
    header: null,
  };
  const [eventData, setEventData] = useState('event');
  const [IRValue, setIRValue] = useState('-');
  const [dtt, setdtt] = useState('dtt');
  const [isVisible, setIsVisible] = useState(true);
  const refS = useRef();
  const refM = useRef();
  const [imageUri, setImageUri] = useState();
  const [imageUriArr, setImgUriArr] = useState([]);
  const uriArr = [];
  const [arrUri, setArrUri] = useState([]);
  const [bat, setBatt] = useState(0);
  const [ir, setIR] = useState(0);
  const timerRef = useRef(null);
  const [timerValue, setTimerValue] = useState(0);
  const [timing, setTiming] = useState(0);
  const [distance, setDistance] = useState(0);
  const [prevCoords, setPrevCoords] = useState(null);

  useEffect(() => {
    let timerInterval;
    if (isTimerStart) {
      timerInterval = setInterval(() => {
        setTiming(prevTime => prevTime + 1);
      }, 1000);
    } else {
      setTiming(0);
    }

    return () => clearInterval(timerInterval);
  }, [isTimerStart]);

  // const startLocationUpdates = () => {
  //   GetLocation.watchPosition(
  //     position => {
  //       console.log('jkjjhjj=====position====', position);
  //       const {latitude, longitude} = position.coords;

  //       if (prevCoords) {
  //         const prevLat = prevCoords.latitude;
  //         const prevLong = prevCoords.longitude;
  //         const currLat = latitude;
  //         const currLong = longitude;

  //         const dLat = ((currLat - prevLat) * Math.PI) / 180;
  //         const dLong = ((currLong - prevLong) * Math.PI) / 180;
  //         const a =
  //           0.5 -
  //           Math.cos(dLat) / 2 +
  //           (Math.cos((prevLat * Math.PI) / 180) *
  //             Math.cos((currLat * Math.PI) / 180) *
  //             (1 - Math.cos(dLong))) /
  //             2;
  //         const distanceInMeters = 6371 * Math.asin(Math.sqrt(a));
  //         console.log('disssssss', distanceInMeters);
  //         setDistance(prevDistance => prevDistance + distanceInMeters);
  //       }
  //       console.log('poiiiiii', position);
  //       setPrevCoords(position.coords);
  //     },
  //     error => {
  //       console.log('Location error:', error);
  //     },
  //     {enableHighAccuracy: true, distanceFilter: 10},
  //   );
  // };

  // useEffect(() => {
  //   startLocationUpdates();
  // }, []);

  useEffect(() => {
    console.log('ddddddd===previ===', prevCoords);
    // startLocationUpdates();
  }, [prevCoords]);

  // const startTimer = () => {
  //   if (timerRef.current) return; // Timer is already running

  //   timerRef.current = setInterval(() => {
  //     setTimerValue((prevValue) => prevValue + 1);
  //   }, 1000); // 1000 milliseconds = 1 second
  // };

  // const stopAndClearTimer = () => {
  //   clearInterval(timerRef.current);
  //   timerRef.current = null;
  //   setTimerValue(0);
  // };

  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     setIsVisible((prevVisible) => !prevVisible);
  //   }, 500); // Change the interval duration as per your preference

  //   return () => clearInterval(interval); // Cleanup the interval on component unmount
  // }, []);

  const deg2rad = deg => deg * (Math.PI / 180);

  const api = async () => {
    try {
      // const formData = new FormData();
      // formData.append('attachment', file);
      // "http://***********:8080/c?dwn=//_1080_1.AVi",
      await fetch('http://***********/sensor')
        .then(response => response.text())
        .then(htmlData => {
          // Process the HTML data here
          // Alert.alert(JSON.stringify(htmlData));
          const regex = /(<([^>]+)>)/gi;
          const result = htmlData?.replace(regex, '');
          // const b = result.trim().split(":")[2];
          const irValue = result?.match(/IR Value : (.*)/)[1];
          const batteryLevel = result?.match(/Battery Level : (.*)/)[1];
          setIR(irValue?.replace(/\\.*/, ''));
          setBatt(batteryLevel?.replace(/\\.*/, ''));

          const IRV = irValue?.replace(/\\.*/, '');
          if (IRV <= 40) {
            // startTimer();
          } else if (IRV > 40) {
            // stopAndClearTimer();
          }
          setTimeout(() => {
            api();
          }, 3000);

          console.log(htmlData);
        })
        .catch(error => {
          // Handle any errors that occurred during the fetch
          // Alert.alert(JSON.stringify(error));
          setTimeout(() => {
            api();
          }, 3000);
          console.error(error);
        });
    } catch (err) {
      setTimeout(() => {
        api();
      }, 3000);
      console.log('err ===========2==== ', err);
    }
  };

  useEffect(() => {
    if (isBleConnected) {
      api();
      // const interval = setInterval(() => {
      //   api();
      //   // Make sure to handle the API response or any errors appropriately
      // }, 5000); // 1000 milliseconds = 1 second
      // // Clear the interval when the component is unmounted
      // return () => clearInterval(interval);
      // downloadVideo();
    } else {
      // stopAndClearTimer();
      // start and stop distance===============mmmmmm on device connect and disconnect
      // and also check bg maode
    }
  }, [isBleConnected]);
  useEffect(() => {
    if (isLandscape) {
      navigation.setOptions({
        tabBarVisible: false,
      });
    } else {
      navigation.setOptions({
        tabBarVisible: true,
      });
    }
  }, [isLandscape]);

  useEffect(() => {
    // on mount
    if (showImage) {
      if (onTap) {
        refM?.current?.capture()?.then(uri => {
          console.log('do something with ', uri);
          setImageUri(uri);
          // imageUriArr.push(uri);
          // setImgUriArr(imageUriArr);
          // sendSnapShot(uri);
          // uriArr.push({
          //   save_image: uri,
          //   location_name: "vadodara",
          //   date_time: moment.now(),
          // });
          setImgUriArr([
            ...imageUriArr,
            {
              save_image: uri,
              location_name: 'vadodara',
              date_time: moment.now(),
            },
          ]);
        });
      } else {
        refS?.current?.capture()?.then(uri => {
          console.log('do something with ', uri);
          setImageUri(uri);
          // imageUriArr.push(uri);
          // setImgUriArr(imageUriArr);
          // sendSnapShot(uri);
          // can not push its getting override please check --- device not connected is commented,mmmm
          console.log('uri Arr-----', arrUri);
          arrUri.push({
            save_image: uri,
            location_name: '-',
            date_time: moment.now(),
          });
          setArrUri(arrUri);
          imageUriArr.push({
            save_image: uri,
            location_name: '-',
            date_time: moment.now(),
          });
          setImgUriArr(imageUriArr);
        });
      }
    }
  }, [showImage]);

  useEffect(() => {
    console.log('App ddddd');
    const handleAppStateChange = nextAppState => {
      console.log(`App State: ${nextAppState}`);
      if (nextAppState === 'active') {
        console.log('App State======', true);
        // sendErrorReport(true, "app_state_active");
        setAppState(true);
      } else if (nextAppState === 'background') {
        console.log('App State======', false);
        // sendErrorReport(false, "app_state_background");
        setAppState(false);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => subscription.remove();
  }, []);

  // useEffect(() => {
  //   getCurrentLocation();
  // }, []);

  useEffect(() => {
    if (activeDeviceId) {
      const index = findIndex(
        connectedDeviceDetail,
        i => i?.product_id === activeDeviceId?.product_id,
      );
      if (index > -1) {
        sendErrorReport(activeDeviceId, 'activeDeviceId---dash');
        sendErrorReport(
          connectedDeviceDetail[index],
          'connectedDeviceDetail[index]---dash',
        );
        setIsChildActiveDevice(connectedDeviceDetail[index]);
      }
    }
  }, [activeDeviceId]);

  /** this function for get Badge Count
   * @function getSnapShotList
   * @param {object} data {}
   */
  async function getSnapShotList() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.qeridooSaveImageList,
        'GET',
        {},
        headers,
      );

      if (response.success) {
        console.log('rreeee===', response.data);
        if (response.data) {
          setImgUriArr(response.data);
        }
      } else {
        console.log('🚀 save image list err', response);
      }
    } catch (error) {
      console.log('error for save image list ere list ===', error);
      sendErrorReport(error, 'save image list er');
    }
  }
  /** this function for send Attachment
   * @function sendSnapShot
   * @param {object} data uri, name, type
   */
  const sendSnapShot = async uris => {
    // const uri = "file:///var/mobile/Containers/Data/Application/85C31A7D-1615-4B14-87F0-20FFB2438A78/Documents/891.mp4"
    const uri = uris; // videoUrl;
    const name = uri ? uri.substring(uri.lastIndexOf('/') + 1) : '';
    console.log('name save--', loc);
    const formdata = new FormData();
    formdata.append('is_image', 0);
    formdata.append('latitude', loc?.latitude || 0);
    formdata.append('longitude', loc?.longitude || 0);
    formdata.append('save_image', {
      uri,
      name,
      type: 'jpg',
    });
    console.log('name save-forms-', formdata);
    try {
      // const formData = new FormData();
      // formData.append('attachment', file);

      fetch(BaseSetting.api + BaseSetting.endpoints.qeridooSaveImage, {
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
        },
        body: formdata,
      })
        .then(response => response.json())
        .then(responseJson => {
          console.log('chillbaby ~ qeridooSaveImage', responseJson);
          getSnapShotList();
        })
        .catch(err => {
          uriArr.push({
            save_image: uris,
            location_name: '',
            date_time: moment.now(),
          });
          setImgUriArr(uriArr);
          sendErrorReport(err, 'qeridooSaveImage');
          console.log('err =qeridooSaveImage==', err);
        });
    } catch (err) {
      uriArr.push(uris);
      setImgUriArr(uriArr);
      sendErrorReport(err, 'qeridooSaveImage_catch');
      console.log('qeridooSaveImage_catch', err);
      sendErrorReport(err, 'qeridooSaveImage_catch');
    }
  };
  const parseBleData = useMemo(
    () =>
      JSON.parse(
        isCurrentActiveDevice?.[activeDeviceId?.product_id || ''] || '{}',
      ) || {},
    [isCurrentActiveDevice, activeDeviceId],
  );

  const batteryVoltage =
    isObject(parseBleData) && !isEmpty(parseBleData) && parseBleData?.BV
      ? parseBleData.BV
      : parseBleData.bv;

  const SW =
    !isEmpty(activeDeviceId) && !isUndefined(activeDeviceId?.product_id)
      ? getSWVal(parseBleData, activeDeviceId)
      : 0;
  const temp =
    isObject(parseBleData) &&
    !isEmpty(parseBleData) &&
    parseBleData?.Temperature
      ? parseBleData.Temperature
      : parseBleData.TEMP;

  const [myDevices, setMyDevices] = useState(false);

  useFocusEffect(
    useCallback(() => {
      // Orientation.unlockAllOrientations();
      // Orientation.lockToPortrait();
      if (accessToken !== '') {
        getBadgeCount();
        getSnapShotList();
      }
    }, []),
  );

  /** this function for get Badge Count
   * @function getBadgeCount
   * @param {object} data {}
   */
  async function getBadgeCount() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getUserAlertCount,
        'POST',
        {},
        headers,
      );

      if (response.success) {
        dispatch(setNotiCount(response));
      } else {
        console.log(
          '🚀 ~ file: index.js ~ line 220 ~ getBadgeCount ~ response',
          response,
        );
      }
    } catch (error) {
      console.log('error for device list ===', error);
      sendErrorReport(error, 'get_device_list');
    }
  }

  useEffect(() => {
    setTimeout(() => {
      Animated.loop(
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1400,
          useNativeDriver: true,
        }),
      ).start();
    }, 100);
    setTimeout(() => {
      Animated.loop(
        Animated.timing(animatedValue1, {
          toValue: 1,
          duration: 1400,
          useNativeDriver: true,
        }),
      ).start();
    }, 500);
  }, []);

  useEffect(() => {
    BluetoothStateManager.getState().then(bluetoothState => {
      switch (bluetoothState) {
        case 'Unknown':
        case 'Resetting':
        case 'Unsupported':
        case 'Unauthorized':
        case 'PoweredOff':
          if (Platform.OS === 'android') {
            BluetoothStateManager.requestToEnable().then(result => {
              console.log(
                'BluetoothStateManager.requestToEnable -> result',
                result,
              );
              // result === true -> user accepted to enable bluetooth
              // result === false -> user denied to enable bluetooth
            });
          } else {
            console.log('=====1111111');
            // BluetoothStateManager.openSettings();==----------------------mm
          }
          break;
        case 'PoweredOn':
          console.log('ON ==== ...... ******');
          // startScan();
          break;
        default:
          break;
      }
    });
  }, []);

  const getConvertedTemp = realTemp => {
    if (isFarenheit) {
      return ((Number(realTemp) * 9) / 5 + 32).toFixed(0);
    }
    return realTemp;
  };

  useEffect(() => {
    // dispatch(setActiveDeviceId(isActive));
    if (connectedDeviceDetail.length > 0) {
      connectedDeviceDetail.map(item => {
        if (isUndefined(item.product_id)) {
          connectedDeviceDetail.splice(item);
        }
      });
    }
  }, [connectedDeviceDetail]);

  useEffect(() => {
    BleManager.getConnectedPeripherals([]).then(results => {
      console.log('SEE CONNECTED PERI', results);
      const mydevs = [];
      results.map(v => {
        mydevs.push({id: v.id, name: v.name});
      });
      setMyDevices(mydevs);
    });
  }, []);

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }
  useEffect(() => {
    if (showImage) {
      setTimeout(() => {
        setShowImage(false);
      }, 5000);
    }
  }, [showImage]);
  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  // useEffect(() => {
  //   if (!isBleConnected) setIsLandscape(false);
  // }, [isBleConnected]);

  const batteryPercent =
    batteryVoltage === 271
      ? 90
      : batteryVoltage === 270
      ? 80
      : batteryVoltage === 269 || batteryVoltage === 268
      ? 70
      : batteryVoltage === 267 || batteryVoltage === 266
      ? 60
      : batteryVoltage >= 260
      ? 50
      : batteryVoltage >= 252
      ? 40
      : batteryVoltage >= 246
      ? 30
      : batteryVoltage >= 236
      ? 20
      : batteryVoltage >= 215
      ? 10
      : batteryVoltage >= 175
      ? 0
      : 100;

  // this function for handle blue pulse
  useEffect(() => {
    let text = '';
    const possible =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 5; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    dispatch(BluetoothAction.setSwiperKey(text));
  }, []);

  // this function for get current location
  // async function getCurrentLocation() {
  //   console.log('called----1');
  //   GetLocation.getCurrentPosition({
  //     enableHighAccuracy: true,
  //     timeout: !appState ? 15000 : 0,
  //   })
  //     .then(location => {
  //       // console.log("location tada", JSON.stringify(location, null, 2));
  //       sendErrorReport(location, 'SMS_location');
  //       setLoc(JSON.parse(location));
  //     })
  //     .catch(async error => {
  //       const {code, message} = error;
  //       if (code === 'UNAVAILABLE') {
  //         Toast.show(
  //           'Please enable your location service to send emergency alert.',
  //         );
  //         GetLocation.openAppSettings();
  //       }
  //     });
  // }
  const updatedIndexData = data => {
    console.log('data...', data);
    // Alert.alert(JSON.stringify(data));
    if (!isUndefined(data)) {
      // Alert.alert("dddeleted");
      setTimeout(() => {
        imageUriArr.splice(data, 1);
        setImgUriArr(imageUriArr);
      }, 2000);
      deleteSnapVideo(data);
    }
  };

  async function deleteSnapVideo(dataId) {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    const data = {
      image_id: dataId,
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.deleteSnapVideo,
        'POST',
        data,
        headers,
      );
      console.log('id---', data);
      console.log('ddddd', response);
      if (response.success) {
        Toast.show(translate(response.message));
        navigation.goBack();
      } else {
        imageUriArr.splice(data, 1);
        setImgUriArr(imageUriArr);
      }
    } catch (error) {
      imageUriArr.splice(data, 1);
      setImgUriArr(imageUriArr);
      Toast.show('Something went wrong, please try again');
      console.log('error ===', error);
    }
  }

  const convertDateTime = dateTime => {
    const currentDate = new Date();
    const itemDate = new Date(dateTime);

    const diffInMilliseconds = Math.abs(currentDate - itemDate);
    const diffInMinutes = Math.floor(diffInMilliseconds / 1000 / 60);

    const hours = Math.floor(diffInMinutes / 60);
    const minutes = diffInMinutes % 60;

    const formattedDate = `${itemDate.getDate()}/${
      itemDate.getMonth() + 1
    }/${itemDate.getFullYear()}`;

    return `${hours} hr ${minutes} min | ${formattedDate}`;
  };

  const mapStyle = [
    {
      elementType: 'geometry',
      stylers: [
        {
          color: '#f5f5f5',
        },
      ],
    },
    {
      elementType: 'labels.icon',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      elementType: 'labels.text.fill',
      stylers: [
        {
          color: '#616161',
        },
      ],
    },
    {
      elementType: 'labels.text.stroke',
      stylers: [
        {
          color: '#f5f5f5',
        },
      ],
    },
    {
      featureType: 'administrative.land_parcel',
      elementType: 'labels.text.fill',
      stylers: [
        {
          color: '#bdbdbd',
        },
      ],
    },
    {
      featureType: 'poi',
      elementType: 'geometry',
      stylers: [
        {
          color: '#eeeeee',
        },
      ],
    },
    {
      featureType: 'poi',
      elementType: 'labels.text.fill',
      stylers: [
        {
          color: '#757575',
        },
      ],
    },
    {
      featureType: 'poi.park',
      elementType: 'geometry',
      stylers: [
        {
          color: '#e5e5e5',
        },
      ],
    },
    {
      featureType: 'poi.park',
      elementType: 'labels.text.fill',
      stylers: [
        {
          color: '#9e9e9e',
        },
      ],
    },
    {
      featureType: 'road',
      elementType: 'geometry',
      stylers: [
        {
          color: '#ffffff',
        },
      ],
    },
    {
      featureType: 'road.arterial',
      elementType: 'labels.text.fill',
      stylers: [
        {
          color: '#757575',
        },
      ],
    },
    {
      featureType: 'road.highway',
      elementType: 'geometry',
      stylers: [
        {
          color: '#dadada',
        },
      ],
    },
    {
      featureType: 'road.highway',
      elementType: 'labels.text.fill',
      stylers: [
        {
          color: '#616161',
        },
      ],
    },
    {
      featureType: 'road.local',
      elementType: 'labels.text.fill',
      stylers: [
        {
          color: '#9e9e9e',
        },
      ],
    },
    {
      featureType: 'transit.line',
      elementType: 'geometry',
      stylers: [
        {
          color: '#e5e5e5',
        },
      ],
    },
    {
      featureType: 'transit.station',
      elementType: 'geometry',
      stylers: [
        {
          color: '#eeeeee',
        },
      ],
    },
    {
      featureType: 'water',
      elementType: 'geometry',
      stylers: [
        {
          color: '#c9c9c9',
        },
      ],
    },
    {
      featureType: 'water',
      elementType: 'labels.text.fill',
      stylers: [
        {
          color: '#9e9e9e',
        },
      ],
    },
  ];

  const render = ({item, index}) => (
    <>
      <TouchableOpacity
        style={{
          width: '28%', // Reduce the width so that three items fit across with less space in between

          margin: 10, // Adjust padding if needed
          // marginHorizontal: -5, // Add horizontal margin if needed
          overflow: 'hidden',
          backgroundColor: BaseColor.whiteColor,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
          elevation: 5,
          borderRadius: 15,
        }}
        onPress={() => {
          navigation.navigate('snapShotView', {
            imageUri: item.save_image, // item
            loc: item.location_name,
            date: item.date_time,
            id: item.id || index,
            updatedIndex: updatedIndexData,
          });
        }}>
        {/* <View style={{ maxWidth: dWidth - 287, height: 129,  }}> */}
        <Image
          source={{uri: item.save_image}} // item  }}
          style={{
            width: '100%',
            height: 129,
            borderRadius: 15,
            // marginRight: 20,
          }}
          resizeMode="cover"
        />
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.8)']}
          style={{
            position: 'absolute',
            left: 0,
            right: 0,
            bottom: 0,
            height: '50%', // adjust this value to control the height of the gradient
            borderBottomLeftRadius: 15,
            borderBottomRightRadius: 15,
          }}
        />
        {/* </View> */}

        <View
          style={{
            position: 'absolute',
            // backgroundColor: BaseColor.textGrey,
            bottom: 0,
            // left: 5,
            paddingLeft: 5,
            // borderWidth: 1,
            width: '100%',
            height: '20%',
            borderBottomEndRadius: 15,
            borderBottomStartRadius: 15,
          }}>
          <Text
            style={{
              fontFamily: FontFamily.regular,
              fontSize: 9,

              color: BaseColor.whiteColor,
            }}
            numberOfLines={1}>
            Snapshot - {item?.location_name}
          </Text>
          <Text
            style={{
              fontSize: 6,

              color: BaseColor.whiteColor,
            }}>
            {item?.date_time && convertDateTime(item.date_time)}
          </Text>
        </View>
        {/* <FAIcon
          name="angle-right"
          size={24}
          color={BaseColor.blackColor}
          style={{ right: 10, position: "absolute" }}
        /> */}
      </TouchableOpacity>
      {/* <View
        style={{
          backgroundColor: BaseColor.blackColor,
          height: 1,
          marginHorizontal: 20,
          marginVertical: 10,
          opacity: 0.2,
        }}
      /> */}
    </>
  );
  const html = `
  <html>
  <head></head>
  <body>   
   <input id="myId" placeholder="Enter text" oninput="getValue()">
   <script>
    window.id = setInterval(() => {
      if (window.ReactNativeWebView) {
        document.body.style.backgroundColor = 'green'; 
        clearInterval(window.id);
      }
    }, 1000);
     function getValue() {
       let answer = document.getElementById("myId").value;
       window.ReactNativeWebView.postMessage(answer);              
     };
   </script>  
  </body>
  </html>
`;

  const INJECTEDJAVASCRIPT = `const meta = document.createElement('meta'); meta.setAttribute('content', 'width=device-width, initial-scale=0.9, maximum-scale=0.5, user-scalable=0'); meta.setAttribute('name', 'viewport'); document.getElementsByTagName('head')[0].appendChild(meta); `;
  const getData = () => {
    fetch('http://***********/sensor')
      .then(response => response.json())
      .then(data => {
        // qqqqqqqqq

        setdtt(`${JSON.stringify(data)}kkk`);
        // handle the response data
      })
      .catch(error => {
        setdtt(JSON.stringify(error));
        // handle any errors
      });
  };
  // useEffect(() => {
  //   const fileArray =
  //     '["/config.txt","/1305_1.AVi","/idx.tmp","/1306_1.AVi","/1308_1.AVi","/1309_1.AVi","/1310_1.AVi","/1312_1.AVi","/1314_1.AVi","/1316_1.AVi","/1317_1.AVi","/1321_1.AVi","/1323_1.AVi","/"/config.txt","/1305_1.AVi","/idx.tmp","/1306_1.AVi","/1308_1.AVi","/1309_1.AVi","/1310_1.AVi","/1312_1.AVi","/1314_1.AVi","/1316_1.AVi","/1317_1.AVi","/1321_1.AVi","/1323_1.AVi","]';
  //   const aviFiles = fileArray
  //     .match(/\/([\w\d]+\.AVi)/gi)
  //     .map((file) => file.substring(1).toLowerCase()); // convert to lowercase and remove leading slash

  //   console.log("aviFiles-----?>>", aviFiles);
  // }, []);

  const [webviewData, setWebviewData] = useState('http://***********/sensor');

  useEffect(() => {
    const intervalId = setInterval(() => {
      // Fetch data from API or update the data state here
      setWebviewData('http://***********/sensor');
    }, 1000);
    return () => clearInterval(intervalId);
  }, []);
  const [headerData, setHeaderData] = useState('header');

  const onMessage = event => {
    setEventData(JSON.stringify(event.nativeEvent.data));
    const data = JSON.parse(event.nativeEvent.data);
    setHeaderData(data.header);

    // const data = JSON.parse(event.nativeEvent.data);
    // setEventData(JSON.stringify(data));
    // const { batt, IR } = data;
    // console.log("Battery Level:", batt);
    // console.log("IR Value:", IR);
    // setHeaderData(batt);
  };

  const onHttpError = event => {
    const {headers} = event.nativeEvent;
    setHeaderData(headers);
  };

  const onHttpFinish = event => {
    const {headers} = event.nativeEvent;
    setHeaderData(headers);
  };
  const [irValue, setIrValue] = useState(0);
  const [batteryLevel, setBatteryLevel] = useState(0);

  //   const injectedJavaScript = `
  //   document.addEventListener("message", (event) => {
  //     const data = JSON.parse(event.data);
  //     const irValue = data.IR;
  //     const batteryLevel = data.batt;
  //     console.log("Received IR value:", irValue);
  //     console.log("Received battery level:", batteryLevel);
  //     // Use irValue and batteryLevel as needed
  //   });
  // `;
  const displaySpinner = () => (
    <ActivityIndicator
      color="#bc2b78"
      size="large"
      styles={styles.activityIndicator}
    />
  );

  const handleWebViewMessage = event => {
    const htmlData = event.nativeEvent.data;
  };
  const formatTime = time => {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = time % 60;

    return `${hours.toString().padStart(2, '0')} : ${minutes
      .toString()
      .padStart(2, '0')} : ${seconds.toString().padStart(2, '0')}`;
  };

  const formattedTime = formatTime(timing);

  useFocusEffect(
    React.useCallback(() => {
      // Unlock orientations when entering the dashboard
      Orientation.unlockAllOrientations();

      return () => {
        // Lock to portrait when leaving the dashboard
        Orientation.lockToPortrait();
      };
    }, []),
  );

  useDeviceOrientationChange(ori => {
    if (ori === 'LANDSCAPE-RIGHT' || ori === 'LANDSCAPE-LEFT') {
      Orientation.unlockAllOrientations();
      setIsLandscape(true);
    } else if (ori === 'UNKNOWN' || ori === 'FACE-UP') {
      null;
    } else {
      Orientation.lockToPortrait();
      setIsLandscape(false);
    }
  });

  useEffect(() => {
    let timeoutId;
    if (tapLoader) {
      timeoutId = setTimeout(() => {
        setOnTap(!onTap);
        setTapLoader(false);
      }, 1000);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [tapLoader, onTap]);

  return (
    <View style={{flex: 1, backgroundColor: BaseColor.whiteColor}}>
      {!isLandscape && (
        <View
          style={{
            justifyContent: 'space-between',
            flexDirection: 'row',
            alignItems: 'center',
            height: '7%',
            alignContent: 'center',
            paddingHorizontal: 16,
            backgroundColor: BaseColor.whiteColor,
          }}></View>
      )}
      <ScrollView
        contentContainerStyle={{
          paddingBottom: 0,
          marginTop: 0,
        }}
        bounces={false}
        scrollEnabled={!isLandscape}>
        <>
          {!isLandscape && (
            <View
              style={{
                marginHorizontal: isLandscape ? 0 : 18,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 10,
                backgroundColor: BaseColor.whiteColor,
              }}>
              <Text
                style={{
                  fontSize: 10,
                  fontFamily: FontFamily.regular,
                  color: BaseColor.primary,
                }}>
                {!isBleConnected
                  ? 'Please go to “Settings” to reconnect the device'
                  : 'Starting Camera... let’s roll!'}
              </Text>
              <TouchableOpacity
                onPress={() => navigation.navigate('FileManager')}>
                <CustomIcon
                  name="file-manager"
                  size={22}
                  color={BaseColor.blackColor}
                />
              </TouchableOpacity>
            </View>
          )}
          <View
            style={{
              height: isLandscape ? dHeight : 368,
              width: isLandscape ? '100%' : dWidth - 37,
              // height: 210, // 420
              backgroundColor: BaseColor.whiteColor,
              borderRadius: isLandscape ? 0 : 15,
              overflow: 'hidden',
              marginHorizontal: isLandscape ? 0 : 18,
              justifyContent: isBleConnected ? 'center' : 'flex-start',
              // borderWidth: 1,
              borderColor: '#EAEFEF',
              elevation: 2,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
            }}>
            {tapLoader ? (
              <View
                style={{
                  height: '100%',
                  justifyContent: 'center',
                }}>
                <ActivityIndicator
                  size={'large'}
                  color={BaseColor.primary}
                  animating
                />
              </View>
            ) : (
              <>
                {!isBleConnected ? (
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      // borderWidth: 1,
                      marginTop: '15%',
                    }}>
                    <CustomIcon
                      name="camera-off"
                      size={45}
                      color={BaseColor.primary}
                    />
                    <CButton
                      title="Reconnect Device"
                      iconBg={BaseColor.whiteColor}
                      style={[
                        {
                          backgroundColor: BaseColor.primary,
                          borderRadius: 20,
                          // paddingLeft: 12,
                          // paddingRight: 8,
                          width: '40%',
                          // height: 24,
                          height: '25%',
                        },
                      ]}
                      titleStyle={{
                        color: BaseColor.whiteColor,
                        fontSize: 12,
                        fontFamily: FontFamily.regular,
                        letterSpacing: 0,
                        fontWeight: 'normal',
                      }}
                      onPress={() => {
                        setTimeout(() => {
                          setLoader(true);
                        }, 1000);
                        setTimeout(() => {
                          setLoader(false);
                          navigation.navigate(translate('setting'));
                          // onCardClick();
                        }, 3000);
                      }}
                      loader={loader}
                    />
                  </View>
                ) : (
                  <>
                    {isLandscape && (
                      <View
                        style={{
                          backgroundColor: BaseColor.whiteColor,
                          height: 54,
                          // opacity: 0.9,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          position: 'absolute',
                          bottom: '10%',
                          right: '33%',
                          zIndex: 99999999,
                          paddingHorizontal: 30,
                          width: '45%',
                          alignItems: 'center',
                          borderRadius: 10,
                          borderWidth: 0.5,
                          borderColor: BaseColor.blackColor,
                          elevation: 5,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.25,
                          shadowRadius: 3.84,
                        }}>
                        <View style={{alignItems: 'center', marginTop: 5}}>
                          <Text
                            style={{
                              fontFamily: FontFamily.regular,
                              color: BaseColor.blackColor,
                              fontSize: 10,
                            }}>
                            Distance
                          </Text>
                          <Text
                            style={{
                              fontFamily: FontFamily.regular,
                              color: BaseColor.blackColor,
                              fontSize: 16,
                              marginTop: 5,
                            }}>
                            {distance?.toFixed(2) || 0}
                            {/* {bat} */}
                          </Text>
                        </View>
                        <View style={{alignItems: 'center', marginTop: 5}}>
                          <Text
                            style={{
                              fontFamily: FontFamily.regular,
                              color: BaseColor.blackColor,
                              fontSize: 10,
                            }}>
                            IR Value
                          </Text>
                          <Text
                            style={{
                              fontFamily: FontFamily.regular,
                              color: BaseColor.blackColor,
                              fontSize: 16,
                              marginTop: 5,
                            }}>
                            {ir}
                          </Text>
                        </View>
                        <View style={{alignItems: 'center', marginTop: 5}}>
                          <Text
                            style={{
                              fontFamily: FontFamily.regular,
                              color: BaseColor.blackColor,
                              fontSize: 10,
                            }}>
                            Time (mins)
                          </Text>
                          <Text
                            style={{
                              fontFamily: FontFamily.regular,
                              color: BaseColor.blackColor,
                              fontSize: 16,
                              marginTop: 5,
                            }}>
                            {formattedTime}
                          </Text>
                        </View>
                      </View>
                    )}
                    {/* captured image */}

                    {/* webview */}
                    <TouchableOpacity
                      onPress={() => {
                        setTapLoader(true);
                      }}
                      style={{
                        height: onTap ? 90 : isLandscape ? dHeight : '100%', // 430,
                        width: onTap ? 140 : isLandscape ? '100%' : dWidth,
                        position: 'absolute',
                        borderRadius: onTap ? 10 : 0,
                        overflow: 'hidden',
                        zIndex: onTap ? 999 : 9,
                        right: onTap ? 20 : 0,
                        top: isLandscape ? (onTap ? null : null) : null,
                        bottom: isLandscape ? (onTap ? 20 : 0) : onTap ? 20 : 0,
                        justifyContent: 'center',
                        borderWidth: onTap ? 0.5 : 0,
                        borderColor: '#EAEFEF',
                      }}>
                      <ViewShot
                        ref={refS}
                        startInLoadingState
                        options={{
                          fileName: 'qeridoo',
                          format: 'jpg',
                          quality: 0.9,
                        }}
                        style={{
                          height: onTap ? 170 : isLandscape ? '100%' : '100%', // 430,
                          width: onTap ? 250 : isLandscape ? '100%' : dWidth,
                          marginTop: isLandscape ? -10 : 0,
                        }}>
                        <WebView
                          ref={ref => (webViewRef.current = ref)}
                          // source={{ html }}
                          style={{marginTop: onTap ? 0 : isLandscape ? 0 : 0}}
                          source={{
                            uri: 'http://***********/stream', // https://www.diawi.com/", // file manager - http://***********:8080/ photps - http://***********/photos
                          }}
                          pullToRefreshEnabled
                          onError={e => {
                            console.log('error----bbbrr', e);
                            // Toast.show(e);
                            Alert.alert('error');
                          }}
                          // scalesPageToFit={false}
                          // injectedJavaScript={injectedJavaScript}
                          onMessage={onMessage}
                          // injectedJavaScript="window.ReactNativeWebView.postMessage(JSON.stringify({ header: document.head.innerHTML }))"
                          javaScriptEnabled
                          domStorageEnabled
                          contentInset={{
                            top: 0,
                            right: 0,
                            left: 0,
                            bottom: 0,
                          }}
                          scrollEnabled
                          renderLoading={() => displaySpinner()}
                        />
                      </ViewShot>
                    </TouchableOpacity>
                  </>
                )}
                {/* capture button & image  */}
                {showImage ? (
                  <View
                    style={{
                      backgroundColor: BaseColor.whiteColor,
                      position: 'absolute',
                      bottom: isLandscape ? 35 : 10,
                      left: isLandscape ? 20 : 10,
                      zIndex: 99999999,
                      justifyContent: 'space-evenly',
                      alignItems: 'center',
                      width: 50,
                      height: 50,
                      borderRadius: 25,
                      borderWidth: 5,
                      borderColor: isBleConnected
                        ? BaseColor.whiteColor
                        : '#F8F8F8',
                    }}>
                    <Image
                      source={{
                        uri: imageUri,
                      }}
                      style={{
                        height: 35,
                        width: 35,
                        borderColor: BaseColor.blackColor,
                        borderRadius: 30,
                      }}
                      resizeMode="cover"
                    />
                    <View
                      style={{
                        position: 'absolute',
                        zIndex: 1,
                        top: '25%',
                      }}>
                      <CustomIcon
                        name="check-rounded"
                        size={16}
                        color={BaseColor.whiteColor}
                      />
                    </View>
                  </View>
                ) : (
                  <TouchableOpacity
                    style={{
                      backgroundColor: isBleConnected
                        ? BaseColor.red
                        : '#DCE1E1',
                      position: 'absolute',
                      bottom: isLandscape ? 35 : 10,
                      left: isLandscape ? 20 : 10,
                      zIndex: 99999999,
                      justifyContent: 'space-evenly',
                      alignItems: 'center',
                      width: 50,
                      height: 50,
                      borderRadius: 25,
                      borderWidth: 5,
                      borderColor: isBleConnected
                        ? BaseColor.whiteColor
                        : '#F8F8F8',
                    }}
                    onPress={() => {
                      if (isBleConnected) {
                        setShowImage(true);
                      }
                      // navigation.goBack();
                    }}>
                    {isBleConnected && (
                      <CustomIcon
                        name="add-image"
                        size={16}
                        color={BaseColor.whiteColor}
                      />
                    )}
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={{
                    position: 'absolute',
                    bottom: isLandscape ? 70 : 45,
                    left: isLandscape ? 20 : 10,
                    zIndex: 99999999,
                    justifyContent: 'space-evenly',
                    alignItems: 'center',
                    // width: 50,
                    height: 50,
                  }}>
                  {isBleConnected && (
                    <Text
                      style={{
                        color: isBleConnected
                          ? BaseColor.whiteColor
                          : BaseColor.blackColor,
                        fontFamily: FontFamily.regular,
                        fontSize: 10,
                      }}>
                      {' '}
                      {activeChildDetail?.nick_name
                        ? `${activeChildDetail?.nick_name} Cam`
                        : !isEmpty(deviceDetail)
                        ? `${deviceDetail[0]?.nick_name} Cam`
                        : 'Child Cam'}
                    </Text>
                  )}
                </TouchableOpacity>
                {/* live */}
                <View
                  style={{
                    backgroundColor: isBleConnected
                      ? BaseColor.alertRed
                      : BaseColor.whiteColor,
                    paddingHorizontal: 8,
                    height: 22,
                    borderRadius: 17,
                    position: 'absolute',
                    top: isLandscape ? 130 : null,
                    bottom: 25,
                    right: isLandscape ? 40 : 28,
                    zIndex: 9999,
                    flexDirection: 'row',
                    justifyContent: 'space-evenly',
                    alignItems: 'center',
                  }}>
                  {isBleConnected && (
                    <View
                      style={{
                        width: 10,
                        height: 10,
                        marginRight: 3,
                        borderRadius: 5,
                        backgroundColor: 'white', // Change the color as per your preference
                        opacity: isVisible ? 1 : 0,
                      }}
                    />
                  )}
                  <TouchableOpacity
                    onPress={() => {
                      setTapLoader(true);
                    }}>
                    <Text
                      style={{
                        color: isBleConnected
                          ? BaseColor.whiteColor
                          : BaseColor.blackColor,
                        fontFamily: FontFamily.regular,
                        fontSize: 12,
                      }}>
                      {isBleConnected ? 'Live' : 'Not connected'}
                    </Text>
                  </TouchableOpacity>
                </View>
                {/* map */}
                <TouchableOpacity
                  style={{
                    height:
                      !isBleConnected || onTap
                        ? isLandscape
                          ? '100%'
                          : dWidth + 40
                        : 91,
                    width:
                      !isBleConnected || onTap
                        ? isLandscape
                          ? '100%'
                          : dWidth
                        : 138,
                    right: !isBleConnected || onTap ? 0 : isLandscape ? 20 : 20,
                    borderRadius: isLandscape
                      ? !isBleConnected || onTap
                        ? 0
                        : 10
                      : 10,
                    top: isLandscape
                      ? !isBleConnected || onTap
                        ? null
                        : null
                      : null,
                    bottom: isLandscape
                      ? !isBleConnected || onTap
                        ? 0
                        : 20
                      : !isBleConnected || onTap
                      ? 0
                      : 20,
                    borderWidth: !isBleConnected || onTap ? 0 : 0.5,
                    position: 'absolute',
                    zIndex: !isBleConnected || onTap ? 9 : 999,
                    backgroundColor: BaseColor.textGrey,
                    overflow: 'hidden',
                    borderColor: '#EAEFEF',
                  }}>
                  <TouchableOpacity
                    style={{
                      height: onTap ? '40%' : '100%',
                      width: onTap ? '38%' : '100%',
                      position: 'absolute',
                      borderRadius: 20,
                      overflow: 'hidden',
                      zIndex: onTap ? 999 : 9,
                    }}
                    onPress={() => {
                      setTapLoader(true);
                    }}
                  />
                  <ViewShot
                    ref={refM}
                    options={{
                      fileName: 'Your-File-Name',
                      format: 'jpg',
                      quality: 0.9,
                    }}>
                    <MapView
                      style={{
                        height: '100%',
                        width: '100%',
                      }}
                      customMapStyle={mapStyle}
                      region={{
                        latitude: loc?.latitude || 0,
                        longitude: loc?.longitude || 0,
                        latitudeDelta: 0.015,
                        longitudeDelta: 0.0121,
                      }}
                      pitchEnabled={true}
                      rotateEnabled={false}
                      scrollEnabled={true}
                      zoomEnabled={true}>
                      {isBleConnected ? (
                        <MapMarker
                          coordinate={{
                            latitude: loc?.latitude || 0,
                            longitude: loc?.longitude || 0,
                          }}>
                          <View
                            style={{
                              backgroundColor: BaseColor.transparentWhite,
                              height: 40,
                              width: 40,
                              borderRadius: 15,
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}>
                            <Image
                              source={require('../../assets/images/smallQ.png')}
                              style={{
                                height: 40,
                                width: 40,
                              }}
                            />
                          </View>
                        </MapMarker>
                      ) : (
                        <MapMarker
                          coordinate={{
                            latitude: loc?.latitude || 0,
                            longitude: loc?.longitude || 0,
                          }}
                        />
                      )}
                      {!isEmpty(placeLocation) && (
                        <MapMarker
                          coordinate={{
                            latitude: placeLocation?.latitude || 0,
                            longitude: placeLocation?.longitude || 0,
                          }}
                          title={'Destination'}
                          description={'This is your destination.'}>
                          <View
                            style={{
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: 30,
                              height: 30,
                              borderRadius: 30,
                              // padding: 5,
                              backgroundColor: '#bfbdfc',
                            }}>
                            <Iconin name="location" size={25} color="blue" />
                          </View>
                        </MapMarker>
                      )}

                      {!isEmpty(placeLocation) && (
                        <MapViewDirections
                          origin={{
                            latitude: loc?.latitude || 0,
                            longitude: loc?.longitude || 0,
                          }}
                          destination={{
                            latitude: placeLocation?.latitude || 0,
                            longitude: placeLocation?.longitude || 0,
                          }}
                          apikey={environment.mapKey}
                          strokeWidth={3}
                          strokeColor="blue"
                          onReady={result => {
                            setDistance(result?.distance);
                          }}
                        />
                      )}
                    </MapView>
                  </ViewShot>
                </TouchableOpacity>
              </>
            )}
          </View>

          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              padding: 10,
              borderTopStartRadius: 40,
              borderTopEndRadius: 40,
              marginTop: 10,
              paddingBottom: 200,
            }}>
            <View
              style={{
                backgroundColor: BaseColor.whiteColor,
                height: 60,
                flexDirection: 'row',
                borderRadius: 10,
                justifyContent: 'space-between',
                // alignContent: "center",
                // alignItems: "center",
                paddingHorizontal: 30,
                borderWidth: 0.3,
                borderColor: '#D6EBED',
                elevation: 0.1,
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.05,
                shadowRadius: 1.84,
              }}>
              <View style={{alignItems: 'center', marginTop: 5}}>
                <Text
                  style={{
                    fontFamily: FontFamily.regular,
                    color: BaseColor.blackColor,
                    fontSize: 10,
                  }}>
                  Distance (Km)
                </Text>
                <Text
                  style={{
                    fontFamily: FontFamily.regular,
                    color: BaseColor.blackColor,
                    fontSize: 16,
                    marginTop: 5,
                  }}>
                  {distance?.toFixed(2) || 0}
                </Text>
              </View>
              <View style={{alignItems: 'center', marginTop: 5}}>
                <Text
                  style={{
                    fontFamily: FontFamily.regular,
                    color: BaseColor.blackColor,
                    fontSize: 10,
                  }}>
                  IR Value
                </Text>
                <Text
                  style={{
                    fontFamily: FontFamily.regular,
                    color: BaseColor.blackColor,
                    fontSize: 16,
                    marginTop: 5,
                  }}>
                  {ir}
                </Text>
              </View>
              <View style={{alignItems: 'center', marginTop: 5}}>
                <Text
                  style={{
                    fontFamily: FontFamily.regular,
                    color: BaseColor.blackColor,
                    fontSize: 10,
                  }}>
                  Time (minutes)
                </Text>
                <Text
                  style={{
                    fontFamily: FontFamily.regular,
                    color: BaseColor.blackColor,
                    fontSize: 16,
                    marginTop: 5,
                  }}>
                  {formattedTime}
                </Text>
              </View>
            </View>
            {!isLandscape && (
              <>
                {imageUriArr.length ? (
                  <Text
                    style={{
                      fontFamily: FontFamily.regular,
                      fontSize: 16,
                      color: '#333333',
                      marginTop: 20,
                      marginLeft: 5,
                    }}>
                    Saved Snapshots
                  </Text>
                ) : null}

                <FlatList
                  data={imageUriArr}
                  renderItem={render}
                  keyExtractor={(item, index) => index}
                  bounces
                  showsVerticalScrollIndicator={false}
                  numColumns={3}
                />

                {!isEmpty(placeLocation) && (
                  <View
                    style={{
                      width: '100%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginTop: '20%',
                      flex: 1,
                    }}>
                    <CButton
                      title={'Stop Trip'}
                      style={[
                        {
                          // width: 309,
                          height: 54,
                          backgroundColor: BaseColor.primary,
                          borderRadius: 8,
                          // marginTop: 33,
                          marginHorizontal: 16,
                        },
                      ]}
                      paddingVertical={4}
                      titleStyle={{
                        fontSize: 20,
                        fontWeight: 'normal',
                        fontFamily: FontFamily.bold,
                      }}
                      onPress={() => {
                        setTimeout(() => {
                          // setLoader(true);
                        }, 1000);
                        setTimeout(() => {
                          // setLoader(false);
                          // setdone(true);
                        }, 3000);
                        setDistance(0);

                        dispatch(setTimerState(false));
                        dispatch(setTripTimerValue(timing));
                        navigation.navigate('Bookmark', {
                          tripEnd: true,
                          tripName: placeLocation.tripName,
                        });
                      }}
                      // loader={loader}
                      // done={done}
                      anim
                    />
                  </View>
                )}
              </>
            )}
          </View>
        </>
      </ScrollView>
    </View>
  );
};

export default Dashboard;
