/* eslint-disable global-require */
/* eslint-disable quotes */
import {useTheme} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  Platform,
  Text,
  TouchableOpacity,
  View,
  Dimensions,
  ScrollView,
} from 'react-native';
import {useSelector} from 'react-redux';
import {isArray} from 'lodash';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import Toast from 'react-native-simple-toast';
import Share from 'react-native-share';
import RNFetchBlob from 'react-native-blob-util';
import CHeader from '../../components/CHeader';
import {translate} from '../../lang/Translate';
import styles from './styles';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import {FontFamily} from '../../config/typography';
import CAlert from '../../components/CAlert';
import {Alert} from 'react-native';
import {sendErrorReport} from '../../utils/commonFunction';

/**
 *
 *@module SnapShotView
 *
 */
export default function SnapShotView({navigation, route}) {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const {height: dHeight, width: dWidth} = Dimensions.get('window');
  const token = useSelector(state => state.auth.accessToken);
  const imageUri = route?.params?.imageUri;
  console.log('save image', imageUri);
  const [AlerModal1, setAlerModal1] = useState({
    visible: false,
    title: '',
    message: '',
  });
  const {isBleConnected, isConnectedNet} = useSelector(
    state => state.bluetooth,
  );
  const [path, setPath] = useState('fff');
  useEffect(() => {
    if (isBleConnected) {
      // downloadVideo();
    }
  }, []);

  const shareImage = url => {
    if (
      imageUri &&
      (imageUri.includes('http://') || imageUri.includes('https://'))
    ) {
      const shareResponse = Share.open({
        url,
        message: `Shared snapshot`,
      })
        .then(res1 => {
          console.log(res1);
        })
        .catch(err => {
          console.log(err);
        });
      console.log(shareResponse);
    } else {
      Toast.show(translate('urlError'));
    }
  };
  const onShare = async type => {
    try {
      if (type === 'share') {
        const {fs} = RNFetchBlob;
        let imagePath = null;
        RNFetchBlob.config({
          fileCache: true,
        })
          .fetch('GET', imageUri)
          // the image is now dowloaded to device's storage
          .then(resp => {
            // the image path you can use it directly with Image component
            imagePath = resp.path();
            return resp.readFile('base64');
          })
          .then(base64Data => {
            // here's base64 encoded image
            const base64 = `data:image/png;base64,${base64Data}`;
            shareImage(base64);
            // remove the file from storage
            return fs.unlink(imagePath);
          });
      } else {
        const result = await Share.open({
          message: `Shared snapshot`,
          // url: shareText,
        });
        if (result.action === Share.sharedAction) {
          if (result.activityType) {
            // shared with activity type of result.activityType
          } else {
            // shared
          }
        } else if (result.action === Share.dismissedAction) {
          // dismissed
        }
      }
    } catch (error) {
      console.log('error: ==>', error);
    }
  };

  return (
    <View style={[styles.root, {backgroundColor: BaseColor.homeBgColor}]}>
      {/* <GradientBack /> */}
      <View style={styles.root}>
        <CHeader
          title={translate('Snapshot - image')}
          backBtn
          onLeftPress={() => {
            navigation.goBack();
          }}
        />
        <ScrollView
          contentContainerStyle={{paddingBottom: 0, marginTop: 0}}
          bounces={false}>
          {/* <Text>{path}</Text> */}
          <Image
            source={{
              uri: imageUri,
            }}
            style={{
              height: 380,
              width: dWidth - 20,
              borderRadius: 40,
              marginHorizontal: 10,
            }}
            resizeMode="cover"
          />
          <View
            style={{
              backgroundColor: '#5F5F5F',
              padding: 18,
              borderTopStartRadius: 40,
              borderTopEndRadius: 40,
              marginTop: 15,
              paddingBottom: 200,
            }}>
            <View
              style={{
                backgroundColor: BaseColor.infoView,
                height: 84,
                flexDirection: 'row',
                borderRadius: 35,
                justifyContent: 'space-around',
                alignContent: 'center',
                alignItems: 'center',
                paddingHorizontal: 30,
              }}>
              <TouchableOpacity
                style={{
                  alignItems: 'center',
                  backgroundColor: BaseColor.blueDark,
                  height: 44,
                  width: 44,
                  borderRadius: 22,
                  justifyContent: 'center',
                }}
                onPress={() => {
                  onShare('share');
                }}>
                <FAIcon
                  name="cloud-upload"
                  size={20}
                  color={BaseColor.whiteColor}
                />
              </TouchableOpacity>
              <View
                style={{
                  height: 70,
                  opacity: 0.2,
                  width: 1,
                  backgroundColor: BaseColor.blackColor,
                }}
              />
              <TouchableOpacity
                style={{
                  alignItems: 'center',
                  backgroundColor: BaseColor.whiteColor,
                  height: 44,
                  width: 44,
                  borderRadius: 22,
                  borderWidth: 2,
                  borderColor: BaseColor.blueDark,
                  justifyContent: 'center',
                }}
                onPress={() => {
                  setAlerModal1({
                    visible: true,
                    title: 'Delete',
                    message: 'Are you sure you want to delete?',
                  });
                }}>
                <FAIcon name="trash" size={20} color={BaseColor.blueDark} />
              </TouchableOpacity>
            </View>
            <View
              style={{
                backgroundColor: BaseColor.blackColor,
                height: 1,
                marginHorizontal: 20,
                marginVertical: 20,
                opacity: 0.2,
              }}
            />
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignContent: 'center',
                alignItems: 'center',
              }}>
              <Image
                source={{
                  uri: imageUri,
                }}
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: 30,
                  marginHorizontal: 20,
                }}
              />
              <View>
                <Text
                  style={{
                    fontFamily: FontFamily.regular,
                    size: 16,
                    lineHeight: 20,
                    color: BaseColor.whiteColor,
                    letterSpacing: 0.7,
                  }}>
                  Snapshot - {route?.params?.loc}
                </Text>
                <Text
                  style={{
                    size: 12,
                    lineHeight: 20,
                    color: BaseColor.whiteColor,
                  }}>
                  {route?.params?.date}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
      <CAlert
        visible={AlerModal1.visible}
        onRequestClose={() =>
          setAlerModal1({
            ...AlerModal1,
            visible: false,
          })
        }
        onCancelPress={() =>
          setAlerModal1({
            ...AlerModal1,
            visible: false,
          })
        }
        onOkPress={async () => {
          setAlerModal1({
            ...AlerModal1,
            visible: false,
          });
          // delete-===============
          // setTimeout(() => {
          route?.params?.updatedIndex(
            route?.params?.index || route?.params?.id,
          );
          navigation.goBack();
          // }, 1000);
        }}
        alertTitle={AlerModal1.title}
        alertMessage={AlerModal1.message}
        agreeTxt={translate('delete')}
      />
    </View>
  );
}
