/* eslint-disable eqeqeq */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
/* eslint-disable no-fallthrough */
/* eslint-disable max-len */
/* eslint-disable no-console */
/* eslint-disable quotes */
/**
 * Sample BLE React Native App
 *
 * @format
 * @flow strict-local
 */

import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  NativeModules,
  NativeEventEmitter,
  Platform,
  PermissionsAndroid,
  BackHandler,
  Text,
  Dimensions,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import _, {
  find,
  findIndex,
  isArray,
  isBoolean,
  isEmpty,
  isObject,
  isUndefined,
} from 'lodash';
import BleManager from 'react-native-ble-manager';
import BluetoothStateManager from 'react-native-bluetooth-state-manager';
import {useDispatch, useSelector} from 'react-redux';
import Toast from 'react-native-simple-toast';
import {openSettings} from 'react-native-permissions';
import _BackgroundTimer from 'react-native-background-timer';
import styles from './styles';
import CHeader from '../../components/CHeader';
import {translate} from '../../lang/Translate';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {sendErrorReport} from '../../utils/commonFunction';
import AuthAction from '../../redux/reducers/auth/actions';

const BleManagerModule = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);

/**
 *
 *@module BleList
 *
 */
const BleList = ({navigation}) => {
  const dispatch = useDispatch();
  const token = useSelector(state => state.auth.accessToken);
  const languageData = useSelector(state => state.language);
  const [isScanning, setIsScanning] = useState(false);
  const peripherals = new Map();
  const [list, setList] = useState([]);
  const connectedDeviceDetail = useSelector(
    state => state.bluetooth.connectedDeviceDetail,
  );
  const {isClickAddQr, bleDeviceList, isSkipShow, isBleConnected} = useSelector(
    state => state.bluetooth,
  );

  const {
    step5Done,

    closeOnboarding,
  } = useSelector(state => state.auth);
  // const [connnectedID, setconnnectedID] = useState("");
  const [isRefreshing, setisRefreshing] = useState(false);
  const [refresh, setrefresh] = useState(false);
  const [deviceId, setDeviceId] = useState(null);
  const [scanningText, setScanningText] = useState(
    translate('searchingQRCode'),
  );
  const [showStep5, setShowStep5] = useState(false);
  const [showStep5c, setShowStep5c] = useState(false);
  const [showStep5d, setShowStep5d] = useState(false);

  useEffect(() => {
    if (!step5Done && !closeOnboarding) {
      setShowStep5(true);
    }
  }, []);

  const startScan = () => {
    if (!isScanning) {
      BleManager.scan([], 3, true)
        .then(results => {
          setIsScanning(true);
          setrefresh(false);
        })
        .catch(err => {
          setrefresh(false);
          console.error(err);
          sendErrorReport(err, 'scan_error');
        });
    }
  };

  useEffect(() => {
    setTimeout(() => {
      startScan();
    }, 1500);
  }, []);

  useEffect(() => {
    dispatch(BluetoothAction.setBleDeviceList(list));
  }, [list]);

  const handleStopScan = () => {
    setIsScanning(false);
    setisRefreshing(false);
    dispatch(BluetoothAction.setClickAddQr(false));
  };

  const handleDiscoverPeripheral = peripheral => {
    if (!peripheral.name) {
      peripheral.name = 'NO NAME';
    }
    peripherals.set(peripheral.id, peripheral);
    setList(Array.from(peripherals.values()));
  };

  useEffect(() => {
    /* Listening to IOS Background events as per the docs - Not Tested */
    if (isClickAddQr || isRefreshing) {
      bleManagerEmitter.addListener(
        'BleManagerCentralManagerWillRestoreState',
        data => {
          console.log(
            'BLE ==> BleManagerCentralManagerWillRestoreState ===> ',
            data,
          );
        },
      );

      bleManagerEmitter.addListener(
        'BleManagerDiscoverPeripheral',
        handleDiscoverPeripheral,
      );
      bleManagerEmitter.addListener('BleManagerStopScan', handleStopScan);

      if (Platform.OS === 'android' && Platform.Version >= 23) {
        console.log('called---5');
        PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ).then(result => {
          if (result) {
            console.log('Permission is OK');
          } else {
            PermissionsAndroid.request(
              PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            ).then(res => {
              if (res) {
                console.log('User accept');
              } else {
                console.log('User refuse');
              }
            });
          }
        });
      }
    }

    return () => {
      console.log('unmount');
    };
  }, [isClickAddQr, isRefreshing]);

  function handleBackButtonClick() {
    navigation.navigate(translate('home'));
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  /** this function for onReadQRList
   * @function onReadQRList
   * @param {object} data device_bluetooth_name
   */

  const onReadQRList = async item => {
    sendErrorReport(item, 'item__item');
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getDevice,
        'POST',
        {
          device_ssid: item?.name,
          product_id: item?.id,
          lang_code: languageData?.languageData || 'es',
        },
        headers,
      );

      if (response.success && !isEmpty(response.data)) {
        setDeviceId(response?.data?.id);
        if (isArray(connectedDeviceDetail)) {
          const obj = {...response?.data};
          obj.product_id = item?.id;

          const arr = [...connectedDeviceDetail] || [];
          const index = findIndex(
            arr,
            lt => lt?.product_id === obj?.product_id,
          );
          if (index > -1) {
            arr[index] = obj;
          } else {
            arr.unshift(obj);
          }
          dispatch(BluetoothAction.setConnectedDeviceDetail(arr));
          dispatch(BluetoothAction.setConnectedDeviceDetails(arr));
          dispatch(BluetoothAction.setSwiperKey(obj?.product_id));
          dispatch(BluetoothAction.setActiveDeviceId(obj));
        }
        if (isObject(item) && !isEmpty(item)) {
          dispatch(BluetoothAction.setDeviceID(''));
          setTimeout(() => {
            dispatch(BluetoothAction.setDeviceID(item.id));
            dispatch(BluetoothAction.setLastDeviceId(item.id));
            dispatch(BluetoothAction.setIsConnectLoad(true));
            dispatch(BluetoothAction.setClickAddQr(false));
            navigation.navigate('Connect', {
              product_id: item?.id,
              device_id: response?.data?.id || deviceId,
              device_data: item?.name,
              device_ssid: item?.name,
            });
          }, 2500);
        } else {
          setIsScanning(false);
          Toast.show(translate('cannotFindDevice'));
          startScan();
        }
      } else {
        Toast.show(response.message);
        setScanningText(translate('searchingQRCode'));
      }
    } catch (error) {
      console.log('error device detail ===', error);
      sendErrorReport(error, 'on_read_qr');
    }
  };

  const NoPermissionViewIos = (
    <View
      style={{
        width: Dimensions.get('window').width * 0.8,
        alignSelf: 'center',
      }}>
      <Text style={styles.qrTextStyle}>{translate('noCameraAcces')}</Text>
      <TouchableOpacity onPress={() => openSettings()}>
        <Text style={styles.openSettingsText}>{translate('openSettings')}</Text>
      </TouchableOpacity>
    </View>
  );
  const checkBTStatus = () => {
    BluetoothStateManager.getState().then(bluetoothState => {
      switch (bluetoothState) {
        case 'Unknown':
        case 'Resetting':
        case 'Unsupported':
        case 'Unauthorized':
        case 'PoweredOff':
          // setShowStep5d(true);
          sendErrorReport('step5c', 'step5c_device_list');
          setShowStep5c(true); // m
          break;
        case 'PoweredOn':
          sendErrorReport('step5d', 'step5d_device_list');
          setShowStep5d(true); // m
          break;
        default:
          break;
      }
    });
  };
  const renderItem = ({item}) =>
    item?.name.includes('ESP32_CAM') ? (
      <View
        style={{
          padding: 12,
          borderRadius: 8,
          backgroundColor: '#fff',
          shadowColor: '#000',
          margin: 8,
          sshadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
          elevation: 5,
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
          {/* <Text style={{ fontSize: 18, fontWeight: "700" }}>NAME : </Text> */}
          <Text
            style={{
              fontSize: 18,
              marginStart: 8,
              flex: 1,
              fontWeight: '700',
            }}>
            {item?.name == 'NO NAME' ? 'N/A' : item?.name}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor:
                item?.advertising?.isConnectable == 1 ? 'green' : 'green',
              padding: 8,
              borderRadius: 8,
            }}
            activeOpacity={0.7}
            onPress={() => {
              onReadQRList(item);
            }}>
            <Text style={{color: '#fff'}}>CONNECT</Text>
          </TouchableOpacity>
        </View>
        <View style={{flexDirection: 'row'}}>
          <Text style={{fontSize: 15, marginStart: 8, flex: 1}}>
            {item?.id}
          </Text>
        </View>
      </View>
    ) : null;

  const onRefresh = () => {
    setisRefreshing(true);
    BleManager.stopScan().then(() => {
      // Success code
      console.log('Scan stopped');
    });
    setTimeout(() => {
      startScan();
    }, 1000);
  };

  return (
    <View style={styles.root}>
      {/* <GradientBack /> */}
      <CHeader
        title={translate('connectToYourBabyAuto')}
        // backBtn
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.navigate(translate('home'));
          dispatch(BluetoothAction.setClickAddQr(false));
        }}
      />
      <View style={{flex: 1}}>
        <FlatList
          data={bleDeviceList}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          contentContainerStyle={{flexGrow: 1}}
          onRefresh={onRefresh}
          refreshing={isRefreshing}
          ListEmptyComponent={() => (
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text style={{fontWeight: 'bold', color: '#000'}}>
                No Device Available
              </Text>
            </View>
          )}
        />
      </View>
    </View>
  );
};

export default BleList;
