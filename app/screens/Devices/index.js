/* eslint-disable array-callback-return */
/* eslint-disable global-require */
/* eslint-disable quotes */
import React, {useEffect, useState, useCallback} from 'react';
import {
  FlatList,
  Text,
  View,
  Image,
  TouchableOpacity,
  BackHandler,
  RefreshControl,
  ActivityIndicator,
  Platform,
  Dimensions,
} from 'react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome5';
import _, {sample} from 'lodash';
import Toast from 'react-native-simple-toast';
import {useDispatch, useSelector} from 'react-redux';
import {useFocusEffect, useIsFocused, useTheme} from '@react-navigation/native';
import styles from './styles';
import {translate} from '../../lang/Translate';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {sendErrorReport} from '../../utils/commonFunction';
import AuthAction from '../../redux/reducers/auth/actions';
import bluetoothActions from '../../redux/reducers/bluetooth/actions';
import Orientation from 'react-native-orientation-locker';
import SubHeader from '../../components/SubHeader';
import {FontFamily} from '../../config/typography';
import CButton from '../../components/CButton';
import MultiStepSlider from '../../components/MultiSlider/MultiSlider';

let backPressed = 0;

const {width, height} = Dimensions.get('window');

/**
 *
 *@module Devices
 *
 */
const Devices = ({navigation, route}) => {
  const {type} = route?.params;
  const isFocused = useIsFocused();
  const colors = useTheme();
  const BaseColor = colors.colors;
  const dispatch = useDispatch();
  const {setNotiCount} = AuthAction;
  const {setDeviceDetail} = bluetoothActions;
  const [loader, setloader] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const token = useSelector(state => state.auth.accessToken);
  const [devices, setDevices] = useState([
    {
      type: 'add',
    },
  ]);

  /** this function for get Badge Count
   * @function getBadgeCount
   * @param {object} data {}
   */
  async function getBadgeCount() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getUserAlertCount,
        'POST',
        {},
        headers,
      );

      if (response.success) {
        dispatch(setNotiCount(response));
      } else {
        console.log(
          '🚀 ~ file: index.js ~ line 15ewe ~ getBadgeCount ~ response',
          response,
        );
      }
    } catch (error) {
      console.log('error for device list ===', error);
      sendErrorReport(error, 'get_device_list');
    }
  }

  const onRefresh = React.useCallback(() => {
    getChildInfo();
  }, []);

  /** this function for get Child Information
   * @function getChildInfo
   * @param {object} data {}
   */
  const getChildInfo = () => {
    setloader(true);
    const childData = {
      platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
    };
    getApiData(BaseSetting.endpoints.getUserChild, 'POST', childData, '', true)
      .then(response => {
        if (response.success) {
          const tempArr = [
            {
              type: 'add',
            },
          ];
          const childArr = response.data;
          dispatch(setDeviceDetail(response.data));
          childArr.map(item => {
            tempArr.push(item);
          });
          setDevices(tempArr);
        } else {
          Toast.show(response.message);
        }
        setloader(false);
        setRefreshing(false);
      })
      .catch(err => {
        Toast.show('Something went wrong while getting child detail');
        sendErrorReport(err, 'get_child_in_device3');
        setRefreshing(false);
      });
  };

  const render = ({item}) => {
    return (
      <View style={styles.renderView}>
        {item.type === 'add' ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={() => {
              navigation.navigate('ChildInfo', {type: 'add'});
            }}
            style={[styles.addNewProfile, {alignItems: 'center'}]}>
            <View
              style={{
                width: 50,
                height: 50,
                borderRadius: 30,
                justifyContent: 'center',
                backgroundColor: BaseColor.primary,
              }}>
              <Text style={{textAlign: 'center', color: BaseColor.whiteColor}}>
                +
              </Text>
            </View>
            <View
              style={{
                marginVertical: 10,
                width: '80%',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text
                style={[
                  styles.title,
                  {width: '50%', textAlign: 'center', lineHeight: 20},
                ]}>
                {translate('addNew')}
              </Text>
            </View>
          </TouchableOpacity>
        ) : (
          <View>
            <TouchableOpacity
              activeOpacity={0.8}
              style={styles.addNewProfile}
              onPress={() => {
                navigation.navigate('ChildInfo', {type: 'edit', item: item});
                // navigation.navigate('CDeviceList', {
                //   deviceDetail: item.deviceDetails || [],
                //   title: item.nick_name || '',
                //   item,
                // });
              }}>
              <View
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Image
                  style={styles.image}
                  source={{uri: item.child_profile}}
                />
                <View style={styles.contentTextView}>
                  <Text
                    style={[styles.title, {color: BaseColor.blackColor}]}
                    numberOfLines={1}>
                    {item.nick_name}
                  </Text>
                  <Text
                    style={[
                      styles.connectedDevice,
                      {color: BaseColor.textGrey},
                    ]}>
                    {`DOB ${item?.date_of_birth}`}
                  </Text>
                  <Text
                    style={{
                      color: BaseColor.blueDark,
                      fontSize: 12,
                    }}>
                    {item.device_connection === 'Active'
                      ? translate('activeDevice')
                      : translate('deactivedDevice') || ''}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  useEffect(() => {
    getChildInfo();
    getBadgeCount();
  }, [isFocused]);

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={styles.root}>
      <SubHeader
        title={translate('childProfileScreen')}
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <Text
        style={{
          marginHorizontal: 15,
          fontSize: 20,
          fontWeight: '500',
        }}>
        Active Profiles
      </Text>
      <View style={[{marginHorizontal: 20, marginBottom: 20}]}>
        {loader ? (
          <View
            style={{
              justifyContent: 'center',
              height: height / 4,
            }}>
            <ActivityIndicator size={42} color={BaseColor.primary} />
          </View>
        ) : (
          <FlatList
            horizontal
            data={devices}
            renderItem={render}
            keyExtractor={(item, index) => index}
            showsHorizontalScrollIndicator={false}
            style={{marginBottom: 30}}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          />
        )}
        {type === 'registerSucess' && (
          <>
            <View style={{justifyContent: 'center', alignItems: 'center'}}>
              <Text
                style={{
                  fontSize: 24,
                  fontFamily: FontFamily.bold,
                  color: BaseColor.primary,
                  marginBottom: 15,
                }}>
                Congratulations!
              </Text>
              <Text
                style={{
                  textAlign: 'center',
                  fontSize: 14,
                  fontFamily: FontFamily.robotoLight,
                  lineHeight: 18,
                  color: '#787E7E',
                }}>
                {`Child profile is added successfully! \n You can also add multiple profiles on this page \n You can access them in the “Settings” page`}
              </Text>
            </View>
            <View style={{marginTop: '15%'}}>
              <CButton
                onPress={() => navigation.navigate('DrawerNav')}
                style={{width: '80%'}}
                title={translate('Go to HomePage')}
              />
            </View>
          </>
        )}
      </View>
      <View
        style={{
          flex: 1,
          justifyContent: 'flex-end',
          marginHorizontal: width / 9,
          marginBottom: 25,
        }}>
        <MultiStepSlider />
      </View>
    </View>
  );
};

export default Devices;
