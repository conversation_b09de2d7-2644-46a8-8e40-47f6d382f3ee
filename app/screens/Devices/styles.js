import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const {width, height} = Dimensions.get('window');
const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
  },
  flatlistView: {
    marginTop: 30,
    marginBottom: 10,
  },
  addNewProfile: {
    height: height / 6,
    width: 100,
    borderWidth: 1,
    borderRadius: 10,
    backgroundColor: BaseColor.whiteColor,
    borderColor: BaseColor.whiteColor,
    justifyContent: 'center',
    elevation: 2,
    shadowColor: 'black',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
  },
  addText: {
    textAlign: 'center',
    fontSize: 20,
    color: BaseColor.blueDark,
    fontFamily: FontFamily.default,
  },
  tapText: {
    textAlign: 'center',
    fontSize: 12,
    paddingVertical: 4,
  },
  renderView: {
    flexDirection: 'row',
    marginVertical: 20,
    paddingHorizontal: 10,
    height: height / 6,
  },
  checkIcon: {
    backgroundColor: BaseColor.whiteColor,
    width: 28,
    height: 28,
    borderRadius: 14,
    textAlignVertical: 'center',
    marginRight: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColor.textGrey,
  },
  touchableContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: BaseColor.whiteColor,
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
    borderRadius: 20,
  },
  image: {
    width: 50,
    height: 50,
    borderRadius: 30,
  },
  title: {
    color: '#1A1A1A',
    fontSize: 13,
    fontWeight: '600',
    // fontFamily: FontFamily.bold,
  },
  connectedDevice: {
    color: BaseColor.textGrey,
    fontSize: 10,
    paddingVertical: 2,
  },
  contentTextView: {
    marginVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default styles;
