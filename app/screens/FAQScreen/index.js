/* eslint-disable indent */
/* eslint-disable react/jsx-indent */
/* eslint-disable quotes */
import React, { useEffect, useState } from "react";
import { ActivityIndicator, Back<PERSON><PERSON><PERSON>, <PERSON>rollView, View } from "react-native";
import { useSelector } from "react-redux";
import { isArray } from "lodash";
import Toast from "react-native-simple-toast";
import Accordian from "../../components/Accordian";
import CHeader from "../../components/CHeader";
import GradientBack from "../../components/gradientBack";
import BaseSetting from "../../config/setting";
import { getApiData } from "../../utils/apiHelper";
import BaseColor from "../../config/colors";
import styles from "./styles";
import { sendErrorReport } from "../../utils/commonFunction";
import CustomHeader from "../../components/CustomHeader/CustomHeader";

/**
 *
 *@module FAQ
 *
 */
export default function FAQScreen({ navigation }) {
  const [FAQS, setFAQS] = useState([]);
  const token = useSelector((state) => state.auth.accessToken);
  const [pageLoad, setPageLoad] = useState(true);
  const languageData = useSelector((state) => state.language.languageData);

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  // this function for get faq's list
  /** this function for get faq's list
   * @function getFAQs
   * @param {object} data {}
   */
  async function getFAQs() {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.faqsList,
        "POST",
        {
          language_code: languageData,
        },
        headers
      );

      if (response.success) {
        setFAQS(response.data);
      } else {
        Toast.show(response.message);
      }
      setPageLoad(false);
    } catch (error) {
      setPageLoad(false);
      sendErrorReport(error, "get_Faq");
      console.log("feed post error ===", error);
    }
  }

  useEffect(() => {
    getFAQs();
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  return (
    <View style={{ flex: 1, backgroundColor: BaseColor.whiteColor }}>
      <CustomHeader
        title="FAQ"
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
        backBtn
        rightIconName="bell-thick"
        onRightPress={() => {
          navigation.navigate("Alerts");
        }}
      />
      {pageLoad ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator color={BaseColor.whiteColor} />
        </View>
      ) : (
        <ScrollView
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {isArray(FAQS) && FAQS.length > 0
            ? FAQS.map((fq) => (
                <Accordian
                  key={`fq_${fq.id}`}
                  title={fq.title}
                  data={fq.description}
                />
              ))
            : null}
        </ScrollView>
      )}
    </View>
  );
}
