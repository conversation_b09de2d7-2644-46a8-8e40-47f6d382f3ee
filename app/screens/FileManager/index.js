/* eslint-disable quotes */
import React, {useEffect, useRef, useState} from 'react';
import {
  FlatList,
  View,
  BackHandler,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
  Alert,
  Platform,
  ActivityIndicator,
} from 'react-native';
import Toast from 'react-native-simple-toast';
import {useSelector} from 'react-redux';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import WebView, {WebViewMessageEvent} from 'react-native-webview';
import styles from './styles';
import CHeader from '../../components/CHeader';
import BaseColor from '../../config/colors';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {sendErrorReport} from '../../utils/commonFunction';
import {FontFamily} from '../../config/typography';
import BaseColors from '../../config/colors';
import RNFetchBlob from 'react-native-blob-util';
import CustomHeader from '../../components/CustomHeader/CustomHeader';
import {useDeviceOrientationChange} from 'react-native-orientation-locker';
let backPressed = 0;

const FileManager = ({navigation}) => {
  const accessToken = useSelector(state => state.auth.accessToken);
  const aviFiles = useSelector(state => state.bluetooth.aviFiles);
  console.log('avi filesss----', aviFiles);
  const [imageUriArr, setImgUriArr] = useState(aviFiles);
  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      // Toast.show("Press Again To Exit");
      navigation.goBack();
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }
  const {fs} = RNFetchBlob;
  const {isBleConnected} = useSelector(state => state.bluetooth);
  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  useEffect(() => {
    setImgUriArr(aviFiles);
  }, [aviFiles]);

  // http://192.168.4.1:8080/c?dwn=//${name}
  const fetchVideo = async (item, index) => {
    try {
      const {DownloadDir, DocumentDir} = fs.dirs;
      const downloadsPath = Platform.OS === 'ios' ? DocumentDir : DownloadDir;
      const videoFilename = item.name; // Replace with the actual filename of your video
      const videoPath = `${downloadsPath}/${videoFilename}`;

      const videoExists = await fs.exists(videoPath);
      console.log('videoPath============', videoPath);
      if (videoExists) {
        const newArr = [...imageUriArr];
        newArr[index].status = true;
        setImgUriArr(newArr);
        console.log('Video file  exist.============', videoPath);
      } else {
        const newArr = [...imageUriArr];
        newArr[index].status = false;
        setImgUriArr(newArr);
        console.log('Video file does not ex?????????');
      }
    } catch (error) {
      console.log('Error fetching video:', error);
    }
  };
  useEffect(() => {
    imageUriArr.map((item, index) => {
      fetchVideo(item, index);
    });
  }, []);
  const renderVideoList = ({item}) => (
    <>
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignContent: 'center',
          // justifyContent: "space-evenly",
          alignItems: 'center',
        }}
        onPress={() => {
          navigation.navigate('VideoView', {
            filename: item.name,
          });
        }}>
        <View
          style={{
            height: 60,
            width: 60,
            borderRadius: 30,
            backgroundColor: BaseColor.primary,
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            // bottom: 0,
            zIndex: 99,
            // left: 40,
          }}>
          <MaterialCommunityIcons
            name="video"
            color={BaseColors.whiteColor}
            size={25}
          />
        </View>
        <Image
          source={{uri: item?.save_image}}
          style={{
            width: 60,
            height: 60,
            borderRadius: 30,
            marginRight: 20,
          }}
        />

        <View>
          <Text
            style={{
              fontFamily: FontFamily.regular,
              fontSize: 16,
              lineHeight: 20,
              color: BaseColor.whiteColor,
              letterSpacing: 0.7,
            }}>
            Snapshot - {item.name}
          </Text>
          <Text
            style={{
              fontSize: 12,
              lineHeight: 20,
              color: BaseColor.whiteColor,
            }}>
            {!item.status && 'not downloaded yet'}
          </Text>
        </View>
        {!item.status ? (
          isBleConnected ? (
            <ActivityIndicator
              size={'small'}
              color={'white'}
              style={{marginRight: 20}}
            />
          ) : null
        ) : (
          <FAIcon
            name="angle-right"
            size={24}
            color={BaseColor.whiteColor}
            style={{right: 10, position: 'absolute'}}
          />
        )}
      </TouchableOpacity>
      <View
        style={{
          backgroundColor: BaseColor.blackColor,
          height: 1,
          marginHorizontal: 20,
          marginVertical: 10,
          opacity: 0.2,
        }}
      />
    </>
  );
  const render = ({item}) => (
    <>
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignContent: 'center',
          // justifyContent: "space-evenly",
          alignItems: 'center',
        }}
        onPress={() => {
          navigation.navigate('snapShotView', {
            imageUri: item.save_image,
            loc: item.location_name,
            date: item.date_time,
          });
        }}>
        <View
          style={{
            height: 22,
            width: 22,
            borderRadius: 11,
            backgroundColor: BaseColor.primary,
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            bottom: 0,
            zIndex: 99,
            left: 40,
          }}>
          <MaterialCommunityIcons
            name="camera-outline"
            color={BaseColors.whiteColor}
            size={12}
          />
        </View>
        <Image
          source={{uri: item.save_image}}
          style={{
            width: 60,
            height: 60,
            borderRadius: 30,
            marginRight: 20,
          }}
        />

        <View>
          <Text
            style={{
              fontFamily: FontFamily.regular,
              fontSize: 16,
              lineHeight: 20,
              color: BaseColor.whiteColor,
              letterSpacing: 0.7,
            }}>
            Snapshot - {item?.location_name}
          </Text>
          <Text
            style={{
              fontSize: 12,
              lineHeight: 20,
              color: BaseColor.whiteColor,
            }}>
            {item.date_time}
          </Text>
        </View>
        <FAIcon
          name="angle-right"
          size={24}
          color={BaseColor.whiteColor}
          style={{right: 10, position: 'absolute'}}
        />
      </TouchableOpacity>
      <View
        style={{
          backgroundColor: BaseColor.blackColor,
          height: 1,
          marginHorizontal: 20,
          marginVertical: 10,
          opacity: 0.2,
        }}
      />
    </>
  );
  /** this function for get Badge Count
   * @function getSnapShotList
   * @param {object} data {}
   */
  async function getSnapShotList() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.qeridooSaveImageList,
        'GET',
        {},
        headers,
      );

      if (response.success) {
        console.log('rreeee===', response.data);
        if (response.data) {
          setImgUriArr(response.data);
        }
      } else {
        console.log('🚀 save image list err', response);
      }
    } catch (error) {
      console.log('error for save image list ere list ===', error);
      sendErrorReport(error, 'save image list er');
    }
  }

  useEffect(() => {
    getSnapShotList();
  }, []);

  const [isLandscape, setIsLandscape] = useState(false);
  useDeviceOrientationChange(ori => {
    if (ori === 'LANDSCAPE-RIGHT' || ori === 'LANDSCAPE-LEFT') {
      // Orientation.unlockAllOrientations();
      setIsLandscape(true);
    } else if (ori === 'UNKNOWN' || ori === 'FACE-UP') {
      setIsLandscape(false);
      null;
    } else if (ori === 'PORTRAIT-UPSIDEDOWN') {
      // Orientation.lockToPortrait();
      setIsLandscape(true);
    } else {
      // Orientation.lockToPortrait();
      setIsLandscape(false);
    }
  });

  return (
    <View style={styles.root}>
      <View style={{marginTop: isLandscape ? '-9%' : 0}}>
        <CustomHeader
          title="File Manager"
          leftIconName="left-arrow"
          onLeftPress={() => {
            navigation.goBack();
          }}
          backBtn
        />
      </View>
      <View style={{marginHorizontal: 20}}>
        <FlatList
          data={imageUriArr}
          renderItem={renderVideoList}
          keyExtractor={(item, index) => index}
          contentContainerStyle={{flexGrow: 1, paddingBottom: 220}}
          bounces
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={() => (
            <View style={styles.emptyComponent}>
              <Text
                style={{
                  padding: 8,
                  width: '100%',
                  fontSize: 16,
                  color: BaseColor.whiteColor,
                  textAlign: 'center',
                }}>
                NO FILES
              </Text>
            </View>
          )}
        />
      </View>
    </View>
  );
};

export default FileManager;
