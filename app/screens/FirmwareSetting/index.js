/* eslint-disable global-require */
/* eslint-disable quotes */
import {useTheme} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  Platform,
  Text,
  TouchableOpacity,
  View,
  PermissionsAndroid,
  ScrollView,
  Dimensions,
  Linking,
} from 'react-native';
import {check, PERMISSIONS} from 'react-native-permissions';
import {useSelector} from 'react-redux';
import {isArray} from 'lodash';
import RNFetchBlob from 'react-native-blob-util';
import Toast from 'react-native-simple-toast';
import SimpleToast from 'react-native-simple-toast';
import CHeader from '../../components/CHeader';
import {translate} from '../../lang/Translate';
import styles from './styles';
import {
  enableAnimateInEaseOut,
  openInAppBrowser,
} from '../../utils/commonFunction';
import {CustomIcon} from '../../config/LoadIcons';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import {FontFamily} from '../../config/typography';
import FIcon from 'react-native-vector-icons/Feather';
import AndroidOpenSettings from 'react-native-android-open-settings';
import CustomHeader from '../../components/CustomHeader/CustomHeader';

/**
 *
 *@module FirmwareSetting
 *
 */
export default function FirmwareSetting({navigation}) {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const token = useSelector(state => state.auth.accessToken);

  const [pageLoad, setPageLoad] = useState(true);

  const [deviceList, setDeviceList] = useState([]);
  console.log(
    '🚀 ~ file: index.js ~ line 42 ~ MyQRcode ~ deviceList',
    deviceList,
  );
  const [page, setPage] = useState(1);
  const {width: dWidth, height: dHeight} = Dimensions.get('window');
  useEffect(() => {
    setPage(1);
    getDeviceList();
  }, []);

  async function getDeviceList() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    const data = {
      platform: Platform.OS,
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.userManual,
        'POST',
        data,
        headers,
      );
      if (response.success && isArray(response.data)) {
        const arr = response.data;
        setDeviceList(arr);
      } else {
        setDeviceList([]);
      }
    } catch (error) {
      setDeviceList([]);
      console.log('error for device list ===', error);
    }
  }
  const checkPermission = () => {
    if (Platform.OS === 'android' && Platform.Version >= 23) {
      PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      ).then(result => {
        if (result) {
          console.log('Permission is OK');
        } else {
          PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          ).then(res => {
            if (res) {
              console.log('User accept');
            } else {
              console.log('User refuse');
            }
          });
        }
      });
    } else {
      check(PERMISSIONS.IOS.WRITE_EXTERNAL_STORAGE).then(res => {
        if (res !== 'granted') {
          console.log('permission granted === ');
        }
      });
    }
  };
  const renderProducts = ({item}) => (
    <TouchableOpacity
      style={[styles.cardRoot, {backgroundColor: BaseColor.lightgray}]}
      onPress={() => {
        checkPermission();
        console.log('dddddddddddddd file', item?.device_file);
        if (item?.device_file) {
          // openInAppBrowser(item?.device_file);
          try {
            const {fs} = RNFetchBlob;
            const {DownloadDir, DocumentDir} = fs.dirs;
            const saveFilePath =
              Platform.OS === 'ios' ? DocumentDir : DownloadDir;
            RNFetchBlob.config({
              fileCache: true,
              addAndroidDownloads: {
                useDownloadManager: true,
                notification: true,
                path: `${saveFilePath}/${
                  item?.device_name || 'Linko'
                }-UserManual.pdf`,
                description: 'Downloading.',
              },
              path: `${saveFilePath}/${
                item?.device_name || 'Linko'
              }-UserManual.pdf`,
            })
              .fetch('GET', item?.device_file, {})
              .then(res => {
                // the temp file path
                if (Platform.OS === 'ios') {
                  RNFetchBlob.ios.openDocument(res.path());
                  Toast.show('Downloded to files');
                } else {
                  Toast.show('Downloded to files');
                  console.log(
                    '🚀 ~ file: index.js ~ line 138 ~ .then ~ saveFilePath',
                    saveFilePath,
                  );
                }
              });
          } catch (error) {
            console.log(
              '🚀 ~ file: index.js ~ line 145 ~ MyQRcode ~ error',
              error,
            );
          }
        }
      }}>
      <View
        style={{
          alignSelf: 'flex-end',
          backgroundColor: BaseColor.whiteColor,
          borderRadius: 50,
          padding: 5,
        }}>
        <CustomIcon name="info1" size={26} color={BaseColor.org} />
      </View>
      <View style={styles.rowStyle}>
        <View>
          <Image
            source={
              item?.device_image
                ? {uri: item?.device_image}
                : require('../../assets/images/logo.png')
            }
            style={[styles.imgStyle, {borderColor: BaseColor.black60}]}
          />
          {item?.device_name && (
            <View
              style={{
                position: 'absolute',
                flex: 1,
                flexDirection: 'row',
                alignItems: 'center',
                bottom: 10,
                left: Platform.OS === 'ios' ? 80 : 80,
                right: Platform.OS === 'ios' ? 80 : 80,
                backgroundColor: '#fff',
                borderRadius: 12,
              }}>
              <Text style={[styles.childNameStyle]} numberOfLines={2}>
                {item.device_name}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  enableAnimateInEaseOut();

  return (
    <View style={{flex: 1}}>
      <View style={styles.root}>
        <CustomHeader
          title="Firmware setting guide"
          leftIconName="left-arrow"
          onLeftPress={() => {
            navigation.goBack();
          }}
          backBtn
          rightIconName="bell-thick"
          onRightPress={() => {
            navigation.navigate('Alerts');
          }}
        />
        <ScrollView contentContainerStyle={{flexGrow: 1}} bounces={false}>
          <View style={{marginHorizontal: 20}}>
            <Text style={{fontWeight: 'bold'}}>
              Please turn on mobile data.
            </Text>
            <Text style={{lineHeight: 23}}>
              <Text style={{fontWeight: 'bold', color: BaseColor.alertRed}}>
                Step 1 :
              </Text>{' '}
              Open settings, wifi list, and find your firmware WIFI
              name(WIFI-SSID) i.e{' '}
              <Text style={{fontWeight: 'bold'}}>"QeridooCAMXXXX"</Text>
            </Text>
            <Text>Please join this network</Text>
            <Text>
              and click on <FIcon name="info" size={15} color={'#03a9f4'} />{' '}
              icon
            </Text>
            <Image
              source={require('../../assets/images/step1F.jpeg')}
              style={{
                height: dHeight * 0.45,
                width: dWidth * 0.9,
              }}
              resizeMode="contain"
            />
            <Text style={{lineHeight: 23}}>
              <Text style={{fontWeight: 'bold', color: BaseColor.alertRed}}>
                Step 2 :{' '}
              </Text>
              Find "Configure IP" and set it to{' '}
              <Text style={{fontWeight: 'bold'}}>"Manual"</Text>
            </Text>

            <Image
              source={require('../../assets/images/step2F.png')}
              style={{
                height: dHeight * 0.65,
                width: dWidth * 0.9,
                marginTop: 20,
              }}
              resizeMode="contain"
            />
            <Text style={{lineHeight: 23}}>- To set it Manual</Text>
            <Text style={{lineHeight: 23}}>
              - Keep "IP address" same as before
            </Text>
            <Text style={{lineHeight: 23}}>
              - Keep "Subnet Mask" same as before
            </Text>
            <Text>
              - Must leave <Text style={{fontWeight: 'bold'}}>"Router"</Text>{' '}
              field blank and press{' '}
              <Text style={{color: '#03a9f4', fontSize: 16, fontWeight: '700'}}>
                Save
              </Text>
            </Text>
            <Image
              source={require('../../assets/images/manualSaveF.png')}
              style={{
                height: dHeight * 0.45,
                width: dWidth * 0.9,
                marginTop: 20,
              }}
              resizeMode="contain"
            />
            <TouchableOpacity
              style={{
                backgroundColor: '#22AFA1',
                height: 50,
                flex: 1,
                borderRadius: 15,
                alignItems: 'center',
                justifyContent: 'center',
                paddingHorizontal: 24,
                marginVertical: 20,
              }}
              onPress={() => {
                if (Platform.OS === 'ios') {
                  Linking.openURL('App-Prefs:root=WIFI');
                } else {
                  AndroidOpenSettings.wifiSettings();
                }
              }}>
              <Text
                style={{
                  fontSize: 18,
                  color: BaseColor.whiteColor,
                  fontFamily: FontFamily.regular,
                  letterSpacing: 0.85,
                }}>
                Open wifi Settings
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </View>
  );
}
