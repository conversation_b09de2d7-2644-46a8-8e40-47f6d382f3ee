/* eslint-disable quotes */
/* eslint-disable no-unused-expressions */
import React, {useEffect, useState} from 'react';
import {View, Text, BackH<PERSON>ler, ScrollView} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Toast from 'react-native-simple-toast';
import {isObject} from 'lodash';
import {useTheme} from '@react-navigation/native';
import styles from './styles';
import CButton from '../../components/CButton';
import CInput from '../../components/CInput';
import {translate} from '../../lang/Translate';
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from '../../utils/commonFunction';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import AuthAction from '../../redux/reducers/auth/actions';
import {FontFamily} from '../../config/typography';
import CustomHeader from '../../components/CustomHeader/CustomHeader';

/**
 *
 *@module ForgotPassword
 */
function ForgotPassword({navigation}) {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const {brandToken} = useSelector(state => state.auth);

  const [isNumber, setIsNumber] = useState('');
  const [IsNumberError, setIsNumberError] = useState(false);
  const [IsNumberErrorTxt, setIsNumberErrorTxt] = useState('');

  const [loader, setloader] = useState(false);
  const [btnDisable, setBtnDisable] = useState(true);

  const dispatch = useDispatch();
  const {setUserId} = AuthAction;

  const validation = () => {
    enableAnimateInEaseOut();

    const emailVal =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    if (isNumber == '') {
      allErrorFalse();
      setIsNumberError(true);
      setIsNumberErrorTxt('Please enter Email Address');
    } else if (!emailVal.test(String(isNumber))) {
      allErrorFalse();
      setIsNumberError(true);
      setIsNumberErrorTxt('Please enter valid Email');
    } else {
      allErrorFalse();
      forgotPassword();
    }
  };

  const allErrorFalse = () => {
    setIsNumberError(false);
  };

  /** this function for forgot Password
   * @function forgotPassword
   * @param {object} data email, token
   */
  const forgotPassword = () => {
    setloader(true);
    const data = {
      email: isNumber,
      token: brandToken,
      brand_name: 'Qeridoo',
      app_name: 'qeridoo',
    };

    getApiData(BaseSetting.endpoints.forgotPassword, 'POST', data)
      .then(response => {
        const uId =
          response && isObject(response.data) && response.data.user_id
            ? response.data.user_id
            : null;
        dispatch(setUserId(uId));

        if (response.success) {
          setTimeout(() => {
            setloader(false);
          }, 2000);
          setTimeout(() => {
            navigation.navigate('Otp', {type: 'ForgotPassword'});
          }, 3000);
        } else {
          Toast.show(response.message);
          setloader(false);
        }
      })
      .catch(err => {
        Toast.show(
          'Something went wrong while sending password recovery request',
        );
        sendErrorReport(err, 'forgot_pass_api');
        setloader(false);
      });
  };

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={styles.root}>
      <View style={{marginTop: '5%'}}>
        <CustomHeader
          transparentView
          leftIconName="left-arrow"
          borderCircular
          backgroundColor={'#F1F5F9'}
          onLeftPress={() => {
            navigation.goBack();
          }}
          backBtn
        />
      </View>

      <ScrollView
        contentContainerStyle={{flexGrow: 1, padding: 16}}
        bounces={false}
        showsVerticalScrollIndicator={false}>
        <View style={styles.loginTextView}>
          <View>
            <Text style={[styles.loginText]}>{translate('forgotScreen')}</Text>
          </View>
          <Text style={[styles.associatedTest]}>
            {translate('forgotEmail')}
          </Text>
        </View>
        <View style={[styles.inputWrapper, {marginTop: '10%'}]}>
          <Text
            style={{
              color: BaseColor.blackColor,
              fontFamily: FontFamily.regular,
              fontSize: 14,
            }}>
            {translate('emailId')}
          </Text>
          <CInput
            placeholder={translate('enterYourEmail')}
            value={isNumber}
            onChangeText={val => {
              setIsNumber(val);
              if (val == '') {
                setBtnDisable(true);
              } else {
                setBtnDisable(false);
              }
            }}
            keyboardType="email-address"
            placeholderTextColor={BaseColor.textGrey}
            showError={IsNumberError}
            errorMsg={IsNumberErrorTxt}
            textInputWrapper={{}}
            onSubmitEditing={() => {
              validation();
            }}
          />
        </View>
        <View style={{marginTop: '15%'}}>
          <CButton
            style={styles.loginBtn}
            title={translate('forgotBtn')}
            onPress={() => {
              validation();
            }}
            disable={btnDisable}
            loader={loader}
          />
        </View>
      </ScrollView>
    </View>
  );
}

export default ForgotPassword;
