/* eslint-disable quotes */
import {StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
  },
  mainContainer: {
    flex: 1,
    padding: 16,
    // justifyContent: "center",
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: 'flex-end',
  },
  loginBtn: {
    width: '80%',
  },
  mainInputStyle: {
    // flex: 1,
    justifyContent: 'center',
  },
  inputWrapper: {
    marginVertical: 7,
  },
  loginText: {
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    fontSize: 30,
    // lineHeight: 26,
    textAlign: 'center',
  },
  loginTextView: {
    marginBottom: 20,
    alignItems: 'center',
    fontFamily: FontFamily.default,
  },
  associatedTest: {
    fontFamily: FontFamily.regular,
    color: BaseColor.primary,
    fontSize: 14,
    textAlign: 'center',
    paddingTop: 18,
    lineHeight: 22,
  },
  lockIconStyle: {
    backgroundColor: BaseColor.onBoardBgColor,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    height: 80,
    // borderRadius: 40,
    // elevation: 5,
    marginBottom: 20,
  },
  headerStyle: {position: 'absolute', zIndex: 10},
});

export default styles;
