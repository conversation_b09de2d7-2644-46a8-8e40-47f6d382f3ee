import React, {Fragment, useCallback, useEffect, useState} from 'react';
import {
  AppState,
  BackHandler,
  Dimensions,
  FlatList,
  Image,
  Modal,
  Text,
  TouchableOpacity,
  View,
  Platform,
  ActivityIndicator,
  NativeModules,
  NativeEventEmitter,
  PermissionsAndroid,
  Alert,
  DeviceEventEmitter,
} from 'react-native';
import Slider from '@react-native-community/slider';
import Iconin from 'react-native-vector-icons/Ionicons';
import AIcon from 'react-native-vector-icons/AntDesign';
import RNFetchBlob from 'react-native-blob-util';
import Toast from 'react-native-simple-toast';
import {useFocusEffect, useIsFocused, useTheme} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import moment from 'moment';
import {findIndex, flattenDeep, isArray, isEmpty, isNull} from 'lodash';
import {
  check,
  PERMISSIONS,
  request,
  checkMultiple,
} from 'react-native-permissions';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import BleManager from 'react-native-ble-manager';
import _BackgroundTimer from 'react-native-background-timer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ScrollView} from 'react-native-gesture-handler';
import WifiManager from 'react-native-wifi-reborn';
import NetInfo from '@react-native-community/netinfo';
import Orientation from 'react-native-orientation-locker';
import {useRef} from 'react';
import {CustomIcon} from '../../config/LoadIcons';
import {FontFamily, FontWeight} from '../../config/typography';
import styles from './styles';
import CAlert from '../../components/CAlert';
import {translate} from '../../lang/Translate';
import CButton from '../../components/CButton';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {
  addAction,
  openInAppBrowser,
  savePageViewData,
  sendErrorReport,
} from '../../utils/commonFunction';
import InAppModal from '../../components/InAppModal';
import bluetoothActions from '../../redux/reducers/bluetooth/actions';
import AuthAction from '../../redux/reducers/auth/actions';
import TripAction from '../../redux/reducers/trip/actions';
import preferredLocationData from '../../assets/preferredLocation/preferredLocation';
import {SuggestionFlatList} from '../../components/SuggestionCard';
import GetLocation from 'react-native-get-location';
import environment from '../../config/environment';
import fetchWeatherData from '../../utils/weatherFunction';
import ProgressBar from '../../components/ProgressBar/ProgressBar';
import {BluetoothStatus} from 'react-native-bluetooth-status';
import useBluetoothConstantlyListenState from '../../customHook/useBluetoothConstantlyListenState';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import StepComponent from '../../components/StepComponent';
import MemoriesMoment from '../../components/MemoriesMoment';
import MaintenanceInfo from '../../components/MaintenanceInfo';
import SwitchComponent from '../../components/Switch';
import HighLights from '../../components/HighLights';
import CInput from '../../components/CInput';
import ModalAuth from '../../redux/reducers/modal/actions';
import PlaceAction from '../../redux/reducers/place/actions';
import Advertiesment from '../../components/Advertisement/indes';
import TriphistoryComponent from '../../components/TripHistoryComponent';
import FeatureFavourite from '../../components/FeatureFavourite';
import {Images} from '../../config/Images';
import Challenges from '../../components/Challenges';

let backPressed = 0;

/**
 *
 *@module Home
 *
 */

const BleManagerModule = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);

const IOS = Platform.OS === 'ios';
const {width, height} = Dimensions.get('window');
const Home = ({navigation}) => {
  const iPhone14 = getStatusBarHeight();
  const scrollViewRef = useRef();
  const isFocused = useIsFocused();
  const {NutSdkModule} = NativeModules;
  const dispatch = useDispatch();
  const {fs} = RNFetchBlob;
  const {
    setConnectedDeviceDetail,
    setConnectedDeviceDetails,
    setLeftChildAlert,
  } = bluetoothActions;
  const {setTripPlaceName} = PlaceAction;
  const {setUserLocalAddress, setUserLatLong} = AuthAction;
  const {setTripStartDate, setTripId} = TripAction;
  const {setFirstTimeLogin, setTrailerConnectedModal, setConnectedDeviceList} =
    ModalAuth;
  const {
    firstTimeLogin,
    sliderProgress,
    trailerConnectedModal,
    connectedDeviceList,
  } = useSelector(sta => sta.modal);
  const {userData, userLatLong, userLocalAddress} = useSelector(
    state => state.auth,
  );
  const {
    isBleConnected,
    lastDeviceId,
    emergencyAlert,
    isLeftChildAlert,
    isConnecting,
    deviceDetail,
    qrWifiSSID,
    aviFiles,
  } = useSelector(state => state.bluetooth);
  const [videoPlayModal, setvideoPlayModal] = useState(false);
  const [alertModal, setAlertModal] = useState(false);
  const token = useSelector(state => state.auth.accessToken);
  const [inAppModal, setInAppModal] = useState(false);
  const [inAppMsgData, setinAppMsgData] = useState({});
  const [deviceList, setDeviceList] = useState([]);
  const [list, setList] = useState([]);
  const [isScanning, setIsScanning] = useState(false);
  const [imgState, setImgState] = useState('');
  const colors = useTheme();
  const BaseColor = colors.colors;
  const [state, setSWState] = useState({
    key: '',
    value: null,
    isDisCon: true,
    isAutoConnect: '',
  });
  const [loader, setLoader] = useState(false);
  const [syncLoader, setSyncLoader] = useState(false);
  const [searchModal, setSearchModal] = useState(false);
  const [searchError, setSearchError] = useState({error: false, message: ''});
  const [tripLoader, setTripLoader] = useState(false);
  const [otherPreferenceData, setOtherPreferenceData] = useState([]);
  const [nearestLocation, setNearestLocation] = useState([]);
  const [previewSliderValue, setPreviewSliderValue] = useState(10);
  const [selectedFilter, setSelectedFilter] = useState({
    title: '',
    tag: [],
    location: '',
    distance: previewSliderValue,
  });

  const handleConnectivity = state => {
    // console.log("handleConnectivity -> state", state);
    sendErrorReport(state, 'network_state');
    if (state?.type === 'wifi') {
      // Alert.alert("Please turn on mobile data");
    }
    if (state.isConnected) {
      WifiManager.getCurrentWifiSSID().then(
        async ssid => {
          // console.log(`Your current connected wifi SSID is ${ssid}`);
          if (ssid === qrWifiSSID) {
            // qrWifiSSID
            setTimeout(() => {
              dispatch(bluetoothActions.setIsBleConnected(true));
            }, 5000);
          } else {
            dispatch(bluetoothActions.setIsBleConnected(false));
          }
          // if (Platform.OS === "android") {
          //   await WifiManager.forceWifiUsageWithOptions(true, {
          //     noInternet: true,
          //   });
          // }
        },
        () => {
          console.log('Cannot get current SSID!');
        },
      );
    } else {
      dispatch(bluetoothActions.setIsBleConnected(false));
    }

    if (state.isInternetReachable) {
      // syncData();
    }
  };

  const getSliderDetail = async () => {
    try {
      const url = BaseSetting.endpoints.getConnectedDetails;
      const response = await getApiData(url, 'GET');
      if (response.success) {
        const device = response?.data;
        dispatch(setConnectedDeviceList(device));
        setTimeout(() => {
          if (isEmpty(device) && firstTimeLogin === null) {
            dispatch(setFirstTimeLogin(true));
          }
          if (!isEmpty(device) && device?.trip_detail == 0) {
            if (
              (device?.trailer_detail >= 1 ||
                device?.camera_detail >= 1 ||
                device?.tracker_detail >= 1) &&
              trailerConnectedModal === null
            ) {
              dispatch(setTrailerConnectedModal(true));
            }
          }
        }, 3000);
      }
    } catch (err) {
      console.log('ERRR==', err);
    }
  };

  // Device List API Integration
  const getOtherPreferenceList = async () => {
    try {
      const url = BaseSetting.endpoints.otherPreference;
      const response = await getApiData(url, 'GET');
      if (response?.success) {
        setOtherPreferenceData(response?.data);
      }
    } catch (err) {
      console.log('ERRR==', err);
    }
  };
  // End

  useEffect(() => {
    getSliderDetail();
    getOtherPreferenceList();
  }, [isFocused]);
  useEffect(() => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({x: 0, y: 0, animated: true});
    }
  }, [trailerConnectedModal, searchModal]);

  useEffect(() => {
    Orientation.lockToPortrait();
    NetInfo.addEventListener(state => {
      handleConnectivity(state);
    });
    // NetworkInfo.getGatewayIPAddress().then((ipAddress) => {
    // sendErrorReport(ipAddress, "getGatewayIPAddress");
    // });
  }, []);
  useEffect(() => {
    if (Platform.OS === 'android' && Platform.Version >= 31) {
      check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
        .then(res => {
          // console.log("BLUETOOTH_CONNECT--q-", res);
          if (res === 'granted') {
            // setBPermission(true);
          }
        })
        .catch(e => {
          // console.log("bluetooth_", e);
        });
      request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
        .then(result => {
          // console.log("BLUETOOTH_CONNECT----1", result);
        })
        .then(statuses => {
          // console.log(
          //   "BLUETOOTH_CONNECT--2",
          //   statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT]
          // );
        });
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      Orientation.lockToPortrait();
      if (token !== '') {
        // getPost("feed_post");
        // getFeedPost();
        getDeviceList();
        // getBadgeCount();
        // getChildInfo();
        handleStopScan();
      }
    }, []),
  );
  useEffect(() => {
    getDeviceList();
  }, [isBleConnected, isConnecting, deviceDetail]);

  useEffect(() => {
    if (Platform.OS === 'ios') {
      PushNotificationIOS.setApplicationIconBadgeNumber(0);
    }
  }, []);

  // console.log("device---", deviceDetail, connectedDeviceDetail);
  const downloadVideo = async obj => {
    // setLoader(true);
    sendErrorReport(obj, 'downloadOBj');
    try {
      const {DownloadDir, DocumentDir} = fs.dirs;
      const downloadsPath = Platform.OS === 'ios' ? DocumentDir : DownloadDir;
      const videoFilename = obj.name; // Replace with the actual filename of your video
      const videoPath = `${downloadsPath}/${videoFilename}`;
      dispatch(bluetoothActions.setPath(downloadsPath));
      const videoExists = await fs.exists(videoPath);
      sendErrorReport(downloadsPath, 'downloadsPath');
      if (videoExists) {
      } else {
        const saveFilePath = Platform.OS === 'ios' ? DocumentDir : DownloadDir;
        await RNFetchBlob.config({
          fileCache: true,
          addAndroidDownloads: {
            useDownloadManager: true,
            notification: true,
            path: `${saveFilePath}/${obj.name}`,
            description: 'Downloading.',
          },
          path: `${saveFilePath}/${obj.name}`,
        })
          .fetch('GET', `http://***********:8080/c?dwn=//${obj.name}`, {})
          .then(res => {
            sendErrorReport(res, 'downloads---res');
            const arr = [...aviFiles] || [];
            const index = findIndex(arr, i => i?.name === obj?.name);
            if (index > -1) {
              arr[index].status = true;
              dispatch(bluetoothActions.setAVIFiles(arr));
            }

            // Alert.alert(JSON.stringify(res.path()));
            // sendErrorReport(res.path(), "res.path()downloadErr");
            // the temp file path
            // setLoader(false);
          })
          .catch(err => {
            Toast.show('Error --1');
            setLoader(false);
          });
      }
    } catch (error) {
      Alert.alert(JSON.stringify(error));
      sendErrorReport(error, 'downloadErr');
      console.log('🚀 ~ file: index.js ~ line 145 ~ MyQRcode ~ error', error);
      Toast.show('Error --2');
      setLoader(false);
    }
  };

  const apiVideo = async () => {
    try {
      await fetch('http://***********:8080/getfiles')
        .then(response => response.text())
        .then(htmlData => {
          // Process the HTML data here
          const regex = /(<([^>]+)>)/gi;
          const result = htmlData?.replace(regex, '');
          Alert.alert(JSON.stringify(result));
          sendErrorReport(JSON.stringify(htmlData), 'htmlData');
          const aviFiles = result // mansi please change ===========================================
            .match(/\/([\w\d]+\.avi)/gi)
            .map(file => file.substring(1).toLowerCase()); // convert to lowercase and remove leading slash
          // const aviFiles = result
          //   .match(/"\w+\.avi"/g)
          //   .map((file) => file.slice(1, -1));
          // Alert.alert(JSON.stringify(aviFiles));
          const uniqueArray = aviFiles.filter(
            (value, index, self) => self.indexOf(value) === index,
          );
          // Alert.alert(JSON.stringify(uniqueArray));
          const newArr = uniqueArray.slice(1);
          // Alert.alert(JSON.stringify(newArr));
          const fileObjects = newArr.map(file => ({
            name: file,
            status: false,
          }));
          // Alert.alert(JSON.stringify(uniqueArray));
          dispatch(bluetoothActions.setAVIFiles(fileObjects));
          // Alert.alert(JSON.stringify(aviFiles));
          fileObjects.map(obj => {
            downloadVideo(obj);
          });
          // setLoader(false);
          // Alert.alert(JSON.stringify(aviFiles));
          // downloadFilesSequentially(aviFiles);
        })
        .catch(error => {
          // Handle any errors that occurred during the fetch
          sendErrorReport(JSON.stringify(error), 'htmlData---error');
          Alert.alert('error getting list');
        });
    } catch (err) {
      console.log('err ===========2==== ', err);
    }
  };

  // this function for get device list
  /** this function for get device list
   * @function getDeviceList
   * @param {object} data platform
   */
  async function getDeviceList() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    const newObj = {
      type: 'add',
    };

    const data = {
      platform: Platform.OS,
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.connectedDevice,
        'POST',
        data,
        headers,
      );
      if (response.success && isArray(response.data)) {
        dispatch(setConnectedDeviceDetails(flattenDeep(response.data)));
        dispatch(setConnectedDeviceDetail(flattenDeep(response.data)));
        const arr = response.data;
        arr.unshift(newObj);
        setDeviceList(arr);
        console.log('cdevice list---step1', arr);
      } else {
        dispatch(setConnectedDeviceDetails([]));
        dispatch(setConnectedDeviceDetail([]));
        setDeviceList([newObj]);
        Toast.show(response.message);
      }
    } catch (error) {
      setDeviceList([newObj]);
      if (isBleConnected) {
        const obj = {device_i: ''};
        setDeviceList([{type: 'add'}, obj]);
      }
      dispatch(setConnectedDeviceDetails([]));
      dispatch(setConnectedDeviceDetail([]));
      console.log('error for device list ===', error);
      sendErrorReport(error, 'get_device_list');
    }
  }
  // Test---------------------------------------------------->->

  let peripherals = new Map();

  const onRefresh = () => {
    BleManager.stopScan().then(() => {
      // Success code
      console.log('Scan stopped');
      peripherals = new Map();
    });
    setTimeout(() => {
      // startScan();
    }, 1000);
  };

  useEffect(() => {
    if (emergencyAlert || isLeftChildAlert) {
      // setTimeout(() => {
      getDeviceList();
      dispatch(setLeftChildAlert(false));
      onRefresh();
      // }, 100);
    }
  }, [emergencyAlert, isLeftChildAlert]);

  const handleStopScan = (item = {}) => {
    setIsScanning(false);
    if (list && !isEmpty(list) && item && !isEmpty(item)) {
      const ind = findIndex(list, i => i.id === item?.id);
      if (ind > -1) {
        list.splice(ind, 1);
        setList(list);
      }
    }
  };

  const handleDiscoverPeripheral = peripheral => {
    if (peripheral && !isBleConnected && !emergencyAlert) {
      if (!peripheral.name) {
        peripheral.name = 'NO NAME';
      }
      peripherals.set(peripheral.id, peripheral);
      setList(Array.from(peripherals.values()));
    }
  };

  useEffect(() => {
    if (!isBleConnected && !emergencyAlert && !isLeftChildAlert) {
      bleManagerEmitter.addListener(
        'BleManagerCentralManagerWillRestoreState',
        data => {
          console.log(
            'BLE ==> BleManagerCentralManagerWillRestoreState ===> ',
            data,
          );
        },
      );

      bleManagerEmitter.addListener(
        'BleManagerDiscoverPeripheral',
        handleDiscoverPeripheral,
      );
      bleManagerEmitter.addListener('BleManagerStopScan', handleStopScan);
      // bleManagerEmitter.addListener(
      //   "BleManagerDisconnectPeripheral",
      //   handleDisconnectedPeripheral
      // );

      if (Platform.OS === 'android' && Platform.Version >= 23) {
        console.log('called---5');
        PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ).then(result => {
          if (result) {
            console.log('Permission is OK');
          } else {
            PermissionsAndroid.request(
              PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            ).then(res => {
              if (res) {
                console.log('User accept');
              } else {
                console.log('User refuse');
              }
            });
          }
        });
      }
    }
    return () => {
      console.log('unmount');
    };
  }, [isBleConnected, emergencyAlert, isLeftChildAlert]);

  //! This effect to autoconnect the device when back in range.
  useEffect(() => {
    // this will be executed once after 5 seconds
    console.log('toe');
    if (!isBleConnected && !emergencyAlert && !isLeftChildAlert) {
      _BackgroundTimer.runBackgroundTimer(async () => {
        const isConnect = await AsyncStorage.getItem('isConnect');
        if (
          isConnect !== 'True' &&
          !isBleConnected &&
          !emergencyAlert &&
          !isLeftChildAlert &&
          lastDeviceId &&
          isEmpty(state.isAutoConnect) &&
          token
        ) {
          // setTimeout(() => {
          // onReadQR();
          // }, 1500);
        }
      }, 1500);
    }
  }, [isBleConnected, emergencyAlert, isLeftChildAlert]);

  useEffect(() => {
    if (isBleConnected) {
      apiVideo();
    }
    WifiManager.getCurrentWifiSSID().then(
      ssid => {
        console.log(`Your current connected wifi SSID is ${ssid}`);
        if (ssid === qrWifiSSID) {
          setTimeout(() => {
            setImgState('right');

            dispatch(bluetoothActions.setIsBleConnected(true));
            // Toast.show("connected to ", ssid);
            // openInAppBrowser("http://***********");
          }, 1000);
        }
      },
      () => {
        // Toast.show('Cannot get current SSID!');
        console.log('Cannot get current SSID!');
      },
    );
  }, [isBleConnected]);

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }
  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  //weather condition
  const [weatherText, setWeatherText] = useState('');

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await fetchWeatherData(
          userData.latitude || userLatLong.latitude,
          userData.longitude || userLatLong.longitude,
        );
        setWeatherText(result);
      } catch (error) {
        console.error('Error fetching weather data: ', error);
      }
    };

    fetchData();
  }, [userLatLong]);

  //Show user local address
  const getLocation = () => {
    const myApiKey = environment.mapKey;
    GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 10000,
    })
      .then(location => {
        fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?address=${location.latitude},${location.longitude}&key=${myApiKey}`,
        )
          .then(response => response.json())
          .then(responseJson => {
            let localAddress1 = '';
            let localAddress2 = '';
            let localAddress3 = '';
            const {address_components} = responseJson.results[1];
            address_components.map(item => {
              if (item.types[0] === 'route') {
                localAddress1 = item.long_name;
              } else if (item.types[0] === 'locality') {
                localAddress3 = item.long_name;
              } else if (item.types[0] === 'premise') {
                localAddress2 = item.long_name;
              }
            });
            console.log('location , ....>>>>>', location.latitude);

            console.log(
              'userData.longitude',
              userData.longitude,
              'userData.latitude',
              userData.latitude,
            );
            dispatch(
              setUserLatLong({
                longitude: location.longitude,
                latitude: location.latitude,
              }),
            );

            dispatch(
              setUserLocalAddress({
                localAddress1,
                localAddress2,
                localAddress3,
              }),
            );
          });
      })
      .catch(error => {
        const {code, message} = error;
        sendErrorReport(error, 'get_location');
      });
  };

  useEffect(() => {
    getLocation();
  }, []);

  const tabArr = [
    {type: 'repeat', name: translate('Repeat')},
    {type: 'search', name: translate('search')},
    {type: 'createTrip', name: translate('createTrip')},
  ];

  //slider value
  const [sliderValue, setSliderValue] = useState(10);
  const [radius, setRadius] = useState(previewSliderValue * 1609.34);

  const milesToMeters = miles => {
    setRadius(miles * 1609.34);
  };

  const [suggestPlaces, setSuggestPlaces] = useState([]);

  //greeting
  function generateGreetings() {
    var currentHour = moment().format('HH');
    if (currentHour >= 5 && currentHour < 12) {
      return translate('goodmorning');
    } else if (currentHour >= 12 && currentHour < 18) {
      // return "Good Afternoon";
      return translate('goodafternoon');
    } else if (currentHour >= 18 && currentHour < 24) {
      // return "Good Evening";
      return translate('goodevening');
    } else {
      return 'Hello';
    }
  }

  // tab selection state
  const [selectedTab, setSelectedTab] = useState('repeat');

  const handleTabPress = tab => {
    setSelectedTab(tab);
  };

  // search bar
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [showPrediction, setShowPrediction] = useState(false);

  const [search, setSearch] = useState('');
  const [places, setPlaces] = useState([]);

  // Switch For Suggestion....
  const toggleSwitch = () => {
    if (connectedDeviceList?.trailer_detail >= 1) {
      setShowPrediction(previousState => !previousState);
    } else {
      Alert.alert('Please add Trailer first.');
    }
  };

  const [showSearchAlert, setShowSearchAlert] = useState({
    error: false,
    message: '',
  });

  const handleSearch = input => {
    setSearch(input);
  };

  useEffect(() => {
    if (search.length < 1) {
      setShowSearchAlert({error: false, message: ''});
      setPlaces([]);
    } else if (search.length >= 1 && search.length < 4) {
      setShowSearchAlert({
        error: true,
        message: 'Please enter more than 3 characters',
      });
    } else if (search.length >= 4) {
      setShowSearchAlert({error: false, message: ''});
    }
  }, [search]);

  // preferred location

  const [selectedPreferredLocationId, setSelectedPreferredLocationId] =
    useState('city');

  const handleSelection = location => {
    setSelectedPreferredLocationId(location);
    setSelectedFilter({...selectedFilter, location: location});
  };

  const renderPreferredLocationItem = ({item, index}) => {
    const isSelected = item.key === selectedPreferredLocationId;
    const color = isSelected ? item.selectedColor : item.defaultColor;
    return (
      <View style={[styles.preferredCardContainer]}>
        <TouchableOpacity
          style={[
            styles.preferredCard,
            item.key === selectedPreferredLocationId && {
              borderColor: BaseColor.primary,
            },
          ]}
          onPress={() => handleSelection(item.key)}>
          <CustomIcon
            name={item.icon}
            size={25}
            color={
              item.key === selectedPreferredLocationId
                ? BaseColor.primary
                : BaseColor.textGrey
            }
          />
        </TouchableOpacity>
        <Text style={[styles.preferredLocationName, {color: color}]}>
          {translate(item.text)}
        </Text>
      </View>
    );
  };

  //filter other prefrences
  const Item = ({id, title, isSelected, selectItem, deselectItem}) => (
    <View style={isSelected ? styles.selectedButton : styles.button}>
      {isSelected && (
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            padding: 4,
          }}
          onPress={() => {
            deselectItem(id);
            // setSelectedFilter({...selectedFilter, tag: id});
          }}>
          <Text
            style={{
              color: BaseColor.whiteColor,
              fontSize: 14,
              fontFamily: FontFamily.regular,
            }}>
            {title}
          </Text>
          <Iconin
            style={styles.cross}
            name="close-outline"
            size={19}
            color="#FFFFFF"
          />
        </TouchableOpacity>
      )}
      {!isSelected && (
        <TouchableOpacity
          style={{flexDirection: 'row'}}
          onPress={() => {
            selectItem(id);
            // setSelectedFilter({...selectedFilter, tag: id});
          }}>
          <Text
            style={{
              color: BaseColor.whiteColor,
              fontSize: 14,
              fontFamily: FontFamily.regular,
              padding: 5,
            }}>
            {title}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
  const [selectedItems, setSelectedItems] = useState([]);

  const selectItem = id => {
    setSelectedItems([...selectedItems, id]);
    setSelectedFilter({...selectedFilter, tag: [...selectedItems, id]});
  };

  const deselectItem = id => {
    setSelectedItems(selectedItems.filter(itemId => itemId !== id));
    setSelectedFilter({
      ...selectedFilter,
      tag: selectedItems.filter(itemId => itemId !== id),
    });
  };

  const renderOPFilterItem = ({item}) => {
    return (
      <View id={item} style={{paddingRight: 10}}>
        <Item
          id={item}
          title={item}
          isSelected={selectedItems.includes(item)}
          selectItem={selectItem}
          deselectItem={deselectItem}
        />
      </View>
    );
  };

  // Device List API Integration
  const getGpxLocation = async obj => {
    try {
      const url =
        BaseSetting.endpoints.gpxLocation +
        `?title=${obj?.title}&location_type=${obj?.location}&distance=${obj?.distance}&tag=${obj?.tag}&latitude=${userLatLong?.latitude}&longitude=${userLatLong?.longitude}`;
      const response = await getApiData(url, 'GET');
      if (response?.success) {
        setNearestLocation(response?.data);
      }
    } catch (err) {
      console.log('ERRR==', err);
    }
  };
  // End

  useEffect(() => {
    getGpxLocation(selectedFilter);
  }, [selectedFilter]);

  // Save Trip Name API Integration
  const createNearestTrip = async (tripName, gpxId) => {
    if (
      connectedDeviceList?.trailer_detail < 1 ||
      isEmpty(connectedDeviceList)
    ) {
      Alert.alert('Please add Trailer First.');
      return;
    }
    try {
      const data = {
        trip_name: tripName,
        gpx_id: gpxId,
        trip_type: 'custom_trip',
      };
      const response = await getApiData(
        BaseSetting.endpoints.saveTripName,
        'POST',
        data,
        '',
        true,
      );
      if (response?.success) {
        dispatch(setTripPlaceName(tripName));
        dispatch(setTripId(response?.data?.id));
        navigation.navigate('SearchTrip', {
          tripName: tripName,
          gpxId: gpxId,
        });
      }
    } catch (error) {
      console.log('error for device list ===', error);
      sendErrorReport(error, 'saving_trip_name');
    }
  };
  // End Save Trip Name API Integration

  const renderNearestPlaceItem = ({item}) => {
    return (
      <TouchableOpacity
        key={item.id}
        activeOpacity={0.8}
        onPress={() => createNearestTrip(item?.title, item?.id)}>
        <View style={[styles.nPCard]}>
          <View style={[styles.nPCardImage]}>
            <Image
              source={
                item?.gpx_image_url
                  ? {uri: item?.gpx_image_url}
                  : Images.mapImag
              }
              style={{
                width: '100%',
                height: '100%',
                borderTopLeftRadius: 14,
                borderBottomLeftRadius: 14,
              }}
            />
          </View>
          <View style={[styles.nPCardInfo]}>
            <Text numberOfLines={1} style={[styles.nPCardInfoTitle]}>
              {item.title}
            </Text>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 10,
              }}>
              <Text numberOfLines={1} style={[styles.nPCardInfoAddress]}>
                {item?.description}
              </Text>
            </View>
            <View
              style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <Text numberOfLines={1} style={[styles.tripText]}>
                {!isNull(item?.distance) ? `${item?.distance} |` : null}
                {!isNull(item?.elevation_gain) ? item?.elevation_gain : null}
              </Text>
            </View>
          </View>
          {item?.tag_value ? (
            <View
              style={{
                height: 30,
                backgroundColor: '#AED4CC',
                borderBottomLeftRadius: 12,
                borderTopRightRadius: 12, // Apply border radius to the parent View
              }}>
              <Text
                style={{
                  padding: 8,
                  paddingHorizontal: 10,
                  color: '#FFFFFF',
                  fontSize: 11,
                }}>
                # {item?.tag_value}
              </Text>
              <Text
                style={{
                  fontSize: 10,
                  textAlign: 'right',
                  marginTop: 18,
                  marginRight: 15,
                  color: '#B8C9C9',
                }}>
                {/* Yesterday */}
              </Text>
            </View>
          ) : null}
        </View>
      </TouchableOpacity>
    );
  };

  // -------------------JAVA NATIVE MODULE WORK IN PROGRESS------------------------

  const [isConnected, setIsConnected] = useState(false);
  const [scannedDeviceList, setScannedDeviceList] = useState([]);
  const [isConnectingLoading, setIsConnectingLoading] = useState(false);
  const [intialBluetoothState, setIntialBluetoothState] = useState(false);
  const [currentBluetoothStatus, setCurrentBluetoothStatus] = useState(false);
  const [appState, setAppState] = useState(true);

  const {
    pairedSmartTagDevice,
    isSmartTagDeviceConnected,
    scannedSmartTagDevice,
  } = useSelector(state => state.bluetooth);

  useBluetoothConstantlyListenState(isEnabled => {
    console.log('Bluetooth state changed:', isEnabled);
    setCurrentBluetoothStatus(isEnabled);
    // Take appropriate action based on Bluetooth state
  });

  const requestBluetoothPermissions = async () => {
    try {
      const result = await checkMultiple([PERMISSIONS.ANDROID.BLUETOOTH_SCAN]);

      if (result !== 'granted') {
        const requestResult = await request(PERMISSIONS.ANDROID.BLUETOOTH_SCAN);

        if (requestResult === 'granted') {
          console.log('Bluetooth permission granted.');
          // Do something after permission is granted
        } else {
          console.log('Bluetooth permission denied.');
          // Handle the case when permission is denied
        }
      } else {
        console.log('Bluetooth permission already granted.');
        // Do something if permission is already granted
      }
    } catch (error) {
      console.error(
        'Error checking or requesting Bluetooth permission:',
        error,
      );
      // Handle error
    }
  };

  async function getBluetoothState() {
    const isEnabled = await BluetoothStatus.state();
    console.log('🚀 ~ getBluetoothState ~ isEnabled:', isEnabled);
    if (!isEnabled) {
      Alert.alert(
        'Enable Bluetooth',
        'Please enable bluetooth in order to use Tracking Feature.',
      );
    }
  }

  //intitialization & clean up effecct of stop scanning if component unmount
  useEffect(() => {
    // console.log('🚀 ~ Home ~ Initializing Ble Manager---->>>>>>');
    getBluetoothState();
    const initializeBleManager = async () => {
      // Request Bluetooth permissions if running on Android
      if (Platform.OS === 'android') {
        await requestBluetoothPermissions();
        // Initialize the BleDeviceManager
        await NutSdkModule.initializeBleManager();
        // Binding
        await NutSdkModule.bindBleService();
      }

      if (Platform.OS === 'ios') {
        console.log('IOS BLE DEVICE SCAN');
        if (
          (!isEmpty(pairedSmartTagDevice) && pairedSmartTagDevice) ||
          isEmpty(scannedSmartTagDevice)
        ) {
          dispatch(bluetoothActions.setSmartTagDeviceConnectionLoading(true));
          NutSdkModule.startScanning();
        } else if (!isEmpty(scannedSmartTagDevice)) {
          console.log('scanningStopped');
          NutSdkModule.stopScanning();
        }
      }
    };

    initializeBleManager();

    return () => {
      // Stop scanning when the component unmounts
      // NutSdkModule.stopScanning();
    };
  }, []);

  const checkBluetoothStatus = async () => {
    try {
      const {NutSdkModule} = NativeModules;
      const isEnabled = await NutSdkModule.checkBluetoothStatus();
      if (!isEnabled) {
        setIsConnected(false);
        dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
      }
      setIntialBluetoothState(isEnabled);
    } catch (error) {
      console.error('Error checking Bluetooth status:', error);
    }
  };
  // console.log('Initial BLuetooth state:', intialBluetoothState);

  const getCurrentLocation = async () => {
    console.log('AB HUMARI BARI HAIII---->> CHALNE KII.......');
    const myApiKey = environment.mapKey;
    console.log('myApiKey', myApiKey);
    GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 10000,
    })
      .then(location => {
        console.log('location tada---->>>', location);
        fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?address=${location.latitude},${location.longitude}&key=${myApiKey}`,
        )
          .then(response => response.json())
          .then(responseJson => {
            console.log('responseJson------>>>>>>', responseJson);
            let localAddress1 = '';
            let localAddress2 = '';
            let localAddress3 = '';
            const {address_components} = responseJson.results[1];
            address_components.map(item => {
              if (item.types[0] === 'route') {
                localAddress1 = item.long_name;
              } else if (item.types[0] === 'locality') {
                localAddress3 = item.long_name;
              } else if (item.types[0] === 'premise') {
                localAddress2 = item.long_name;
              }
            });
            console.log('localAddress1', localAddress1);
            console.log('localAddress2', localAddress2);
            console.log('localAddress3', localAddress3);
            dispatch(
              bluetoothActions.setSmartTagDeviceLocation({
                latitude: location?.latitude,
                longitude: location?.longitude,
                localAddress1,
                localAddress2,
                localAddress3,
              }),
            );
          });
      })
      .catch(error => {
        console.warn('error -- location', error);
        if (error?.message !== 'Location cancelled by another request') {
          Toast.show(
            'Please enable your location service to send emergency alert.',
          );
        }
      });
  };

  const connectToDevice = deviceAddress => {
    console.log('Device Address~~~~~>>>>>', deviceAddress);
    setIsConnectingLoading(true);
    dispatch(bluetoothActions.setSmartTagDeviceConnectionLoading(true));
    if (!deviceAddress) {
      Toast.show('Please select device first');
      return;
    }

    if (!isEmpty(pairedSmartTagDevice)) {
      console.log('SO THERE IS PAIRED DEVICE AND WILL CONNECT TO THAT--->>>');
    }
    NutSdkModule.connectToDevice(deviceAddress)
      .then(res => {
        console.log('Connected to device successfully.', res);
        let connectedDeviceInfo = res;
        if (Platform.OS === 'android') {
          connectedDeviceInfo = JSON.parse(res);
        }
        console.log('🚀 ~ .then ~ connectedDeviceInfo:', connectedDeviceInfo);
        //if gets connected then will store in redux
        // dispatch(setSmartTagDevice(connectedDeviceInfo));
        dispatch(
          bluetoothActions.setSmartTagDevice({
            name: connectedDeviceInfo.name,
            customName: 'Qeridoo Smart Tag',
            address: connectedDeviceInfo.address,
            id: connectedDeviceInfo.id,
            product_id: connectedDeviceInfo.productId,
          }),
        );
        setTimeout(() => {
          setIsConnectingLoading(false);
          dispatch(bluetoothActions.setSmartTagDeviceConnectionLoading(false));
        }, 5000);
        getCurrentLocation();
        setIsConnected(true);
        dispatch(bluetoothActions.setSmartTagDeviceConnectionState(true));

        // Perform any additional actions after successful connection
      })
      .catch(error => {
        if (
          error.message === 'No devices scanned. Please start scanning first.'
        ) {
          console.warn('Please start scanning before connecting');
          Toast.show('Please Unpair and statrt scanning first');
        } else {
          console.error('Error connecting to device:', error);
          Toast.show(JSON.stringify(error));
        }
        setIsConnectingLoading(false);
        dispatch(bluetoothActions.setSmartTagDeviceConnectionLoading(false));
      });
  };

  const checkingIsDeviceConnect = async deviceAddress => {
    const isEnabled = await BluetoothStatus.state();
    if (!isEnabled) {
      Toast.show('Please enable bluetooth in order to use Tracking Feature.');
      dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
      setIsConnected(false);
      return;
    }
    //check if paired device exist in redux
    if (!isEmpty(pairedSmartTagDevice)) {
      //if pairedDeviceList is not empty then check if saved device is connected or not
      console.log(
        'NOW CHECKING FUNCTION EXCEUTED----->>>>>',
        pairedSmartTagDevice,
      );

      NutSdkModule.isConnected(deviceAddress)
        .then(isConnected => {
          console.log(` REDUX STORED DEVICE STATUS--->>>>`, isConnected);
          setIsConnected(isConnected);
          dispatch(
            bluetoothActions.setSmartTagDeviceConnectionState(isConnected),
          );

          console.log(
            'IF DEVICE IS NOT CONNECTED THEN WILL CHECK SCANNED RESULT FOR DEVICE---->>',
            isConnected,
            scannedDeviceList,
          );

          if (
            (!isConnected && scannedDeviceList.length > 0) ||
            !isEmpty(scannedSmartTagDevice)
          ) {
            console.log(
              'DEVICE NOT CONECTED BUT WE HAVE THE DEVICE IN THE LIST--->>',
              scannedSmartTagDevice,
            );
            const deviceOnButNotConnected =
              scannedDeviceList.find(
                device => device.id === pairedSmartTagDevice?.id,
              ) ||
              (scannedSmartTagDevice.id === pairedSmartTagDevice?.id
                ? scannedSmartTagDevice
                : null);

            console.log(
              'NOW WE WILL FIND THE DEVICE FROM SCANNED LIST ANDTHEN START IT CONNECTION PROCESS--->>>',
              deviceOnButNotConnected,
            );

            if (deviceOnButNotConnected) {
              console.log('NOW EXECCUTING CONNECTTODEVICE FUNCTION---->>>');
              connectToDevice(deviceOnButNotConnected?.address);
            }
          }
        })
        .catch(error => {
          console.error(error, 'error in checking is device connected');
          setIsConnected(false);
          dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
        });
    } else {
      console.log('No paired device found');
      setIsConnected(false);
      dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
    }

    console.log(
      'when bt status change then is thsis checkIngCOnnect run again',
    );
    if (!deviceAddress) {
      return;
    }
  };

  // ------App state
  useEffect(() => {
    // console.log('App ddddd');
    const handleAppStateChange = nextAppState => {
      console.log(`App State: ${nextAppState}`);
      if (nextAppState === 'active') {
        setAppState(true);
        if (isEmpty(scannedSmartTagDevice)) {
          console.log('Scannes device chalu karra hu mai..');
          // checkBluetoothStatus();
          NutSdkModule.startScanning();
        }
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        console.log('Scannes device list hata ra hu mai..');
        dispatch(bluetoothActions.setScannedSmartTagDevice({}));
        setAppState(false);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => subscription.remove();
  }, []);

  useEffect(() => {
    checkBluetoothStatus();
    if (!isEmpty(pairedSmartTagDevice) && pairedSmartTagDevice?.address) {
      console.log(
        'DEVICE DATA IN REDUX PREVIOSULY NOW CHECKING IS CONNECTED OR NOT->>',
      );
      checkingIsDeviceConnect(pairedSmartTagDevice?.address); //when component will mount will ccheck for pairedDevice if pairedDevice is there then
    }
  }, [
    currentBluetoothStatus,
    scannedDeviceList,
    scannedSmartTagDevice,
    appState,
  ]);

  //write a function here in the useEffect to catch the event coming from the native module
  //that is the scanned bluetooth nut ble devices
  // and store it in a state
  useEffect(() => {
    // Listen for the "ScannedDevices" event emitted from the native module
    const subscription = DeviceEventEmitter.addListener(
      'ScannedDevices',
      scannedDevicesArray => {
        console.log(
          'scanned Devicec--->>>>',
          scannedDevicesArray,
          typeof scannedDevicesArray,
        );

        setScannedDeviceList(scannedDevicesArray);
      },
    );

    if (Platform.OS === 'ios') {
      if (NativeModules.NutSdkModule) {
        try {
          const eventEmitter = new NativeEventEmitter(NutSdkModule);
          const deviceDiscoveredListener = eventEmitter.addListener(
            'discoveredDevice',
            deviceData => {
              const existingDeviceIndex = scannedDeviceList.findIndex(
                device => device.device === deviceData.device,
              );

              if (existingDeviceIndex === -1) {
                // Device not found in list
                // Update scannedDeviceList with unique device
                setScannedDeviceList([...scannedDeviceList, deviceData]);
                dispatch(bluetoothActions.setScannedSmartTagDevice(deviceData));
              } else {
                // Optionally update device data if needed (e.g., latest RSSI)
                console.log('Duplicate Device:', deviceData); // Optional logging
                dispatch(bluetoothActions.setScannedSmartTagDevice(deviceData));
              }
            },
          );

          const connectListener = eventEmitter.addListener(
            'deviceDidConnect',
            data => {
              console.log(
                'Device Connected from Listener connectListner: jŚSSSSSS',
                data,
              );
              if (!isEmpty(pairedSmartTagDevice)) {
                if (data?.device === pairedSmartTagDevice?.address) {
                  setIsConnected(true);
                  dispatch(
                    bluetoothActions.setSmartTagDeviceConnectionState(true),
                  );
                  //For checkig background mode auto connect working
                  // NutSdkModule.beepDevice(
                  //   pairedSmartTagDevice?.address,
                  //   true,
                  //   30,
                  // )
                  //   .then(result => {
                  //     // console.log("Beep command successful:", result);
                  //   })
                  //   .catch(error => {
                  //     console.error('Beep command failed: in Home', error);
                  //   });
                  // setTimeout(() => {
                  //   setIsConnected(false);
                  //   NutSdkModule.beepDevice(
                  //     pairedSmartTagDevice?.address,
                  //     false,
                  //     30,
                  //   )
                  //     .then(result => {
                  //       // console.log("Beep command successful:", result);
                  //     })
                  //     .catch(error => {
                  //       console.error('Beep command failed:', error);
                  //     });
                  // }, 1000);
                }
              }
            },
          );

          const disconnectListener = eventEmitter.addListener(
            'deviceDidDisconnected',
            data => {
              if (data?.device === pairedSmartTagDevice?.address) {
                setIsConnected(false);
                dispatch(
                  bluetoothActions.setSmartTagDeviceConnectionState(false),
                );
                setScannedDeviceList([]);
                NutSdkModule.startScanning(); // Start scanning
              }
            },
          );

          return () => {
            deviceDiscoveredListener.remove();
            connectListener.remove();
            disconnectListener.remove();
          };
        } catch (error) {
          console.error(error, 'from ios scanning listener');
        }
      }
    }
    // Unsubscribe when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  const handleDeviceConnected = deviceData => {
    // Update the state with the connected device data
    console.log(
      '🚀 ~ handleDeviceConnected ~ deviceData event in react:',
      deviceData,
    );
    setIsConnected(true);
    dispatch(bluetoothActions.setSmartTagDeviceConnectionState(true));
    Toast.show('Smart Tag Connected');
  };

  useEffect(() => {
    // Subscribe to the DeviceConnected event
    const deviceConnectedListener = DeviceEventEmitter.addListener(
      'DeviceConnected',
      handleDeviceConnected,
    );

    // Cleanup function to unsubscribe when the component unmounts
    return () => {
      deviceConnectedListener.remove();
    };
  }, []);

  const handleDeviceDisconnected = event => {
    const {deviceName} = event;
    console.log(
      `Device disconnected Event catch in react component===>>: ${deviceName}`,
    );
    setIsConnected(false);
    dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
    getCurrentLocation(); //storing mobile location...
    NutSdkModule.startScanning();
    // Vibration.vibrate([1000, 1000, 1000, 1000]);
    // Vibration.vibrate(90);
  };

  useEffect(() => {
    const deviceDisconnectedListener = DeviceEventEmitter.addListener(
      'DeviceDisconnected',
      handleDeviceDisconnected,
    );

    // Unsubscribe when the component unmounts
    return () => {
      // DeviceEventEmitter.removeListener(
      //   "DeviceDisconnected",
      //   handleDeviceDisconnected
      // );

      deviceDisconnectedListener.remove();
    };
  }, []);

  useFocusEffect(
    useCallback(() => {
      savePageViewData('home');
    }, []),
  );

  // Save Trip Name API Integration
  const createTrip = async () => {
    if (
      connectedDeviceList?.trailer_detail < 1 ||
      isEmpty(connectedDeviceList)
    ) {
      Alert.alert('Please add Trailer First.');
      return;
    }
    try {
      setTripLoader(true);
      const data = {
        trip_name: search,
        trip_type: 'random_trip',
      };
      const response = await getApiData(
        BaseSetting.endpoints.saveTripName,
        'POST',
        data,
        '',
        true,
      );
      if (response?.success) {
        dispatch(setTripPlaceName(search));
        dispatch(setTripId(response?.data?.id));
        navigation.navigate('SearchTrip', {
          tripName: search,
        });
        setSearch('');
      }
      setTripLoader(false);
    } catch (error) {
      setTripLoader(false);
      console.log('error for device list ===', error);
      sendErrorReport(error, 'saving_trip_name');
    }
  };
  // End Save Trip Name API Integration

  //_______________END JAVA TESTING METHODS_________________  //request to on bluetooth
  return (
    <>
      <View
        style={{
          flex: 1,
          backgroundColor: BaseColor.whiteColor,
          marginTop:
            iPhone14 <= 25
              ? getStatusBarHeight() + 30
              : getStatusBarHeight() + 20,
        }}>
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={[
            {
              flexGrow: 1,
              paddingHorizontal: 16,
            },
          ]}
          bounces={false}
          showsVerticalScrollIndicator={false}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <View style={{}}>
              <Text
                style={{
                  fontFamily: FontFamily.bold,
                  color: BaseColor.blackColor,
                  fontSize: 20,
                  width: '90%',
                }}>
                {generateGreetings()}{' '}
                {userData?.full_name
                  ? userData.full_name.split(' ').slice(0, 1)
                  : 'User'}
                !
              </Text>
              <View style={{width: '80%', marginTop: 10}}>
                <Text
                  style={{
                    fontFamily: FontFamily.regular,
                    color: BaseColor.blackColor,
                    fontSize: 13,
                    // letterSpacing: 1,
                    lineHeight: 20,
                  }}>
                  {weatherText}
                </Text>
              </View>
            </View>
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 15,
              }}>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate(translate('qeridoo'));
                }}
                activeOpacity={0.7}
                style={[
                  styles.addProductContainer,
                  {
                    borderWidth: connectedDeviceList?.trailer_image ? 3 : 1.5,
                    borderStyle: connectedDeviceList?.trailer_image
                      ? 'solid'
                      : 'dotted',
                  },
                ]}>
                {connectedDeviceList?.trailer_image ? (
                  <Image
                    source={{uri: connectedDeviceList?.trailer_image}}
                    style={{
                      width: '100%',
                      height: '100%',
                      borderRadius: 12,
                      resizeMode: 'contain',
                    }}
                  />
                ) : (
                  <CustomIcon name="plus" size={20} color={BaseColor.primary} />
                )}
              </TouchableOpacity>
              <Text
                style={{
                  fontFamily: FontFamily.bold,
                  color: '#87B5B3',
                  fontSize: 13,
                }}>
                Add Product
              </Text>
            </View>
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: 10,
            }}>
            <CustomIcon
              name="location-pin-outline"
              size={20}
              color={'#5E5F60'}
            />
            <Text
              style={{
                fontSize: 14,
                color: '#5E5F60',
                marginLeft: 5,
              }}>
              #
              {`${
                userLocalAddress?.localAddress1
                  ? userLocalAddress?.localAddress1
                  : userLocalAddress?.localAddress2
                  ? userLocalAddress?.localAddress2
                  : userLocalAddress?.localAddress3
                  ? userLocalAddress?.localAddress3
                  : ''
              }`}
            </Text>
          </View>
          {!isEmpty(connectedDeviceList) && <MemoriesMoment />}
          {/* Tab Section */}
          <View style={styles.tabContainer}>
            <View style={{marginHorizontal: 10}}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    fontSize: 16,
                    color: '#040415',
                    fontFamily: FontFamily.bold,
                    marginVertical: 5,
                    marginRight: 10,
                  }}>
                  Trips
                </Text>
                {connectedDeviceList?.trip_detail >= 1 && (
                  <Text
                    onPress={() => navigation.navigate('TripHistory')}
                    style={{
                      textDecorationLine: 'underline',
                      color: BaseColor.primary,
                      fontSize: 12,
                      fontFamily: FontFamily.robotoLight,
                    }}>
                    View Trip History
                  </Text>
                )}
              </View>
              <View style={styles.tabBar}>
                {tabArr &&
                  tabArr.map((li, ind) => {
                    return (
                      <TouchableOpacity
                        key={li.type}
                        activeOpacity={0.7}
                        style={[
                          styles.tabButton,
                          selectedTab === li.type && styles.activeTabButton,
                          ind === 0 && {
                            borderTopLeftRadius: 8,
                            borderBottomLeftRadius: 8,
                          },
                          ind === 2 && {
                            borderTopRightRadius: 8,
                            borderBottomRightRadius: 8,
                          },
                        ]}
                        onPress={() => {
                          handleTabPress(li?.type);
                          setSearch('');
                          setSearchError({error: false, message: ''});
                        }}>
                        <Text
                          style={[
                            selectedTab === li.type
                              ? styles.activeTabButtonText
                              : styles.tabButtonText,
                          ]}>
                          {li?.name}
                        </Text>
                      </TouchableOpacity>
                    );
                  })}
              </View>
              {connectedDeviceList?.trip_detail >= 1 &&
                selectedTab === 'repeat' && (
                  <TriphistoryComponent
                    onPress={(tripName, gpxId) =>
                      createNearestTrip(tripName, gpxId)
                    }
                    type="repeat"
                  />
                )}
            </View>
            {(connectedDeviceList?.trip_detail < 1 ||
              isEmpty(connectedDeviceList)) &&
              selectedTab === 'repeat' && (
                <Text
                  style={{
                    textAlign: 'center',
                    marginVertical: 10,
                    lineHeight: 20,
                    color: '#486264',
                  }}>
                  No repeated trips detected. Add your trailer using “Add
                  Device” icon on the top right corner to begin!
                </Text>
              )}
            {selectedTab === 'search' && (
              <View style={styles.tabContent}>
                <CInput
                  value={search}
                  onChangeText={handleSearch}
                  placeholder={translate('whereWouldYouLikeToGo')}
                  placeholderTextColor={BaseColor.opal}
                  rightIcon
                  iconName={'search'}
                  iconColor={BaseColor.opal}
                  showError={showSearchAlert.error}
                  errorMsg={showSearchAlert.message}
                  onRightIconPress={() =>
                    setSelectedFilter({...selectedFilter, title: search})
                  }
                />
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: 20,
                  }}>
                  <Text style={styles.preferredLocationValue}>
                    {showPrediction ? translate('preferredLocationType') : ''}
                  </Text>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        color: showPrediction ? BaseColor.primary : '#C4D6D6',
                        marginHorizontal: 10,
                        fontFamily: FontFamily.regular,
                      }}>
                      Auto Suggestions
                    </Text>
                    <SwitchComponent
                      value={showPrediction}
                      onValueChange={toggleSwitch}
                    />
                  </View>
                </View>
                {selectedTab === 'search' && showPrediction && (
                  <>
                    <View style={styles.preferredLocationContainer}>
                      <FlatList
                        data={preferredLocationData}
                        keyExtractor={(item, index) => index}
                        horizontal
                        renderItem={renderPreferredLocationItem}
                        showsHorizontalScrollIndicator={false}
                      />
                    </View>
                    <View style={styles.sliderContainer}>
                      <View style={styles.sliderValueContainer}>
                        <Text style={styles.sliderTextPlaceholder}>
                          {translate('distance')}
                        </Text>
                        <Text style={styles.sliderTextValue}>
                          {previewSliderValue} {translate('miles')}
                        </Text>
                      </View>
                      <Slider
                        style={styles.slider}
                        minimumValue={0}
                        maximumValue={100}
                        value={sliderValue}
                        onValueChange={inp => {
                          setSliderValue(inp);
                        }}
                        onSlidingComplete={value => {
                          setPreviewSliderValue(value);
                          setSelectedFilter({
                            ...selectedFilter,
                            distance: value,
                          });
                          milesToMeters(value);
                        }}
                        minimumTrackTintColor="#D9D9D9"
                        maximumTrackTintColor="#D9D9D9"
                        thumbTintColor={BaseColor.primary}
                        step={1}
                      />
                    </View>
                    {!isEmpty(otherPreferenceData) && (
                      <View>
                        <Text style={styles.opPlaceholder}>
                          {translate('otherPreferences')}
                        </Text>
                        <View style={styles.container}>
                          <FlatList
                            data={otherPreferenceData}
                            renderItem={renderOPFilterItem}
                            keyExtractor={(item, index) => index}
                            horizontal
                            showsHorizontalScrollIndicator={false}
                          />
                        </View>
                      </View>
                    )}
                    {!isEmpty(nearestLocation) && (
                      <View>
                        <View style={styles.suggestionSubContainer}>
                          <Text style={styles.suggestionPlaceholder}>
                            {translate('suggestion')}
                          </Text>
                          <TouchableOpacity
                            onPress={() =>
                              Alert.alert('New Feature Coming Soon')
                            }
                            style={{paddingRight: 15}}>
                            <Text style={styles.suggestionMore}>
                              {translate('seeAll')}
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View style={[styles.suggestionCardContainer]}>
                          <SuggestionFlatList
                            places={nearestLocation}
                            navigation={navigation}
                            onCardClick={(title, id) =>
                              createNearestTrip(title, id)
                            }
                          />
                        </View>
                      </View>
                    )}
                    {!isEmpty(nearestLocation) && (
                      <View style={styles.nearestPlaceContainer}>
                        <View style={styles.nearestPlacePlaceholderConatiner}>
                          <Text style={styles.nearestPlacePlaceholder}>
                            {translate('nearestPlaces')}
                          </Text>
                          <TouchableOpacity
                            onPress={() =>
                              Alert.alert(`New Feature Coming Soon`)
                            }>
                            <Text style={styles.nearestPlaceMore}>
                              {translate('seeAll')}
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View style={[styles.nPCardContainer]}>
                          <FlatList
                            nestedScrollEnabled
                            data={nearestLocation}
                            keyExtractor={(item, index) => index}
                            renderItem={renderNearestPlaceItem}
                            maxHeight={height / 2}
                          />
                        </View>
                      </View>
                    )}
                  </>
                )}
              </View>
            )}
            {selectedTab === 'createTrip' && (
              <View>
                <CInput
                  value={search}
                  onChangeText={handleSearch}
                  placeholder={translate('Enter Trip Name Here')}
                  placeholderTextColor={BaseColor.opal}
                  rightIcon
                  iconColor={BaseColor.opal}
                  showError={searchError.error}
                  errorMsg={searchError.message}
                  customContent={
                    <TouchableOpacity
                      activeOpacity={0.7}
                      onPress={() => {
                        if (search) {
                          setSearchError({
                            error: false,
                            message: '',
                          });
                          createTrip();
                        } else {
                          setSearchError({
                            error: true,
                            message: 'Please enter trip name',
                          });
                        }
                      }}
                      style={{flexDirection: 'row', marginHorizontal: 10}}>
                      <Text
                        style={{
                          fontSize: 12,
                          fontFamily: FontFamily.bold,
                          color: '#486264',
                          paddingHorizontal: 10,
                        }}>
                        Next
                      </Text>
                      {tripLoader ? (
                        <ActivityIndicator color={'#486264'} />
                      ) : (
                        <AIcon
                          name="rightcircleo"
                          size={20}
                          color={'#486264'}
                        />
                      )}
                    </TouchableOpacity>
                  }
                />
                <HighLights />
                <Challenges />
              </View>
            )}
          </View>
          {selectedTab === 'repeat' && (
            <View
              style={{
                borderColor: '#ECF1F1',
                paddingBottom: 20,
              }}>
              {connectedDeviceList?.trip_detail >= 1 ? (
                <>
                  <HighLights />
                  <Challenges />
                </>
              ) : (
                <>
                  <StepComponent />
                  {sliderProgress >= 1 && (
                    <CButton
                      style={{width: '85%', borderRadius: 5}}
                      title={'Redeem Coupon on Qeridoo Merch '}
                      titleStyle={{fontSize: 12, fontFamily: FontFamily.bold}}
                      rightCustomIcon
                      onPress={() => openInAppBrowser('https://qeridoo.de')}
                      rightIcon={'Upload-link'}
                    />
                  )}
                </>
              )}
            </View>
          )}
          <MaintenanceInfo />
          <Advertiesment />
          <FeatureFavourite />
          <View style={styles.smartDeviceView}>
            <Text
              style={{
                fontFamily: FontFamily.regular,
                color: BaseColor.whiteColor,
                fontSize: 12,
                margin: 20,
              }}>
              MY DEVICES
            </Text>
          </View>
        </ScrollView>
        <CAlert
          visible={alertModal}
          onRequestClose={() => setAlertModal(false)}
          alertMessage={translate('homeAlertMsg')}
          alertTitle={translate('homeAlertTitle')}
        />
        {!isEmpty(inAppMsgData) ? (
          <InAppModal
            visible={inAppModal}
            button={inAppMsgData?.button_info || []}
            title={!isEmpty(inAppMsgData) ? inAppMsgData?.text_info : {}}
            image={!isEmpty(inAppMsgData) ? inAppMsgData?.post_file : ''}
            position={
              !isEmpty(inAppMsgData) ? inAppMsgData?.message_position : ''
            }
            onClose={() => {
              setinAppMsgData({});
              setInAppModal(false);
              addAction(inAppMsgData, 'clicked', token);
            }}
          />
        ) : null}
      </View>
      <Modal
        visible={loader}
        transparent
        style={{
          flex: 1,
        }}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: BaseColor.black30,
          }}>
          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              justifyContent: 'center',
              alignItems: 'center',
              padding: 24,
              paddingHorizontal: 32,
              borderRadius: 24,
            }}>
            <ActivityIndicator size={24} color={BaseColor.blueDark} />
            <Text
              style={{
                color: BaseColor.blackColor,
                fontWeight: 'bold',
                marginTop: 8,
              }}>
              Loading
            </Text>
          </View>
        </View>
      </Modal>
      <Modal
        visible={syncLoader}
        transparent
        style={{
          flex: 1,
        }}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: BaseColor.black30,
          }}>
          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              justifyContent: 'center',
              alignItems: 'center',
              padding: 24,
              paddingHorizontal: 32,
              borderRadius: 24,
            }}>
            <ProgressBar />
            <Text
              style={{
                color: BaseColor.blackColor,
                fontWeight: 'bold',
                marginTop: 8,
              }}>
              Syncing data please wait!
            </Text>
          </View>
        </View>
      </Modal>
      <Modal
        visible={firstTimeLogin}
        transparent
        style={{
          flex: 1,
        }}>
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => dispatch(setFirstTimeLogin(false))}
          style={{
            flex: 1,
            backgroundColor: BaseColor.black90,
            alignItems: 'center',
          }}>
          <TouchableOpacity
            onPress={() => null}
            disabled
            style={{
              position: 'absolute',
              right: IOS ? 5 : 1,
              top: IOS ? getStatusBarHeight() + 20 : 20,
            }}>
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 15,
              }}>
              <TouchableOpacity
                onPress={() => {}}
                activeOpacity={0.7}
                style={styles.addProductContainer}>
                {<CustomIcon name="plus" size={20} color={BaseColor.primary} />}
              </TouchableOpacity>
              <Text
                style={{
                  fontFamily: FontFamily.bold,
                  color: '#87B5B3',
                  fontSize: 13,
                }}>
                Add Product
              </Text>
            </View>
          </TouchableOpacity>
          <View
            style={{
              position: 'absolute',
              top: height / 6.5,
              right: width / 9,
              alignItems: 'center',
            }}>
            <CustomIcon
              name="point-out"
              color={BaseColor.whiteColor}
              size={30}
              style={{marginBottom: 5}}
            />
            <Text
              style={{
                color: BaseColor.whiteColor,
                fontSize: 14,
                textAlign: 'center',
              }}>
              {`Tap here to add \n & manage your smart \n device`}
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => null}
            disabled
            style={{
              position: 'absolute',
              top: height / 2.8,
              backgroundColor: BaseColor.whiteColor,
              width: '95%',
              borderRadius: 10,
              paddingHorizontal: 15,
              paddingVertical: 20,
              alignItems: 'center',
            }}>
            <StepComponent />
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>
      <Modal
        visible={trailerConnectedModal}
        transparent
        style={{
          flex: 1,
        }}>
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => {
            setSelectedTab('search');
            dispatch(setTrailerConnectedModal(false));
            setSearchModal(true);
          }}
          style={{
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.83)',
            alignItems: 'center',
          }}>
          <TouchableOpacity
            onPress={() => null}
            disabled
            style={{
              position: 'absolute',
              top: height / 2.7,
              backgroundColor: BaseColor.whiteColor,
              width: '95%',
              borderRadius: 10,
              padding: 15,
              paddingBottom: 20,
              alignItems: 'center',
            }}>
            <StepComponent />
            <CButton
              style={{
                width: '90%',
                borderRadius: 5,
              }}
              title={'Redeem Coupon on Qeridoo Merch  '}
              onPress={() => openInAppBrowser('https://qeridoo.de')}
              rightCustomIcon
              rightIcon={'Upload-link'}
              titleStyle={{fontSize: 12, fontFamily: FontFamily.bold}}
            />
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>
      <Modal
        visible={searchModal}
        transparent
        style={{
          flex: 1,
        }}>
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => {
            setSearchModal(false);
          }}
          style={{
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.83)',
          }}>
          <View>
            <View
              style={{
                position: 'absolute',
                top: height / (IOS ? 1.5 : 1.38),
                right: 15,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    color: '#C4D6D6',
                    marginHorizontal: 10,
                    fontFamily: FontFamily.regular,
                  }}>
                  Auto Suggestions
                </Text>
                <SwitchComponent
                  value={showPrediction}
                  // onValueChange={toggleSwitch}
                />
              </View>
            </View>
            <View
              style={{
                position: 'absolute',
                backgroundColor: BaseColor.primary,
                top: height / (IOS ? 1.98 : 1.85),
                right: width / 2.78,
              }}>
              <Text
                style={{
                  paddingVertical: 9,
                  paddingHorizontal: 30,
                  fontFamily: FontFamily.bold,
                  fontSize: 16,
                  color: BaseColor.whiteColor,
                }}>
                Search
              </Text>
            </View>
            <View
              style={{
                position: 'absolute',
                top: height / 1.18,
                right: width / 5,
              }}>
              <Text
                style={{
                  color: BaseColor.whiteColor,
                  textAlign: 'center',
                  fontFamily: FontFamily.regular,
                  lineHeight: 20,
                }}>
                {`Tap here on trip icon \n to access camera and all info related to your trips`}
              </Text>
            </View>
            <View
              style={{
                position: 'absolute',
                top: height / 1.1,
                right: width / 2.5,
              }}>
              <CustomIcon
                name="point-out"
                color={BaseColor.whiteColor}
                size={30}
                style={{transform: [{rotate: '90deg'}]}}
              />
            </View>
            <View
              style={{
                position: 'absolute',
                top: height / 1.05,
                right: width / 2.85,
              }}>
              <CustomIcon
                color={BaseColor.whiteColor}
                size={20}
                name="cam-dash"
              />
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default Home;
