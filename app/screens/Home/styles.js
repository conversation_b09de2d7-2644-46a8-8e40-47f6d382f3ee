/* eslint-disable quotes */
import {Dimensions, Platform, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily, FontWeight} from '../../config/typography';

const IOS = Platform.OS === 'ios';
const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deviceCard: {
    height: 65,
    width: 65,
    overflow: 'hidden',
    // borderRadius: 16,
    marginEnd: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColor.whiteColor,
    borderWidth: 2,
    borderStyle: 'solid',
    // padding: 5,
  },
  smartDeviceView: {
    marginTop: 54,
    // backgroundColor: BaseColor.darkBg,
  },
  guideView: {
    marginTop: 36,
  },
  guideRootView: {
    marginVertical: 35,
    // display: "flex",
    height: 248,
    marginRight: 10,
  },
  videoView: {
    marginTop: 19,
    // flex: 1,
    backgroundColor: BaseColor.whiteColor,
    borderTopEndRadius: 30,
  },
  fileManager: {
    marginTop: -30,
    // flex: 1,
    backgroundColor: '#5F5F5F',
    borderTopStartRadius: 30,
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    right: 12,
    top: 34,
  },
  emptyComponent: {
    minHeight: 450,
    alignItems: 'center',
    justifyContent: 'center',
  },
  childImgStyle: {
    height: 25,
    width: 25,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: BaseColor.black60,
  },
  childNameStyle: {
    fontFamily: FontFamily.regular,
    fontWeight: '600',
    fontSize: 8,
    paddingLeft: 10,
    width: 50,
  },

  // tab bar style

  tabContainer: {
    marginVertical: 10,
    marginBottom: 20,
    borderColor: '#ECF1F1',
  },
  tabBar: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  tabButton: {
    borderWidth: 1,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: BaseColor.neyonWhite,
    borderColor: '#8AB0AE',
  },
  activeTabButton: {
    borderWidth: 0,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: BaseColor.primary,
  },
  tabButtonText: {
    padding: 10,
    fontFamily: FontFamily.regular,
    fontSize: 16,
    color: BaseColor.primary,
  },
  activeTabButtonText: {
    padding: 10,
    fontFamily: FontFamily.bold,
    fontSize: 16,
    color: BaseColor.whiteColor,
  },

  tabContent: {
    paddingBottom: 20,
    borderColor: '#ECF1F1',
  },

  // search bar

  searchContainer: {
    borderWidth: 1,
    height: 48,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    borderRadius: 8,
    paddingLeft: 16,
    borderColor: BaseColor.coolmint,
  },
  searchInput: {
    // borderWidth: 1,
    // paddingTop: 14,
    // paddingBottom: 15,
    overflow: 'hidden',
    fontFamily: FontFamily.regular,
    fontSize: 16,
    color: BaseColor.darkSlateGrey,
    width: '90%',
    height: '100%',
  },
  searchIcon: {
    width: '10%',
  },

  // slider distance
  sliderContainer: {
    marginVertical: 21,
  },

  sliderValueContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
  },

  sliderTextPlaceholder: {
    color: BaseColor.blackColor,
    fontFamily: FontFamily.regular,
    fontSize: 16,
  },
  sliderTextValue: {
    color: BaseColor.cadetBlue,
    fontFamily: FontFamily.regular,
    fontSize: 16,
  },
  slider: {
    marginTop: 8,
    height: 40,
    // transform: [{scaleY: IOS ? 1 : 1.5}, {scaleX: IOS ? 1 : 1.5}],
  },

  // preferred Location
  preferredLocationContainer: {
    marginVertical: 25,
  },

  preferredLocationValue: {
    color: BaseColor.blackColor,
    fontFamily: FontFamily.regular,
    fontSize: 16,
  },

  preferredCardContainer: {
    alignItems: 'center',
    paddingRight: 15,
  },

  preferredCard: {
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: 45,
    height: 45,
    borderRadius: 50,
    borderColor: BaseColor.antiFlashWhite,
    backgroundColor: BaseColor.coolmint1,
    marginBottom: 6,
  },

  preferredLocationName: {
    fontSize: 12,
    fontFamily: 'Roboto',
    color: BaseColor.ashGray,
    flexWrap: 'wrap',
    textAlign: 'center',
    width: 50,
  },

  // other prefrence

  opPlaceholder: {
    color: BaseColor.blackColor,
    fontFamily: FontFamily.regular,
    fontSize: 16,
    lineHeight: 20,
  },

  container: {
    marginTop: 20,
  },
  button: {
    backgroundColor: BaseColor.coolmint2,
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
  },
  selectedButton: {
    backgroundColor: BaseColor.primary,
    paddingHorizontal: 15,
    paddingVertical: 6,
    borderRadius: 5,
  },
  cross: {
    color: BaseColor.whiteColor,
    marginLeft: 10,
  },

  suggestionCotainer: {
    marginTop: 30,
    paddingLeft: 9,
    paddingRight: 19,
  },

  suggestionSubContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 25,
    alignItems: 'center',
  },

  suggestionPlaceholder: {
    color: BaseColor.darkCharcoal,
    fontSize: 16,
    fontFamily: FontFamily.regular,
  },

  suggestionMore: {
    color: BaseColor.primary,
    fontSize: 16,
    fontFamily: FontFamily.regular,
  },

  suggestionCardContainer: {
    flexDirection: 'row',
    marginTop: 18,
    justifyContent: 'space-between',
  },

  nearestPlaceContainer: {
    marginTop: 20,
  },

  nearestPlacePlaceholderConatiner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  nearestPlacePlaceholder: {
    fontSize: 24,
    fontFamily: FontFamily.regular,
    color: BaseColor.darkCharcoal,
  },
  nearestPlaceMore: {
    color: BaseColor.primary3,
    fontSize: 16,
    fontFamily: FontFamily.bold,
  },

  nPCardContainer: {
    marginTop: 16,
    width: '100%',
  },

  nPCard: {
    width: '100%',
    borderWidth: 1,
    backgroundColor: '#ffffff',
    borderRadius: 14,
    marginBottom: '3%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderColor: '#FBFDFD',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.1,
    shadowRadius: 1.84,
  },

  nPCardImage: {
    width: 74,
    height: 81,
    borderTopLeftRadius: 8,
  },

  nPCardInfo: {
    paddingLeft: 8,
    flex: 1,
    paddingVertical: 10,
  },
  nPCardInfoTitle: {
    fontSize: 12,
    color: '#486264',
    fontFamily: FontFamily.regular,
  },

  nPCardInfoAddress: {
    fontSize: 11,
    fontFamily: FontFamily.regular,
    color: '#B8C9C9',
  },

  npCardDistance: {
    fontSize: 12,
    fontFamily: FontFamily.bold,
    color: BaseColor.textGrey1,
    marginTop: 9,
  },

  nPCardBtn: {
    backgroundColor: BaseColor.primary,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    height: 32,
    width: '80%',
  },

  nPCardBtnText: {
    color: BaseColor.whiteColor,
    fontSize: 13,
    fontFamily: FontFamily.bold,
    letterSpacing: 0,
    fontWeight: 'normal',
  },
  addProductContainer: {
    width: 51,
    height: 53,
    borderRadius: 12,
    borderWidth: 1.5,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColor.primary,
    borderStyle: 'dashed',
    marginBottom: 10,
  },
  tripText: {
    fontSize: 9,
    fontFamily: FontFamily.regular,
    color: '#486264',
  },
});

export default styles;
