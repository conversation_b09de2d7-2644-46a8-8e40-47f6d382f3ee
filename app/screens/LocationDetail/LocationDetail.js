import React, {useState, useEffect} from 'react';
import {
  AppState,
  BackHandler,
  Dimensions,
  Image,
  Text,
  TouchableOpacity,
} from 'react-native';
import {View} from 'react-native-animatable';
import BaseColor from '../../config/colors';
import {ScrollView} from 'react-native-gesture-handler';
import ImageHeader from '../../components/LocationDetail/ImageHeader';
import suggestionCardDummyData from '../../assets/dummyCard/dummyCard';
import styles from './styles';
import CButton from '../../components/CButton';
import TripModal from '../../components/TripModal/TripModal';
import Divider from '../../components/Divider/Divider';
import {FontFamily, FontWeight} from '../../config/typography';
import GetLocation from 'react-native-get-location';
import MapView, {<PERSON><PERSON><PERSON>, <PERSON><PERSON>, PROVIDER_GOOGLE} from 'react-native-maps';
import {sendErrorReport} from '../../utils/commonFunction';
import RatingStar from '../../components/RatingStart';
import Iconin from 'react-native-vector-icons/Ionicons';
import dummyNearestPlaces from '../../assets/dummyNearestPlaces/dummyNearestPlaces';
import {isEmpty} from 'lodash';
import environment from '../../config/environment';
import mapStyle from '../../config/mapCustomStyle';

const LocationDetail = ({navigation, route}) => {
  const myApiKey = environment.mapKey;
  const {id} = route.params && route?.params;
  const [dummyData, setDummyData] = useState([
    ...suggestionCardDummyData,
    ...dummyNearestPlaces,
  ]);
  const data = dummyData.find(item => item.id === id);
  const [istripModal, setIsTripModal] = useState(false);
  const [loc, setLoc] = useState({});
  const [appState, setAppState] = useState(true);
  const [placeDetails, setPlaceDetails] = useState({});
  const [travelingTime, setTravelingTime] = useState('');
  const [loader, setLoader] = useState(false);

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  // ------App state

  useEffect(() => {
    console.log('App ddddd');
    const handleAppStateChange = nextAppState => {
      console.log(`App State: ${nextAppState}`);
      if (nextAppState === 'active') {
        console.log('App State======', true);
        // sendErrorReport(true, "app_state_active");
        setAppState(true);
      } else if (nextAppState === 'background') {
        console.log('App State======', false);
        // sendErrorReport(false, "app_state_background");
        setAppState(false);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => subscription.remove();
  }, []);
  //map
  // this function for get current location
  async function getCurrentLocation() {
    GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: !appState ? 15000 : 0,
    })
      .then(location => {
        sendErrorReport(location, 'SMS_location');
        setLoc(JSON.parse(location));
      })
      .catch(async error => {
        const {code, message} = error;
        if (code === 'UNAVAILABLE') {
          Toast.show(
            'Please enable your location service to send emergency alert.',
          );
          GetLocation.openAppSettings();
        }
      });
  }

  useEffect(() => {
    getCurrentLocation();
  }, []);

  function handleLike() {
    const idx = dummyData.findIndex(item => item.id === id);
    if (idx !== -1) {
      dummyData[idx].isLike = !dummyData[idx].isLike;
      setDummyData([...dummyData]);
    } else {
      setPlaceDetails({...placeDetails, isLike: !placeDetails.isLike});
    }
  }

  return (
    <>
      <View
        style={{
          flex: 1,
          backgroundColor: BaseColor.whiteColor,
          marginBottom: 20,
        }}>
        <ScrollView
          contentContainerStyle={[
            {
              flexGrow: 1,
            },
          ]}
          bounces={false}
          showsVerticalScrollIndicator={true}>
          <ImageHeader
            imgSource={
              !isEmpty(placeDetails)
                ? placeDetails.imageSource
                : data?.imageSource
            }
            title={!isEmpty(placeDetails) ? placeDetails?.title : data?.title}
            reviewCount={
              !isEmpty(placeDetails)
                ? placeDetails?.reviewCount
                : data?.reviewCount
            }
            rating={
              !isEmpty(placeDetails) ? placeDetails?.rating : data?.rating
            }
            photoCount={
              !isEmpty(placeDetails)
                ? placeDetails?.photoCount
                : data?.photoCount
            }
            isLike={!isEmpty(placeDetails) ? placeDetails.isLike : data?.isLike}
            onLeftPress={() => navigation.goBack()}
            onRightPress={handleLike}
            setLoader={setLoader}
            loader={loader}
            // onOptionPress={() => shareImage(data?.imageSource)}
          />
          <View style={{flex: 1}}>
            <View style={styles.aboutContainer}>
              <Text style={styles.aboutPlaceholder}>About</Text>
              <Text style={styles.aboutInfo}>
                {!isEmpty(placeDetails)
                  ? placeDetails.title + ', ' + placeDetails.about
                  : data?.about}
              </Text>
            </View>
            <View style={styles.chooseTrip}>
              <CButton
                title={`Next`}
                style={{width: '80%', height: 54}}
                titleStyle={styles.btnText}
                onPress={() => {
                  setIsTripModal(true);
                }}
              />
            </View>

            <Divider
              width={Dimensions.get('window').width - 43}
              backgroundColor={'rgba(0, 0, 0, 0.10)'}
              divStyle={{marginHorizontal: 20, marginTop: 20}}
            />

            <Text style={styles.expectPlaceholder}>What to expect</Text>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                // borderWidth: 1,
                marginTop: 20,
                paddingHorizontal: 20,
              }}>
              <View style={styles.card}>
                <Text
                  style={{
                    fontSize: 14,
                    color: BaseColor.blackColor,
                    fontWeight: '300',
                    fontFamily: 'Roboto',
                  }}>
                  Busy Area {/* dynamic text from api */}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    color: BaseColor.textGrey1,
                    fontWeight: FontWeight.light,
                    fontFamily: 'Roboto',
                  }}>
                  Noise level high {/* dynamic text  api*/}
                </Text>
              </View>
              <View style={[styles.card, {marginLeft: 10}]}>
                <Text
                  style={{
                    fontSize: 14,
                    color: BaseColor.blackColor,
                    fontWeight: '300',
                    fontFamily: 'Roboto',
                  }}>
                  {travelingTime ? travelingTime : 'Not Found'}
                  {/* dynamic text from api */}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    color: BaseColor.textGrey1,
                    fontWeight: FontWeight.light,
                    fontFamily: 'Roboto',
                  }}>
                  Duration {/* dynamic text  api*/}
                </Text>
              </View>
            </View>

            <Divider
              width={Dimensions.get('window').width - 43}
              backgroundColor={'rgba(0, 0, 0, 0.10)'}
              divStyle={{marginHorizontal: 20, marginTop: 30}}
            />
            {/* map view */}

            <Text style={styles.expectPlaceholder}>Map Location</Text>
            <Text
              style={{
                fontSize: 12,
                color: BaseColor.textGrey1,
                fontFamily: 'Roboto',
                fontWeight: '300',
                marginLeft: 20,
              }}>
              Check out other near by spots!
            </Text>
            {/* map container */}
            <View
              style={{
                marginTop: 20,
                borderRadius: 15,
                marginHorizontal: 20,
                overflow: 'hidden',
              }}>
              <MapView
                provider={PROVIDER_GOOGLE}
                style={{
                  height: 238,
                  width: '100%',
                }}
                customMapStyle={mapStyle}
                region={{
                  latitude: placeDetails?.latitude || 0,
                  longitude: placeDetails?.longitude || 0,
                  latitudeDelta: 0.0922,
                  longitudeDelta: 0.0421,
                }}
                pitchEnabled={true}
                rotateEnabled={false}
                scrollEnabled={true}
                zoomEnabled={true}>
                <Marker
                  coordinate={{
                    latitude: placeDetails?.latitude || 0,
                    longitude: placeDetails?.longitude || 0,
                  }}
                  // title={placeDetails?.title}
                />
              </MapView>
            </View>
            <Text style={{marginTop: 20, marginHorizontal: 26}}>
              More one line info here
            </Text>
            <Divider
              width={Dimensions.get('window').width - 43}
              backgroundColor={'rgba(0, 0, 0, 0.10)'}
              divStyle={{marginHorizontal: 20, marginTop: 20}}
            />

            {/* photos container */}
            {!isEmpty(placeDetails) && placeDetails.morePhotos.length > 2 && (
              <>
                <View style={styles.morePhotoContainer}>
                  <View style={styles.column}>
                    <Image
                      source={{
                        uri: !isEmpty(placeDetails)
                          ? placeDetails?.morePhotos[0]
                          : data?.morePhotos[0],
                      }}
                      style={[styles.morePhotoImage, {borderTopLeftRadius: 6}]}
                    />
                    <Image
                      source={{
                        uri: !isEmpty(placeDetails)
                          ? placeDetails?.morePhotos[1]
                          : data?.morePhotos[1],
                      }}
                      style={[
                        styles.morePhotoImage,
                        {borderBottomLeftRadius: 6},
                      ]}
                    />
                  </View>
                  <View style={[styles.doubleRow, {marginLeft: '1%'}]}>
                    <Image
                      source={{
                        uri: !isEmpty(placeDetails)
                          ? placeDetails?.morePhotos[2]
                          : data?.morePhotos[2],
                      }}
                      style={[
                        styles.morePhotoImage,
                        {
                          height: '101%',
                          borderBottomRightRadius: 6,
                          borderTopRightRadius: 6,
                        },
                      ]}
                    />
                  </View>
                </View>

                <CButton
                  title={`See all +${placeDetails.photoCount - 2} photos`}
                  style={{
                    backgroundColor: BaseColor.whiteColor,
                    borderWidth: 1,
                    borderColor: BaseColor.blackColor,
                    borderRadius: 6,
                    width: '45%',
                    marginHorizontal: 20,
                    alignSelf: 'flex-start',
                  }}
                  titleStyle={{
                    color: BaseColor.blackColor,
                    fontSize: 12,
                  }}
                  anim
                  onPress={() => {
                    alert('do something');
                  }}
                />
              </>
            )}

            {/* review section */}
            {placeDetails &&
              placeDetails.reviews &&
              placeDetails.reviews.length > 0 && (
                <>
                  <Text style={styles.expectPlaceholder}>Reviews</Text>
                  <Text
                    style={{
                      fontSize: 12,
                      color: BaseColor.textGrey1,
                      marginLeft: 20,
                      fontFamily: 'Roboto',
                      fontWeight: '300',
                    }}>
                    {!isEmpty(placeDetails)
                      ? placeDetails.rating
                      : data?.rating}
                    (
                    {!isEmpty(placeDetails)
                      ? placeDetails.reviewCount
                      : data?.reviewCount}
                    reviews)
                  </Text>
                </>
              )}

            {!isEmpty(placeDetails) &&
              placeDetails?.reviews?.map((review, index) => (
                <View
                  key={index}
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    marginHorizontal: 20,
                    marginTop: 20,
                  }}>
                  <View style={styles.reviewCard}>
                    <View style={styles.reviewUserInfo}>
                      <Image
                        source={{uri: review.userAvtar}}
                        style={[styles.userAvtar]}
                      />

                      <View style={{marginLeft: 10}}>
                        <Text
                          style={{
                            fontSize: 14,
                            color: BaseColor.blackColor,
                            fontWeight: FontWeight.light,
                            fontFamily: FontFamily.robotoLight,
                            lineHeight: 20,
                          }}>
                          {review.userName}
                        </Text>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                          }}>
                          <RatingStar
                            editable={false}
                            totalRating={review.rating}
                            iconSize={10}
                            gap={2}
                          />
                          <Text
                            style={{
                              fontSize: 10,
                              marginLeft: 10,
                              color: BaseColor.textGrey1,
                              fontWeight: FontWeight.light,
                              fontFamily: 'Roboto',
                            }}>
                            {review.reviewDate}
                          </Text>
                        </View>
                      </View>
                    </View>

                    <View style={{marginTop: 18}}>
                      <Text
                        style={{
                          fontSize: 12,
                          color: BaseColor.blackColor,
                          fontWeight: '300',
                          fontFamily: 'Roboto',
                        }}
                        numberOfLines={4}>
                        {review?.reviewDescription
                          ? review?.reviewDescription
                          : 'N/A'}
                      </Text>
                    </View>

                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginTop: 25,
                      }}>
                      <View>
                        <Text
                          style={{
                            fontSize: 10,
                            color: BaseColor.textGrey1,
                            fontFamily: 'Roboto',
                            fontWeight: '300',
                          }}>
                          Visited date
                        </Text>
                        <Text
                          style={{
                            fontSize: 10,
                            color: BaseColor.textGrey1,
                            fontFamily: 'Roboto',
                            fontWeight: '300',
                          }}>
                          {review.visited}
                        </Text>
                      </View>
                      <View>
                        <Iconin
                          style={{color: 'black'}}
                          name="arrow-up-circle-outline"
                          size={25}
                          color="#FFFFFF"
                        />
                      </View>
                    </View>
                  </View>
                </View>
              ))}

            {placeDetails &&
              placeDetails.reviews &&
              placeDetails.reviews.length > 0 && (
                <CButton
                  title={`See all +${
                    placeDetails.reviewCount > 5
                      ? placeDetails.reviewCount > 100
                        ? 99
                        : placeDetails.reviewCount - 1
                      : 3
                  } reviews`}
                  style={[
                    {
                      backgroundColor: BaseColor.whiteColor,
                      borderWidth: 1,
                      width: '45%',
                      borderRadius: 6,
                      marginTop: 20,
                      marginHorizontal: 20,
                      alignSelf: 'flex-start',
                    },
                  ]}
                  titleStyle={{
                    color: BaseColor.blackColor,
                    fontSize: 12,
                    fontFamily: FontFamily.regular,
                    fontWeight: 'normal',
                  }}
                  onPress={() => {
                    alert('do something');
                  }}
                />
              )}

            {/* faq */}

            <Text
              style={{
                fontSize: 16,
                color: 'black',
                fontFamily: FontFamily.regular,
                marginTop: 30,
                marginLeft: 20,
              }}>
              People frequently ask
            </Text>

            <View style={{marginTop: 20, marginLeft: 20}}>
              <TouchableOpacity>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <Text
                    style={{
                      fontSize: 14,
                      color: 'black',
                      fontFamily: FontFamily.regular,
                    }}>
                    About this Place
                  </Text>
                  <View
                    style={{
                      marginLeft: 22,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Iconin
                      style={{color: BaseColor.blackColor}}
                      name="chevron-forward-outline"
                      size={18}
                      color="#FFFFFF"
                    />
                  </View>
                </View>
                <Text
                  style={{
                    fontSize: 12,
                    color: BaseColor.textGrey1,
                    fontFamily: 'Roboto',
                  }}>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. A id
                  diam nisl, non justo, in odio...
                </Text>
              </TouchableOpacity>
            </View>
            <View style={{marginVertical: 20, marginLeft: 20}}>
              <TouchableOpacity>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <Text
                    style={{
                      fontSize: 14,
                      color: 'black',
                      fontFamily: FontFamily.regular,
                    }}>
                    Some Do’s & Dont’s
                  </Text>
                  <View
                    style={{
                      marginLeft: 22,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Iconin
                      style={{color: BaseColor.blackColor}}
                      name="chevron-forward-outline"
                      size={18}
                      color="#FFFFFF"
                    />
                  </View>
                </View>
                <Text
                  style={{
                    fontSize: 12,
                    color: BaseColor.textGrey1,
                    fontFamily: 'Roboto',
                  }}>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. A id
                  diam nisl, non justo, in odio...
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </View>
      {/* Modal */}
      <TripModal
        locationDetail={placeDetails ? placeDetails : data}
        navigation={navigation}
        visible={istripModal}
        onClose={() => {
          setIsTripModal(false);
        }}
      />
    </>
  );
};
export default LocationDetail;
