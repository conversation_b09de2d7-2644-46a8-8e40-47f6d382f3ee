/* eslint-disable quotes */
import {StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily, FontWeight} from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },

  aboutContainer: {
    paddingHorizontal: 20,
    paddingVertical: 26,
  },

  aboutPlaceholder: {
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
    fontSize: 20,
    lineHeight: 24,
  },
  aboutInfo: {
    marginTop: 10,
    fontFamily: 'Roboto',
    fontSize: 12,
    color: BaseColor.blackColor,
  },
  chooseTrip: {
    justifyContent: 'center',
    alignItems: 'center',
    // marginTop: 20,
    paddingHorizontal: 20,
  },
  btn: {
    backgroundColor: BaseColor.primary,
    borderRadius: 8,
    // width: 309,
    // paddingTop: 11,
    // paddingBottom: 12,
    // paddingLeft: 92,
    // paddingRight: 101,
  },
  btnText: {
    fontSize: 20,
    fontFamily: FontFamily.bold,
  },

  // expectContainer: {
  //   paddingHorizontal: 26,
  // },

  expectPlaceholder: {
    color: BaseColor.blackColor,
    fontSize: 16,
    fontFamily: FontFamily.regular,
    marginTop: 20,
    marginLeft: 20,
    lineHeight: 20,
  },

  card: {
    borderWidth: 1,
    paddingVertical: 9,
    paddingHorizontal: 15,
    borderRadius: 15,
    borderColor: 'rgba(0, 0, 0, 0.15)',
    flex: 1,
  },

  mapPlaceholderContainer: {
    paddingHorizontal: 26,
    paddingVertical: 20,
  },

  morePhotoContainer: {
    height: 289,
    marginVertical: 30,
    marginHorizontal: 20,
    flexDirection: 'row',
    // justifyContent: "space-between",
  },
  morePhotoImage: {
    width: '100%',
    height: '50%',
    // margin: "1%",
    marginBottom: '1%',
  },
  doubleRow: {flex: 1},
  column: {
    flexDirection: 'column',
    flex: 1,
    // height: "9%",
  },

  // review container
  reviewContainer: {
    marginTop: 40,
  },

  reviewCard: {
    borderWidth: 1,
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 6,
    borderColor: 'rgba(0, 0, 0, 0.15)',
    flex: 1,
    // elevation: 1,
    // shadowColor: "rgba(0, 0, 0, 0.05)",
    // shadowOffset: {
    //   width: 0,
    //   height: 1,
    // },
    // shadowOpacity: 0.1,
    // shadowRadius: 1.84,
  },
  reviewUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  userAvtar: {
    width: 35,
    height: 35,
    borderRadius: 50,
  },
});

export default styles;
