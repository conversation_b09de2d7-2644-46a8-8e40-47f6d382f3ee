/* eslint-disable eqeqeq */
/* eslint-disable quotes */
import React, {useEffect, useRef, useState} from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  BackHandler,
  ScrollView,
} from 'react-native';
import Toast from 'react-native-simple-toast';
// import Bugsnag from "@bugsnag/react-native";
import {useTheme} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import CButton from '../../components/CButton';
import CInput from '../../components/CInput';
import styles from './styles';
import {FontFamily} from '../../config/typography';
import {translate} from '../../lang/Translate';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import BaseColors from '../../config/colors';
import {
  enableAnimateInEaseOut,
  getSliderDetail,
  sendErrorReport,
} from '../../utils/commonFunction';
import AuthAction from '../../redux/reducers/auth/actions';
import CustomHeader from '../../components/CustomHeader/CustomHeader';

/**
 *
 *@module Login
 *
 */
const Login = ({navigation}) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const dispatch = useDispatch();
  const {setUserData, setAccessToken, setDeviceSettingOpen, setIsFarenheit} =
    AuthAction;
  const brandToken = useSelector(state => state.auth.brandToken);
  const [state, setstate] = useState({
    email: '',
    password: '',
    rememberMe: false,
  });
  const [loader, setloader] = useState(false);
  const [emailError, setEmailError] = useState(false);
  const [passwordError, setPasswordError] = useState(false);
  const [emailErrorTxt, setEmailErrorTxt] = useState('');
  const [passwordErrorTxt, setPasswordErrorTxt] = useState('');
  const [hidePwd, setHidePwd] = useState(true);
  const [btnDisable, setBtnDisable] = useState(true);

  const userRef = useRef();
  const passwordRef = useRef();

  function handleBackButtonClick() {
    navigation.navigate('RedirectLS');
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  const Validation = () => {
    enableAnimateInEaseOut();
    if (state.email == '') {
      allErrorFalse();
      setEmailError(true);
      setEmailErrorTxt('Please enter User Name');
    } else if (state.password == '') {
      allErrorFalse();
      setPasswordError(true);
      setPasswordErrorTxt('Please enter Password');
    } else {
      allErrorFalse();
      userLogin();
    }
  };

  const allErrorFalse = () => {
    setEmailError(false);
    setPasswordError(false);
  };

  /** this function for user login
   * @function userLogin
   * @param {object} data username, password, token
   */
  const userLogin = () => {
    setloader(true);
    const data = {
      username: state.email,
      password: state.password,
      token: brandToken,
    };
    getApiData(BaseSetting.endpoints.login, 'POST', data)
      .then(async response => {
        if (response.success) {
          await getSliderDetail(); // Slider API..
          dispatch(setIsFarenheit(false));
          dispatch(setUserData(response?.data?.user));
          dispatch(setAccessToken(response?.data?.token));
          if (response?.data?.user?.status === 'inactive') {
            sendOTP(response?.data?.user?.id);
          } else {
            setTimeout(() => {
              setTimeout(() => {
                setloader(false);
                dispatch(setDeviceSettingOpen(true));
              }, 2000);
              navigation.navigate('DrawerNav');
            }, 3000);
          }
        } else {
          Toast.show(response.message || 'Something went wrong');
          setloader(false);
        }
      })
      .catch(err => {
        Toast.show('Something went wrong while trying to login');
        sendErrorReport(err, 'login_api');
        setloader(false);
      });
  };

  /** this function for send otp
   * @function sendOTP
   * @param {object} data user_id
   */
  const sendOTP = userID => {
    setloader(true);
    const data = {
      user_id: userID,
    };
    getApiData(BaseSetting.endpoints.sendOtp, 'POST', data)
      .then(response => {
        if (response.success) {
          setTimeout(() => {
            setloader(false);
          }, 2000);
          setTimeout(() => {
            navigation.navigate('Otp', {
              type: 'LoginInactive',
              userId: userID,
            });
          }, 3000);
        } else {
          Toast.show(response.message);
          setloader(false);
        }
      })
      .catch(err => {
        Toast.show('Something went wrong while sending otp request');
        sendErrorReport(err, 'send_otp');
        setloader(false);
        console.log('ERRR', err);
      });
  };

  return (
    <>
      <View style={styles.root}>
        <View style={{marginTop: '5%'}}>
          <CustomHeader
            transparentView
            leftIconName="left-arrow"
            borderCircular
            backgroundColor={'#F1F5F9'}
            onLeftPress={() => {
              navigation.navigate('RedirectLS');
            }}
            backBtn
          />
        </View>
        <ScrollView
          contentContainerStyle={{flexGrow: 1, padding: 16, marginBottom: '2%'}}
          showsVerticalScrollIndicator={false}
          bounces={false}>
          <View>
            <Text style={styles.loginTxt}>{translate('loginBtn')}</Text>
          </View>
          <View style={{marginTop: '15%'}}>
            <View>
              <CInput
                title={translate('emailId')}
                ref={userRef}
                placeholder={translate('enterYourEmail')}
                value={state.email}
                onChangeText={val => {
                  setstate({...state, email: val});
                  if (state.password == '' || val == '') {
                    setBtnDisable(true);
                  } else {
                    setBtnDisable(false);
                  }
                }}
                placeholderTextColor={BaseColors.textGrey}
                iconSize={24}
                onSubmitEditing={() => {
                  passwordRef.current.focus();
                }}
                textInputWrapper={{}}
                showError={emailError}
                errorMsg={emailErrorTxt}
              />
            </View>
            <View style={{marginTop: '2%'}}>
              <CInput
                title={translate('loginPassword')}
                ref={passwordRef}
                placeholder={translate('enterYourPassword')}
                value={state.password}
                secureTextEntry={hidePwd}
                onChangeText={val => {
                  setstate({...state, password: val});
                  if (state.email == '' || val == '') {
                    setBtnDisable(true);
                  } else {
                    setBtnDisable(false);
                  }
                }}
                placeholderTextColor={BaseColors.textGrey}
                rightIcon
                iconName={hidePwd ? 'eye-close' : 'eye'}
                iconColor={hidePwd ? BaseColors.primary : BaseColors.blackColor}
                iconSize={24}
                onRightIconPress={() => setHidePwd(!hidePwd)}
                textInputWrapper={{}}
                showError={passwordError}
                errorMsg={passwordErrorTxt}
                onSubmitEditing={() => {
                  Validation();
                }}
              />
            </View>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('ForgotPassword');
              }}
              style={{
                alignItems: 'flex-end',
                marginTop: 15,
              }}
              activeOpacity={0.7}>
              <Text
                style={{
                  fontSize: 15,
                  color: BaseColor.primary,
                  fontWeight: '600',
                  textAlign: 'left',
                  fontFamily: FontFamily.regular,
                  textDecorationLine: 'underline',
                  // letterSpacing: 0.75,
                }}>
                {translate('loginForgot')}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={{marginTop: '15%'}}>
            <CButton
              style={styles.loginBtn}
              title={translate('signin')}
              onPress={() => {
                Validation();
              }}
              disable={btnDisable}
              loader={loader}
            />
          </View>
        </ScrollView>
      </View>
    </>
  );
};

export default Login;
