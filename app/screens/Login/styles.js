import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const {height: dHeight, width: dWidth} = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
  },
  mainContainer: {
    flex: 1,
    padding: 16,
    marginBottom: '2%',
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
    right: 12,
    top: 34,
  },
  mainInputStyle: {
    // flex: 1,
    justifyContent: 'center',
  },
  inputWrapper: {
    marginVertical: 7,
  },
  loginTextView: {
    marginTop: 40,
    marginBottom: 20,
    alignItems: 'center',
    fontFamily: FontFamily.default,
  },
  rememberText: {
    fontSize: 15,
    color: BaseColor.whiteColor,
    textAlign: 'left',
    marginStart: 8,
  },

  loginBtn: {width: '80%'},
  myQeridoo: {
    fontFamily: FontFamily.regular,
    color: BaseColor.whiteColor,
    fontSize: 38,
    lineHeight: 49,
    textAlign: 'right',
  },
  loginTxt: {
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    fontSize: 30,
    letterSpacing: 0.7,
    textAlign: 'center',
  },
});

export default styles;
