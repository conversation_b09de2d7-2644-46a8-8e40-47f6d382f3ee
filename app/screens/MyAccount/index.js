import React, {useRef, useState} from 'react';
import {View, Text, TouchableOpacity, Image} from 'react-native';
import BaseColor from '../../config/colors';
import CButton from '../../components/CButton';
import {useDispatch, useSelector} from 'react-redux';
import CInput from '../../components/CInput';
import {translate} from '../../lang/Translate';
import {
  checkFileVal,
  enableAnimateInEaseOut,
  sendErrorReport,
} from '../../utils/commonFunction';
import styles from './styles';
import {ScrollView} from 'react-native-gesture-handler';
import {CustomIcon} from '../../config/LoadIcons';
import SubHeader from '../../components/SubHeader';
import ImageCropPicker from 'react-native-image-crop-picker';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import RNFetchBlob from 'react-native-blob-util';
import Toast from 'react-native-simple-toast';
import authAction from '../../redux/reducers/auth/actions';

const ProfileScreen = ({navigation}) => {
  const dispatch = useDispatch();
  const {setUserData} = authAction;
  const userData = useSelector(state => state.auth.userData);
  const [userDetails, setUserDetails] = useState(userData);
  const [nameError, setNameError] = useState(false);
  const [passwordError, setPasswordError] = useState(false);
  const [mailError, setMailError] = useState(false);
  const [numError, setNumError] = useState(false);
  const [nameErrorTxt, setnameErrorTxt] = useState('');
  const [mailErrorTxt, setMailErrorTxt] = useState('');
  const [numErrorTxt, setNumErrorTxt] = useState('');

  const fNameRef = useRef();
  const passwordRef = useRef();
  const emailRef = useRef();
  const pNumRef = useRef();

  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);
  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);

  const Validation = () => {
    const numVal = /^[0-9]+$/;
    const emailVal =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    enableAnimateInEaseOut();

    if (userDetails.fullName === '') {
      allErrorFalse();
      setNameError(true);
      // setnameErrorTxt("Please enter FullName");
      setnameErrorTxt(translate('enterName'));
    } else if (userDetails.email === '') {
      allErrorFalse();
      setMailError(true);
      // setMailErrorTxt("Please enter email");
      setMailErrorTxt(translate('enterEmail'));
    } else if (!emailVal.test(String(userDetails.email))) {
      allErrorFalse();
      setMailError(true);
      // setMailErrorTxt("Please enter valid Email");
      setMailErrorTxt(translate('enterEmailvalid'));
    } else if (userDetails.phone === '') {
      allErrorFalse();
      setNumError(true);
      // setNumErrorTxt("Please enter Phone number");
      setNumErrorTxt(translate('enterPhone'));
    } else if (
      !numVal.test(String(userDetails.phone)) ||
      userDetails.phone.length < 6 ||
      userDetails.phone.length > 12
    ) {
      allErrorFalse();
      setNumError(true);
      // setNumErrorTxt("Please enter valid Phone number");
      setNumErrorTxt(translate('enterPhonevalid'));
    } else {
      allErrorFalse();
      submitProfile();
      setloader(true);
      setanim(true);
      setBackAnim(true);
    }
  };

  // Upload profile Image API..
  const uploadImage = async imag => {
    const url = BaseSetting.endpoints.uploadProfile;
    const imgFile = {user_profile: imag.imageBase64, type: 'image'};
    try {
      const res = await getApiData(url, 'POST', imgFile, '', true);
      if (res?.success) {
        dispatch(setUserData(res?.data));
        setUserDetails(res?.data);
      }
    } catch (err) {
      console.log('error------>>>>>', err);
    }
  };

  // User Profile Update..
  const submitProfile = async () => {
    const url = BaseSetting.endpoints.uploadProfile;
    const profileData = {
      full_name: userDetails?.full_name || '',
      email: userDetails?.email || '',
      phone: userDetails?.phone,
      phone_code: userDetails?.country_code,
      type: 'profile',
    };
    try {
      const res = await getApiData(url, 'POST', profileData, '', true);
      if (res?.success) {
        navigation.goBack();
        setloader(false);
        dispatch(setUserData(res?.data));
        setUserDetails(res?.data);
        Toast.show(res.message);
      }
    } catch (err) {
      console.log('error------>>>>>', err);
    }
  };

  const imagePicker = () => {
    ImageCropPicker.openPicker({
      width: 250,
      height: 250,
      cropping: true,
    })
      .then(img => {
        const fType = img?.mime || '';
        const isValidFile = checkFileVal(fType, img.size);
        if (isValidFile) {
          RNFetchBlob.fs
            .readFile(img.path, 'base64')
            .then(data => {
              const newstate = {
                isImage: img.path,
                imageBase64: `data:image/png;base64,${data}`,
              };
              uploadImage(newstate);
            })
            .catch(err => {
              sendErrorReport(err, 'image_fs');
            });
        }
      })
      .catch(() => {});
  };
  const allErrorFalse = () => {
    setNameError(false);
    setNumError(false);
    setPasswordError(false);
    setMailError(false);
  };

  return (
    <View>
      <SubHeader
        title={translate('myAccount')}
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <ScrollView style={{flexGrow: 1, paddingHorizontal: 20}}>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            alignSelf: 'center',
            width: 128,
            height: 128,
            borderRadius: 100,
            backgroundColor: '#D0D8D8',
          }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={imagePicker}
            style={{
              width: 40,
              height: 40,
              backgroundColor: BaseColor.primary,
              position: 'absolute',
              zIndex: 1,
              right: 10,
              bottom: -5,
              borderRadius: userDetails?.user_profile ? 15 : 30,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <CustomIcon
              name={userDetails?.user_profile ? 'pencil-2' : 'camera'}
              size={userDetails?.user_profile ? 20 : 24}
              color={BaseColor.whiteColor}
            />
          </TouchableOpacity>
          {userDetails?.user_profile && (
            <Image
              style={{width: '100%', height: '100%', borderRadius: 100}}
              source={{uri: userDetails?.user_profile}}
            />
          )}
        </View>
        <Text style={[styles.infoText]}>{translate('Details')}</Text>
        <View
          style={{
            borderBottomWidth: 1,
            borderColor: '#E8E8E8',
            marginBottom: 10,
          }}>
          <CInput
            ref={fNameRef}
            placeholder={translate('fullName')}
            value={userDetails.full_name}
            onChangeText={val => {
              setUserDetails({...userDetails, full_name: val});
            }}
            placeholderTextColor={BaseColor.textGrey}
            onSubmitEditing={() => {
              passwordRef.current.focus();
            }}
            textInputWrapper={{borderWidth: 0}}
            inputStyle={{color: '#486264'}}
            showError={nameError}
            errorMsg={nameErrorTxt}
            // editable={isEdit}
          />
        </View>
        <View
          style={{
            borderBottomWidth: 1,
            borderColor: '#E8E8E8',
            marginBottom: 10,
          }}>
          <CInput
            ref={emailRef}
            placeholder={translate('emailId')}
            value={userDetails.email}
            onChangeText={val => {
              setUserDetails({...userDetails, email: val});
            }}
            placeholderTextColor={BaseColor.textGrey}
            textInputWrapper={{
              borderWidth: 0,
            }}
            keyboardType="email-address"
            onSubmitEditing={() => {
              pNumRef.current.focus();
            }}
            showError={mailError}
            errorMsg={mailErrorTxt}
            editable={false}
            colorStyle={{color: '#B9C6C6'}}
          />
        </View>
        <View style={{borderBottomWidth: 1, borderColor: '#E8E8E8'}}>
          <CInput
            ref={pNumRef}
            // isSuffix
            phoneNumber
            suffixStyle={{
              backgroundColor: BaseColor.transparent,
            }}
            placeholder={translate('forgotInput')}
            value={userDetails.phone}
            onChangeText={val => {
              setUserDetails({...userDetails, phone: val});
            }}
            onSelect={val =>
              setUserDetails({
                ...userDetails,
                country_code: val.cca2,
                country: val.name,
                phone_code: `+${val.callingCode[0]}`,
              })
            }
            placeholderTextColor={BaseColor.textGrey}
            textInputWrapper={{
              borderWidth: 0,
            }}
            keyboardType="number-pad"
            onSubmitEditing={() => {
              Keyboard.dismiss();
            }}
            countryCode={userDetails.country_code}
            showError={numError}
            errorMsg={numErrorTxt}
            editable={false}
            colorStyle={{color: '#B9C6C6'}}
          />
        </View>
        <CButton
          style={{
            marginTop: '30%',
            width: '80%',
          }}
          title={translate('saveChanges')}
          playAnimation={anim}
          backAnim={backAnim}
          onPress={() => {
            Validation();
          }}
          loader={loader}
          done={done}
        />
      </ScrollView>
    </View>
  );
};

export default ProfileScreen;
