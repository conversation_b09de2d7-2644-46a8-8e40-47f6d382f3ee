import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const {height: dHeight, width: dWidth} = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.onBoardBgColor,
  },
  countryPickerStyle: {
    paddingLeft: 20,
    marginTop: '1%',
    borderColor: BaseColor.primary,
    borderWidth: 2,
    height: 60,
    borderRadius: 10,
    paddingVertical: 8,
    justifyContent: 'center',
    backgroundColor: BaseColor.whiteColor,
  },
  loginBtn: {
    alignSelf: 'center',
    fontFamily: FontFamily.default,
    height: 60,
    borderRadius: 40,
  },
  infoText: {
    color: BaseColor.blackColor,
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 20,
    paddingHorizontal: 15,
    fontFamily: FontFamily.bold,
  },
});
export default styles;
