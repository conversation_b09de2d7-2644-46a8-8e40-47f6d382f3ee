import React, {useCallback, useEffect, useState} from 'react';
import BaseColor from '../../config/colors';
import styles from '../Home/styles';
import {ScrollView} from 'react-native-gesture-handler';
import {
  ActivityIndicator,
  AppState,
  BackHandler,
  Dimensions,
  FlatList,
  Image,
  Platform,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {FontFamily} from '../../config/typography';
import {useDispatch, useSelector} from 'react-redux';
import BaseSetting from '../../config/setting';
import bluetoothActions from '../../redux/reducers/bluetooth/actions';
import {
  addAction,
  openInAppBrowser,
  sendErrorReport,
} from '../../utils/commonFunction';
import {useFocusEffect} from '@react-navigation/native';
import Orientation from 'react-native-orientation-locker';
import {CustomIcon} from '../../config/LoadIcons';
import {getApiData} from '../../utils/apiHelper';
import {findIndex, flattenDeep, isArray, isEmpty, isObject, find} from 'lodash';
import AddIcon from '../../assets/AddIcon/AddIcon.svg';
import Divider from '../../components/Divider/Divider';
import {translate} from '../../lang/Translate';
import CustomHeader from '../../components/CustomHeader/CustomHeader';

function MyDevice({navigation}) {
  const [deviceList, setDeviceList] = useState([]);
  const [devices, setDevices] = useState([
    {
      type: 'add',
    },
  ]);
  const [appState, setAppState] = useState(true);
  const token = useSelector(state => state.auth.accessToken);
  const dispatch = useDispatch();
  const {
    setDeviceID,
    setConnectedDeviceDetail,
    setLastDeviceId,
    setActiveDeviceId,
    setSwiperKey,
    setActiveChildDetail,
    setDeviceDetail,
    setConnectedDeviceDetails,
    setIsConnectLoad,
    setHomeDeviceClick,
    setLeftChildAlert,
    setClickAddQr,
    setSkipShow,
  } = bluetoothActions;

  const {
    connectedDeviceDetail,
    isBleConnected,
    lastDeviceId,
    emergencyAlert,
    isLeftChildAlert,
    isConnecting,
    deviceDetail,
    qrWifiSSID,
    path,
    isConnectedNet,
    aviFiles,
  } = useSelector(state => state.bluetooth);

  //----Back btn

  function handleBackButtonClick() {
    navigation.reset({
      index: 0,
      routes: [{name: translate('setting')}],
    });
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  // ------App state
  useEffect(() => {
    console.log('App ddddd');
    const handleAppStateChange = nextAppState => {
      console.log(`App State: ${nextAppState}`);
      if (nextAppState === 'active') {
        console.log('App State======', true);
        // sendErrorReport(true, "app_state_active");
        setAppState(true);
      } else if (nextAppState === 'background') {
        console.log('App State======', false);
        // sendErrorReport(false, "app_state_background");
        setAppState(false);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => subscription.remove();
  }, []);

  //function for get device list
  async function getDeviceList() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    const newObj = {
      type: 'add',
    };

    const data = {
      platform: Platform.OS,
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.connectedDevice,
        'POST',
        data,
        '',
        true,
      );
      if (response.success && isArray(response.data)) {
        dispatch(setConnectedDeviceDetails(flattenDeep(response.data)));
        dispatch(setConnectedDeviceDetail(flattenDeep(response.data)));
        const arr = response.data;
        arr.unshift(newObj);
        setDeviceList(arr);
        console.log('cdevice list---step1', JSON.stringify(arr, null, 2));
      } else {
        dispatch(setConnectedDeviceDetails([]));
        dispatch(setConnectedDeviceDetail([]));
        setDeviceList([newObj]);
        Toast.show(response.message);
      }
    } catch (error) {
      setDeviceList([newObj]);
      if (isBleConnected) {
        const obj = {device_i: ''};
        setDeviceList([{type: 'add'}, obj]);
      }
      dispatch(setConnectedDeviceDetails([]));
      dispatch(setConnectedDeviceDetail([]));
      console.log('error for device list ===', error);
      sendErrorReport(error, 'get_device_list');
    }
  }

  useFocusEffect(
    useCallback(() => {
      Orientation.lockToPortrait();
      if (token !== '') {
        // getPost("feed_post");
        // getFeedPost();
        getDeviceList();
        // getBadgeCount();
        // getChildInfo();
        // handleStopScan();
      }
    }, []),
  );

  useEffect(() => {
    getDeviceList();
  }, [isBleConnected, isConnecting, deviceDetail]);

  // this function for disconnect device if connected
  async function connectDevice(item) {
    console.log(
      '----->>>>>>>>>DEVICE CONNECT FOR DISCONNECT DEVICE<<<<<<<------',
      item,
    );
    sendErrorReport(item, 'Home_click_item');
    if (item?.connected === 1) {
      setTimeout(() => {
        dispatch(setActiveDeviceId(item));
        dispatch(setSwiperKey(item?.product_id));
        navigation.navigate(translate('dashboard'));
      }, 1000);
    } else {
      console.log(
        'db connected else ----------',
        JSON.stringify(item, null, 2),
      );
      try {
        dispatch(setHomeDeviceClick(true));
        dispatch(setIsConnectLoad(true));
        // dispatch(setDeviceID(""));
        dispatch(setDeviceID(item.product_id));
        dispatch(setLastDeviceId(item.product_id));
        // dispatch(setChildProductId({ product_id: item.product_id, child_id: item.child_id }));
        setTimeout(() => {
          const d = devices.findIndex(i => {
            const f = findIndex(
              i.deviceDetails,
              m => m.product_id === item.product_id,
            );
            return f > -1;
          });
          if (d > -1) {
            dispatch(setActiveChildDetail(devices[d]));
          }
          sendErrorReport(devices[d], 'devices[d]');
          // dispatch(setDeviceID(item.product_id));
          // dispatch(setLastDeviceId(item.product_id));

          const arr = [...connectedDeviceDetail] || [];
          const index = findIndex(
            arr,
            lt => lt?.product_id === item?.product_id,
          );
          if (index > -1) {
            arr[index] = item;
            sendErrorReport(item, 'item-----');
            // dispatch(setActiveDeviceId(item));
          } else {
            arr.push(item);
          }
          dispatch(setConnectedDeviceDetail(arr));
          dispatch(setConnectedDeviceDetails(arr));
          dispatch(setActiveDeviceId(item));
          dispatch(setSwiperKey(item?.product_id));
          // getAutoConnect(item?.product_id, item?.child_id);
          navigation.navigate(translate('dashboard'));
        }, 1000);
      } catch (error) {
        sendErrorReport(error, 'home_product_Error');
      }
    }
  }

  //---------------------------------CHILD-COMPONENT-DEVICE-LIST-FLATLIST------------------------------//
  const renderDevice = ({item}) => (
    <View
      style={[
        // styles.deviceCard,
        {
          backgroundColor: BaseColor.whiteColor,
          borderStyle: item.type === 'add' ? 'dotted' : 'solid',
          height: 87,
          width: 87,
          overflow: 'hidden',
          borderRadius: 16,
          marginEnd: 12,
          justifyContent: 'center',
          alignItems: 'center',
          borderColor: BaseColor.whiteColor,
          borderWidth: 2,
          borderStyle: 'solid',
        },
      ]}
      activeOpacity={0.7}
      onPress={() => {
        setAlertModal(true);
      }}>
      {item.type === 'add' ? (
        <TouchableOpacity
          style={{
            justifyContent: 'center',
            alignItems: 'center',
          }}
          activeOpacity={0.7}
          onPress={() => {
            dispatch(setClickAddQr(true));
            dispatch(setSkipShow(false));
            // handleStopScan();
            navigation.navigate('QRScanner');
          }}>
          <CustomIcon
            name="add-device"
            size={44}
            color={BaseColor.blackColor}
          />
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          style={[
            {...styles.deviceCard, marginEnd: 0, borderWidth: 0},
            item?.connected === 1 && isBleConnected
              ? {borderColor: BaseColor.primary}
              : {borderColor: BaseColor.blackColor},
          ]}
          activeOpacity={0.7}
          onPress={() => {
            // setAlertModal(true);
            connectDevice(item);
            // setIsSelect(item?.product_id);
          }}>
          <View
            style={{
              borderWidth: 1,
              borderColor: BaseColor.primary,
              padding: 5,
              borderRadius: 16,
            }}>
            <Image
              source={
                item?.device_image
                  ? {uri: item?.device_image}
                  : require('../../assets/images/qeridoo_img.png')
              }
              style={{
                height: 40,
                width: 40,
              }}
              resizeMode="cover"
            />
          </View>
          {/* {item?.nick_name && (
                <View
                  style={{
                    position: "absolute",
                    flex: 1,
                    flexDirection: "row",
                    alignItems: "center",
                    bottom: 7,
                    left: 5,
                    backgroundColor: "#fff",
                    borderRadius: 5,
                  }}
                >
                  <Image
                    source={
                      item?.child_profile
                        ? { uri: item?.child_profile }
                        : require("../../assets/images/logo.png")
                    }
                    style={[
                      styles.childImgStyle,
                      { borderColor: BaseColor.black60 },
                    ]}
                  />
                  <Text
                    style={[styles.childNameStyle, { color: BaseColor.blackColor }]}
                    numberOfLines={1}
                  >
                    {item?.nick_name}
                  </Text>
                </View>
              )} */}
          {emergencyAlert && item?.last_disconnected === 1 ? (
            <View
              style={[
                {
                  position: 'absolute',
                  height: 28,
                  width: 28,
                  borderRadius: 18,
                  // opacity: 1,
                  bottom: 3,
                  right: 3,
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <CustomIcon name="warning" color={BaseColor.alertRed} size={18} />
            </View>
          ) : isConnecting ? (
            <View
              style={[
                {
                  position: 'absolute',
                  height: 16,
                  width: 16,
                  borderRadius: 18,
                  top: 2,
                  right: 2,
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <ActivityIndicator color={BaseColor.org} />
            </View>
          ) : item?.connected === 1 && isBleConnected ? (
            <View
              style={[
                {
                  position: 'absolute',
                  height: 16,
                  width: 16,
                  borderRadius: 18,
                  top: 2,
                  right: 2,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: BaseColor.primary,
                  borderWidth: 2,
                  borderColor: BaseColor.whiteColor,
                },
              ]}
            />
          ) : (
            <View
              style={[
                {
                  position: 'absolute',
                  height: 16,
                  width: 16,
                  borderRadius: 18,
                  top: 2,
                  right: 2,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: BaseColor.darkGrey,
                  borderWidth: 2,
                  borderColor: BaseColor.whiteColor,
                },
              ]}
            />
          )}
        </TouchableOpacity>
      )}
    </View>
  );

  //-------------------------------------------PARENT-COMPONENT----------------------------------------//
  return (
    <>
      <View>
        <CustomHeader
          leftIconName="left-arrow"
          onLeftPress={() => {
            navigation.goBack();
          }}
          backBtn
          rightIconName="bell-thick"
          onRightPress={() => {
            navigation.navigate('Alerts');
          }}
        />
      </View>
      <ScrollView
        contentContainerStyle={[
          {
            flexGrow: 1,
          },
        ]}
        bounces={false}
        showsVerticalScrollIndicator={false}>
        <View>
          <Text
            style={{
              fontFamily: FontFamily.regular,
              color: BaseColor.blackColor,
              fontSize: 17,
              marginHorizontal: 22,
              // marginTop: 20,
            }}>
            My Devices
          </Text>
          <View style={{height: '100%'}}>
            {deviceList.length ? (
              <>
                <FlatList
                  data={deviceList}
                  renderItem={renderDevice}
                  horizontal
                  contentContainerStyle={{paddingStart: 5}}
                  showsHorizontalScrollIndicator={false}
                />
                <Divider
                  width={Dimensions.get('window').width - 45}
                  backgroundColor={'rgba(0, 0, 0, 0.10)'}
                  divStyle={{marginHorizontal: 20, marginTop: 20}}
                />
              </>
            ) : (
              <View
                style={{
                  flex: 1,
                  height: '100%',
                  justifyContent: 'center',
                }}>
                <ActivityIndicator
                  size={'large'}
                  color={BaseColor.primary}
                  animating
                />
              </View>
            )}
          </View>
        </View>
      </ScrollView>
    </>
  );
}

export default MyDevice;
