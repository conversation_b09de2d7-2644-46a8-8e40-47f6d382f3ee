/* eslint-disable global-require */
/* eslint-disable quotes */
import {useTheme} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  Platform,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useSelector} from 'react-redux';
import {isArray} from 'lodash';
import CHeader from '../../components/CHeader';
import {translate} from '../../lang/Translate';
import styles from './styles';
import {enableAnimateInEaseOut} from '../../utils/commonFunction';
import {CustomIcon} from '../../config/LoadIcons';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import CustomHeader from '../../components/CustomHeader/CustomHeader';

/**
 *
 *@module Product
 *
 */
export default function MyQRcode({navigation}) {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const [pageLoad, setPageLoad] = useState(true);
  const [deviceList, setDeviceList] = useState([]);

  async function getDeviceList() {
    const data = {
      platform: Platform.OS,
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.connectedDevice,
        'POST',
        data,
        '',
        true,
      );
      // if (response.success && isArray(response.data)) {
      // const arr = response.data;
      // setDeviceList(arr);
      // } else {
      setDeviceList([]);
      // }
    } catch (error) {
      setDeviceList([]);
      console.log('error ', error);
    }
  }

  useEffect(() => {
    getDeviceList();
  }, []);

  const renderProducts = ({item}) => (
    <TouchableOpacity
      style={[styles.cardRoot, {backgroundColor: BaseColor.lightgray}]}
      onPress={() => {
        navigation.navigate('QRCodeDetail', {QRCodeDetail: item});
      }}>
      <View
        style={{
          alignSelf: 'flex-end',
          padding: 10,
          backgroundColor: BaseColor.whiteColor,
          borderRadius: 20,
        }}>
        <CustomIcon name="qrcode" size={22} color={BaseColor.org} />
      </View>
      <View style={styles.rowStyle}>
        <View>
          <Image
            source={
              item?.device_image
                ? {uri: item?.device_image}
                : require('../../assets/images/logo.png')
            }
            style={[styles.imgStyle, {borderColor: BaseColor.black60}]}
          />
          {item?.nick_name && (
            <View
              style={{
                position: 'absolute',
                flex: 1,
                flexDirection: 'row',
                alignItems: 'center',
                bottom: 10,
                left: Platform.OS === 'ios' ? 70 : 80,
                right: Platform.OS === 'ios' ? 70 : 80,
                backgroundColor: '#fff',
                borderRadius: 12,
              }}>
              <Image
                source={
                  item.child_profile
                    ? {uri: item.child_profile}
                    : require('../../assets/images/logo.png')
                }
                style={[styles.childImgStyle, {borderColor: BaseColor.black60}]}
              />
              <Text
                style={[styles.childNameStyle, {color: BaseColor.blackColor}]}
                numberOfLines={2}>
                {item.nick_name}
              </Text>
            </View>
          )}
        </View>
        <View style={{flex: 1}}>
          <Text
            style={[
              styles.nameStyle,
              {color: BaseColor.blackColor, paddingTop: 5},
            ]}>
            {/* {item.device_name} */}
            {item?.device_ssid || item.device_name}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  enableAnimateInEaseOut();

  return (
    <View style={[styles.root, {backgroundColor: BaseColor.whiteColor}]}>
      <View style={styles.root}>
        <CustomHeader
          title={translate('MyQRcode')}
          leftIconName="left-arrow"
          onLeftPress={() => {
            navigation.goBack();
          }}
          backBtn
          rightIconName="bell-thick"
          onRightPress={() => {
            navigation.navigate('Alerts');
          }}
        />

        <FlatList
          keyExtractor={(item, index) => index}
          data={deviceList}
          renderItem={renderProducts}
          contentContainerStyle={{flexGrow: 1, paddingBottom: 24}}
          onEndReachedThreshold={0.4}
          ListEmptyComponent={() => (
            <View style={styles.emptyComponent}>
              {pageLoad ? (
                <ActivityIndicator color={BaseColor.whiteColor} />
              ) : (
                <Text
                  style={{
                    fontSize: 16,
                    color: BaseColor.whiteColor,
                    textAlign: 'center',
                  }}>
                  {translate('noProducts')}
                </Text>
              )}
            </View>
          )}
        />
      </View>
    </View>
  );
}
