import React, { useEffect, useState } from "react";
import { Al<PERSON>, Modal, Text, View } from "react-native";
import NetInfo from "@react-native-community/netinfo";
import { useDispatch, useSelector } from "react-redux";
import { useTheme } from "@react-navigation/native";
import WifiManager from "react-native-wifi-reborn";
import { FontFamily } from "../../config/typography";
import socketActions from "../../redux/reducers/socket/actions";
import BluetoothActions from "../../redux/reducers/bluetooth/actions";

let socketCnt = 1;
let socketTimer = null;

export default function NetworkModal() {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const darkmode = useSelector((state) => state.auth.darkmode);
  const accessToken = useSelector((uState) => uState.auth.accessToken);
  const socketIo = useSelector((uState) => uState.socket.IOSocket);
  const { qrWifiSSID } = useSelector((state) => state.bluetooth);
  const [connected, setConnected] = useState(true);
  const dispatch = useDispatch();

  const { initilization } = socketActions;

  useEffect(() => {
    NetInfo.addEventListener((state) => {
      handleConnectivity(state);
    });
  }, []);

  //  Socket connection logic
  useEffect(() => {
    if (!socketIo && accessToken && socketCnt <= 5) {
      socketTimer = setInterval(() => {
        if (socketCnt <= 5) {
          dispatch(initilization());
          socketCnt += 1;
        } else {
          clearInterval(socketTimer);
          socketCnt = 1;
        }
      }, 2000);
    } else {
      socketCnt = 1;
      clearInterval(socketTimer);
    }
    return () => {
      socketCnt = 1;
      clearInterval(socketTimer);
    };
  }, [socketIo, accessToken]);

  const handleConnectivity = (state) => {
    console.log("handleConnectivity -> state", state);
    dispatch(BluetoothActions.setIsConnectedNet(state.isConnected));
    if (state.isConnected) {
      setConnected(true);
      WifiManager.getCurrentWifiSSID().then(
        (ssid) => {
          console.log(`Your current connected wifi SSID is ${ssid}`);
          if (ssid === qrWifiSSID) {
            setTimeout(() => {
              dispatch(BluetoothActions.setIsBleConnected(true));
            }, 5000);
          } else {
            dispatch(BluetoothActions.setIsBleConnected(false));
          }
        },
        () => {
          console.log("Cannot get current SSID!");
        }
      );
      if (!socketIo && accessToken) {
        setTimeout(() => {
          dispatch(initilization());
        }, 1000);
      }
    } else {
      dispatch(BluetoothActions.setIsBleConnected(false));
      setConnected(false);
    }
  };

  return (
    <>
      {/* <Modal
        style={{
          justifyContent: "center",
          alignItems: "center",
          flex: 1,
        }}
        transparent
        visible={!connected}>
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: BaseColor.black40,
          }}>
          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              padding: 50,
              borderRadius: 16,
            }}>
            <Text
              style={{
                fontFamily: FontFamily.default,
                fontWeight: "bold",
                color: darkmode ? "white" : "black",
              }}>
              NO INTERNET
            </Text>
          </View>
        </View>
      </Modal> */}
    </>
  );
}
