/* eslint-disable quotes */
/* eslint-disable no-unused-expressions */
import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  BackHandler,
  ActivityIndicator,
  ScrollView,
  KeyboardAvoidingView,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import FIcon from 'react-native-vector-icons/Feather';
import Toast from 'react-native-simple-toast';
import {useDispatch, useSelector} from 'react-redux';
import {useTheme} from '@react-navigation/native';
import styles from './styles';
import CButton from '../../components/CButton';
import OtpComponent from '../../components/OtpComponent/index';
import {CustomIcon} from '../../config/LoadIcons';
import GradientBack from '../../components/gradientBack';
import {translate} from '../../lang/Translate';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import AuthAction from '../../redux/reducers/auth/actions';
import {sendErrorReport} from '../../utils/commonFunction';
import BaseColors from '../../config/colors';
import {FontFamily} from '../../config/typography';
import CustomHeader from '../../components/CustomHeader/CustomHeader';
import CInput from '../../components/CInput';

let backPressed = 0;

/**
 *
 *@module OTP
 *
 */
function Otp({route, navigation}) {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {setUserId, setAccessToken, setUserData} = AuthAction;
  const dispatch = useDispatch();

  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);
  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);

  const [numPad, setNumPad] = useState(false);
  const [code, setCode] = useState('');
  const {type, userId} = route.params;
  const [otpLoader, setOtpLoader] = useState(false);
  const [timeInerval, setTimeInterval] = useState(60); // Set time interval for resend Otp
  const [btnDisable, setBtnDisable] = useState(true); // this for Varify button disable
  const user_id = useSelector(state => state.auth.user_id);
  const passwordRef = useRef();

  const validation = () => {
    if (code == '') {
      // Toast.show("Enter One Time Password");
      Toast.show(translate('enterOTP'));
    } else {
      otpCheck();
    }
  };

  /** this function for OTP
   * @function otpCheck
   * @param {object} data user_id, otp_code
   */
  const otpCheck = () => {
    let updateCode = '';
    setloader(true);
    setanim(true);
    setBackAnim(false);
    if (code) {
      updateCode = String(code).replace(/-/g, '');
    }
    const data = {
      user_id: userId || user_id,
      otp_code: Number(updateCode),
    };

    getApiData(BaseSetting.endpoints.otp, 'POST', data)
      .then(response => {
        if (response.success) {
          if (type !== 'ForgotPassword') {
            dispatch(setUserData(response.data.user));
            dispatch(setUserId(response.data.user.id));
            dispatch(setAccessToken(response.data.token));
          }
          setTimeout(() => {
            setloader(false);
            setdone(true);
          }, 2000);
          setTimeout(() => {
            if (type === 'ForgotPassword') {
              navigation.push('UpdatePassword');
            } else if (type === 'Signup') {
              navigation.push('RegisterSucess');
            } else if (type === 'LoginInactive') {
              navigation.push('Login');
            }
          }, 3000);
        } else {
          Toast.show(response.message);
          setanim(false);
          setBackAnim(true);
          setdone(false);
          setloader(false);
        }
      })
      .catch(err => {
        Toast.show('Something went wrong while verifying otp');
        sendErrorReport(err, 'verify_otp');
        setanim(false);
        setBackAnim(true);
        setdone(false);
        setloader(false);
        console.log('ERRR', err);
      });
  };

  // this function for resend otp
  /** this function for resend otp
   * @function resendOtp
   * @param {object} data user_id
   */
  async function resendOtp() {
    setTimeInterval(60);
    try {
      const response = await getApiData(BaseSetting.endpoints.sendOtp, 'POST', {
        user_id: userId || user_id,
        brand_name: 'Qeridoo',
      });
      Toast.show(response.message);
    } catch (error) {
      console.log('resend otp error ===', error);
      sendErrorReport(error, 'resend_otp');
    }
  }

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      if (timeInerval > 0) setTimeInterval(p => p - 1);
      6 - 4 - 2009;
    }, 1000);
    return () => {
      clearInterval(interval);
    };
  }, [timeInerval]);

  return (
    <>
      <View style={styles.root}>
        <View style={{marginTop: '5%'}}>
          <CustomHeader
            transparentView
            leftIconName="left-arrow"
            borderCircular
            backgroundColor={'#F1F5F9'}
            onLeftPress={() => {
              navigation.navigate('RedirectLS');
            }}
            backBtn
          />
        </View>
        <ScrollView
          contentContainerStyle={{flexGrow: 1, padding: 16}}
          showsVerticalScrollIndicator={false}
          bounces={false}>
          <View>
            <Text
              style={{
                fontFamily: FontFamily.bold,
                color: BaseColor.blackColor,
                fontSize: 30,
                textAlign: 'center',
              }}>
              {translate('verifyAccount')}
            </Text>
            <Text style={{textAlign: 'center', marginTop: 10}}>
              <Text
                style={{
                  fontFamily: FontFamily.regular,
                  color: BaseColor.primary,
                  fontSize: 14,
                  textAlign: 'center',
                  marginTop: 10,
                  lineHeight: 20,
                }}>
                {translate('codeHasBeenSend')}
              </Text>
              <Text
                style={{
                  fontFamily: FontFamily.regular,
                  color: BaseColor.primary,
                  fontSize: 14,
                  textAlign: 'center',
                }}>
                {translate('otpText')}
              </Text>
            </Text>
          </View>
          <View style={{marginTop: '20%'}}>
            <CInput
              title={translate('enterCode')}
              placeholder={translate('4DigtiCode')}
              value={code}
              onChangeText={val => {
                const numericText = val.replace(/[^\d]/g, '');
                let formattedText = '';
                for (let i = 0; i < numericText.length; i++) {
                  if (i !== 0) {
                    formattedText += '-';
                  }
                  formattedText += numericText[i];
                }
                if (val) {
                  setBtnDisable(false);
                } else {
                  setBtnDisable(true);
                }
                setCode(formattedText);
              }}
              inputStyle={{fontFamily: code && FontFamily.bold}}
              maxLength={7}
              keyboardType="numeric"
              placeholderTextColor={BaseColors.textGrey}
              onSubmitEditing={() => {
                passwordRef.current.focus();
              }}
            />
          </View>
          <View style={{marginTop: '15%'}}>
            <CButton
              style={styles.loginBtn}
              titleStyle={{}}
              title={translate('verifyAccount')}
              playAnimation={anim}
              backAnim={backAnim}
              disable={btnDisable}
              onPress={() => {
                validation();
              }}
              loader={loader}
              done={done}
            />
          </View>
          <View
            style={{
              flexDirection: 'row',
              marginTop: '5%',
              justifyContent: 'center',
              marginTop: '20%',
            }}>
            <Text
              style={{
                color: BaseColors.textGrey,
                fontSize: 14,
                fontFamily: FontFamily.regular,
              }}>
              {translate('didNotReceiveCode')}
            </Text>
            <TouchableOpacity
              disabled={timeInerval !== 0}
              activeOpacity={0.7}
              onPress={() => resendOtp()}>
              <Text
                style={{
                  color: BaseColors.primary,
                  fontSize: 14,
                  fontFamily: FontFamily.regular,
                  textDecorationLine: 'underline',
                  marginLeft: 5,
                }}>
                {translate('resend')} OTP
              </Text>
            </TouchableOpacity>
          </View>
          <View style={{marginTop: 10}}>
            <Text
              style={{
                textAlign: 'center',
                color: BaseColors.textGrey,
                fontSize: 14,
                fontFamily: FontFamily.regular,
              }}>
              {`Resend code in 00:${
                String(timeInerval).length === 1
                  ? `0${timeInerval}`
                  : timeInerval
              }`}
            </Text>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

export default Otp;
