import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const {height: dHeight, width: dWidth} = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
  },
  mainContainer: {
    flex: 1,
    padding: 16,
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: 'flex-end',
  },
  loginBtn: {width: '80%'},
  mainInputStyle: {
    // flex: 1,
    justifyContent: 'center',
  },
  inputWrapper: {
    marginVertical: 7,
    marginHorizontal: 40,
  },
  loginText: {
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    fontSize: 30,
    // lineHeight: 26,
    textAlign: 'center',
  },
  loginTextView: {
    marginTop: 40,
    marginBottom: 20,
    alignItems: 'center',
    fontFamily: FontFamily.default,
  },
  associatedTest: {
    fontFamily: FontFamily.default,
    color: BaseColor.whiteColor,
    fontSize: 16,
    textAlign: 'center',
    marginHorizontal: 42,
  },
  lockIconStyle: {
    backgroundColor: BaseColor.onBoardBgColor,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    height: 80,
    borderRadius: 40,
    elevation: 5,
    marginBottom: 20,
  },
  resendView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: 100,
    alignSelf: 'center',
    marginVertical: 20,
  },
  resendText: {
    fontSize: 16,
    fontFamily: FontFamily.regular,
    color: BaseColor.whiteColor,
    fontWeight: 'bold',
    letterSpacing: 0.7,
    paddingHorizontal: 8,
  },
  numPad: {
    flex: 1,
    // justifyContent: 'center',
    alignItems: 'center',
    height: 60,
    marginVertical: 5,
  },
  numTxtStyle: {
    color: BaseColor.blackColor,
    fontFamily: FontFamily.default,
    fontSize: 24,
  },
  closeBtn1: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
    right: 12,
    top: 34,
  },
  BackBtn: {
    height: 40,
    width: 40,

    alignSelf: 'flex-end',
    position: 'absolute',
    left: 12,
    top: 34,
  },
});

export default styles;
