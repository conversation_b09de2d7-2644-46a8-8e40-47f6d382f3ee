/* eslint-disable quotes */
import {useTheme} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  ImageBackground,
  Modal,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Toast from 'react-native-simple-toast';
import {flattenDeep, isEmpty} from 'lodash';
import CButton from '../../components/CButton';
import DropDown from '../../components/DropDown';
import BaseSetting from '../../config/setting';
import {FontFamily} from '../../config/typography';
import {translate} from '../../lang/Translate';
import {getApiData} from '../../utils/apiHelper';
import styles from './styles';
import {sendErrorReport} from '../../utils/commonFunction';
import SubHeader from '../../components/SubHeader';

/**
 *
 *@module ProductCatalouge
 *
 */
export default function ProductCatalouge({navigation}) {
  const {width, height} = Dimensions.get('window');
  const colors = useTheme();
  const BaseColor = colors.colors;
  const [filterModal, setfilterModal] = useState(false);
  const [selectedCat, setselectedCat] = useState('');
  const [selectedFilter, setselectedFilter] = useState('');
  const [selectedChara, setselectedChara] = useState('');
  const [filterData, setFilterData] = useState('');
  const [sortingObj, setSortingObj] = useState({});
  const [refreshing, setRefreshing] = useState(false);
  const [pageLoad, setPageLoad] = useState(true);
  const [page, setPage] = useState(1);
  const [nextPage, setNextPage] = useState(false);
  const [nextLoading, setNextLoading] = useState(false);
  const [ecomCatalougeList, setEcomCatalougeList] = useState([]);
  const [relInfo, setRel] = useState('');
  const [pageInfo, setPageInfo] = useState('');
  const [filterListF, setFilterList] = useState([]);

  const characterstics = [
    {id: 1, name: 'Alphabetically - A Z'},
    {id: 2, name: 'Alphabeticall - Z A'},
    {id: 3, name: 'Date - Old to recent'},
    {id: 4, name: 'Date - Recent to old'},
  ];
  useEffect(() => {
    getEComCatalougeList();
  }, []);

  /** this function for get Product List
   * @function getEComProductList
   * @param {object} data token, per_page, page, category_id
   */

  async function getEComCatalougeList() {
    setPageLoad(true);
    try {
      const response = await getApiData(
        BaseSetting.endpoints.ecomCatalougeList,
        'GET',
      );
      if (response?.success) {
        if (!isEmpty(response.data)) {
          const tempPArr = flattenDeep([ecomCatalougeList, response.data]);
          setEcomCatalougeList(tempPArr);
        }
      } else {
        setEcomCatalougeList([]);
        Toast.show(response.message);
      }
      setPageLoad(false);
    } catch (error) {
      setPageLoad(false);
      Toast.show('Something went wrong while getting ECOM Catalouge list');
      sendErrorReport(err, 'get_product_list');
    }
  }

  // this function is used when list data reached to limit while scrolling
  const onEndReached = () => {
    setNextLoading(true);
    if (nextPage) {
      const tempPage = page + 1;
      setPage(tempPage);
      getEComCatalougeList();
    }
  };

  // this function is used when list is refresh from top
  const onRefresh = React.useCallback(() => {
    setRel('');
    setPageInfo('');
    getEComCatalougeList();
  }, []);

  const renderProducts = ({item, index}) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        style={[
          {
            padding: 10,
          },
        ]}
        onPress={() => {
          navigation.navigate('Products', {catalouge: item});
        }}>
        <ImageBackground
          style={{
            resizeMode: 'cover',
            width: Dimensions.get('window').width / 2.3,
            height: Dimensions.get('window').width / 3,
            backgroundColor: 'rgba(0,0,0,.6)',
          }}
          source={
            item?.image
              ? {uri: item?.image.src}
              : require('../../assets/images/logo.png')
          }>
          <View
            style={{
              backgroundColor: 'rgba(0,0,0,.5)',
              width: '100%',
              height: '100%',
            }}>
            <Text
              style={[
                styles.nameStyle,
                {
                  color: BaseColor.whiteColor,
                  position: 'absolute',
                  left: 5,
                  bottom: 10,
                },
              ]}>
              {item?.title}
            </Text>
          </View>
        </ImageBackground>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.root, {backgroundColor: BaseColor.whiteColor}]}>
      <SubHeader
        title={translate('Catalogue')}
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      {pageLoad ? (
        <View style={{justifyContent: 'center', flex: 1}}>
          <ActivityIndicator color={BaseColor.primary} />
        </View>
      ) : (
        <View style={{marginHorizontal: 5}}>
          <FlatList
            keyExtractor={(item, index) => index}
            numColumns={2}
            data={ecomCatalougeList}
            renderItem={renderProducts}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            maxHeight={height / 1.2}
            onEndReachedThreshold={0.4}
            onEndReached={onEndReached}
          />
        </View>
      )}
      <Modal
        style={{flex: 1}}
        visible={filterModal}
        transparent
        animationType="slide"
        onRequestClose={() => {
          setfilterModal(false);
        }}>
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: BaseColor.black40,
            justifyContent: 'flex-end',
          }}
          onPress={() => {
            setfilterModal(false);
          }}>
          <View
            style={{
              backgroundColor: BaseColor.blueDark,
              padding: 24,
              borderTopEndRadius: 16,
              borderTopStartRadius: 16,
            }}>
            <Text
              style={{
                color: BaseColor.whiteColor,
                fontSize: 16,
                marginVertical: 8,
                fontFamily: FontFamily.default,
                fontWeight: 'bold',
              }}>
              FILTER
            </Text>
            <DropDown
              placeholder="Sorting"
              data={characterstics}
              style={{borderRadius: 12, marginEnd: 4}}
              valueProp="name"
              onSelect={val => {
                setselectedChara(val);
                let obj = {};
                if (val.name === 'Alphabetically - A Z') {
                  obj = {sort_value: 'title', sort_type: 'ascend'};
                } else if (val.name === 'Alphabeticall - Z A') {
                  obj = {sort_value: 'title', sort_type: 'desc'};
                } else if (val.name === 'Date - Old to recent') {
                  obj = {
                    sort_value: 'created_at',
                    sort_type: 'ascend',
                  };
                } else if (val.name === 'Date - Recent to old') {
                  obj = {sort_value: 'created_at', sort_type: 'desc'};
                }
                setSortingObj(obj);
                console.log('---------filter---val--', obj, sortingObj);
              }}
              selectedObject={selectedChara}
            />
            <View style={{marginTop: 16}}>
              <DropDown
                placeholder="Product Type"
                data={filterListF}
                style={{borderRadius: 12, marginEnd: 4}}
                valueProp="product_type"
                onSelect={val => {
                  setPageInfo('');
                  setselectedFilter(val);
                  setFilterData(val.product_type);
                }}
                selectedObject={selectedFilter}
              />
            </View>
            <View style={{flexDirection: 'row', marginBottom: 16}}>
              <CButton
                title="SEARCH"
                style={{
                  backgroundColor: BaseColor.whiteColor,
                  borderRadius: 8,
                  marginTop: 16,
                  marginEnd: 4,
                  flex: 1,
                }}
                titleStyle={{
                  color: BaseColor.blackColor,
                  fontWeight: 'bold',
                }}
                onPress={() => {
                  setfilterModal(false);
                  setEcomCatalougeList([]);
                  setPage(1);
                  setPageLoad(true);
                  getEComCatalougeList('filter');
                }}
              />
              <CButton
                title="RESET"
                style={{
                  backgroundColor: BaseColor.orange,
                  borderRadius: 8,
                  marginTop: 16,
                  marginStart: 4,
                  flex: 1,
                }}
                titleStyle={{
                  color: BaseColor.whiteColor,
                  fontWeight: 'bold',
                }}
                onPress={() => {
                  setselectedCat({});
                  setPage(1);
                  setRel('');
                  setPageInfo('');
                  getEComCatalougeList('reset');
                }}
              />
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}
