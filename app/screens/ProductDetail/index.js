/* eslint-disable quotes */
import React, {useEffect, useState} from 'react';
import {
  BackHandler,
  Dimensions,
  FlatList,
  Image,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import {useTheme} from '@react-navigation/native';
import Toast from 'react-native-simple-toast';
import {useSelector} from 'react-redux';
import Share from 'react-native-share';
import HTML from 'react-native-render-html';
import {translate} from '../../lang/Translate';
import styles from './styles';
import {openInAppBrowser, sendErrorReport} from '../../utils/commonFunction';
import {CustomIcon} from '../../config/LoadIcons';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import {isEmpty} from 'lodash';
import SubHeader from '../../components/SubHeader';

/**
 *
 *@module ProductDetail
 *
 */
export default function ProductDetail({navigation, route}) {
  const token = useSelector(state => state.auth.accessToken);
  const productDetails = route?.params?.productDetail;
  const collectionTitle = route?.params?.collectionTitle;
  const colors = useTheme();
  const BaseColor = colors.colors;
  const [currentIndex, setcurrentIndex] = useState(0);
  const [clrArr, setClrArr] = useState([]);
  const [price, setPrice] = useState(
    productDetails?.variants[0]?.price
      ? productDetails?.variants[0]?.price
      : '0',
  );
  const htmld = productDetails?.body_html;

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  const link = `https://shop.babyauto.com/collections/${collectionTitle}/products/${productDetails.handle}?utm_source=App&utm_medium=shop&utm_id=CBT`;
  useEffect(() => {
    getColors(productDetails?.options);
  });

  const getColors = optionsArr => {
    if (!isEmpty(optionsArr)) {
      optionsArr.find(obj => {
        if (obj?.name.includes('Color')) {
          setClrArr(obj.values);
        }
      });
    }
  };
  const [refThumb, setRefThumb] = useState(null);

  const onViewRef = React.useRef(viewableItems => {
    setcurrentIndex(viewableItems.viewableItems[0].index);
    // Use viewable items in state or as intended
  });
  const viewConfigRef = React.useRef({viewAreaCoveragePercentThreshold: 50});

  const [ref, setRef] = useState(null);

  useEffect(() => {
    if (refThumb !== null) {
      refThumb.scrollToIndex({
        animated: true,
        index: currentIndex,
        viewOffset: Dimensions.get('window').width / 3,
      });
    }
  }, [currentIndex]);
  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  /** this function for get Product View
   * @function getEcomProductView
   * @param {object} data product_id, type
   */
  async function getEcomProductView(id, type) {
    try {
      const response = await getApiData(
        BaseSetting.endpoints.addEcomProductAction,
        'POST',
        {
          product_id: id,
          type,
        },
        '',
        true,
      );

      if (response.success) {
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      sendErrorReport(error, 'addEcomProductAction');
      console.log('feed post error ===', error);
    }
  }

  return (
    <View style={{flex: 1, backgroundColor: BaseColor.whiteColor}}>
      <SubHeader
        title={productDetails.title}
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <ScrollView
        contentContainerStyle={{
          paddingHorizontal: 15,
          backgroundColor: BaseColor.whiteColor,
        }}>
        <View style={[styles.root, {backgroundColor: BaseColor.whiteColor}]}>
          <View
            style={{
              height: 300,
              marginVertical: 15,
              backgroundColor: BaseColor.whiteColor,
            }}>
            <FlatList
              data={productDetails.images}
              ref={refr => {
                setRef(refr);
              }}
              horizontal
              pagingEnabled
              renderItem={({item}) => (
                <Image style={styles.imageStyle} source={{uri: item.src}} />
              )}
              contentContainerStyle={styles.imgFlatlist}
              onViewableItemsChanged={onViewRef.current}
              viewabilityConfig={viewConfigRef.current}
              showsHorizontalScrollIndicator={false}
            />
          </View>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <TouchableOpacity
              onPress={() => {
                if (currentIndex > 0) {
                  ref.scrollToIndex({
                    animated: true,
                    index: currentIndex - 1,
                    viewPosition: currentIndex - 1,
                  });
                  setcurrentIndex(currentIndex - 1);
                }
              }}>
              <FAIcon
                name="angle-left"
                size={20}
                color={BaseColor.blackColor}
              />
            </TouchableOpacity>

            <FlatList
              data={productDetails.images}
              horizontal
              ref={refr => {
                setRefThumb(refr);
              }}
              renderItem={({item, index}) => (
                <TouchableOpacity
                  style={{
                    margin: 10,
                    borderColor: 'black',
                    borderWidth: index === currentIndex ? 2 : 0,
                    padding: 2,
                  }}
                  onPress={() => {
                    setcurrentIndex(index);
                    ref.scrollToIndex({
                      animated: true,
                      index: index,
                      viewPosition: index,
                    });
                  }}>
                  <Image
                    source={{uri: item.src}}
                    style={{height: 50, width: 50}}
                  />
                </TouchableOpacity>
              )}
              style={{marginHorizontal: 10}}
              showsHorizontalScrollIndicator={false}
            />
            <TouchableOpacity
              onPress={() => {
                if (currentIndex < productDetails.images.length - 1) {
                  ref.scrollToIndex({
                    animated: true,
                    index: currentIndex + 1,
                    viewPosition: currentIndex + 1,
                  });
                  setcurrentIndex(currentIndex + 1);
                }
              }}>
              <FAIcon
                name="angle-right"
                size={20}
                color={BaseColor.blackColor}
              />
            </TouchableOpacity>
          </View>
          <View style={{padding: 8}}>
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <Text style={[styles.name, {color: BaseColor.blackColor}]}>
                {productDetails?.title}
              </Text>
              <TouchableOpacity
                style={{padding: 8}}
                activeOpacity={0.7}
                onPress={() => {
                  getEcomProductView(productDetails.id, 'shared');
                  Share.open({
                    title: 'Share Product',
                    url: link,
                  });
                }}>
                <CustomIcon
                  name="send-2"
                  color={BaseColor.blackColor}
                  size={18}
                />
              </TouchableOpacity>
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              {productDetails?.variants[0]?.compare_at_price &&
              productDetails?.variants[0]?.compare_at_price > 0 ? (
                <>
                  <Text
                    style={{
                      ...styles.valueStyle,
                      color: BaseColor.blackColor,
                      fontSize: 18,
                      textDecorationLine: 'line-through',
                      marginEnd: 10,
                    }}>
                    €{productDetails?.variants[0].compare_at_price}
                  </Text>
                </>
              ) : null}

              <Text style={[styles.price, {color: BaseColor.blackColor}]}>
                €{price}
              </Text>
            </View>
            <Text>{translate('taxIncluded')}</Text>
            <HTML source={{html: htmld}} />
            <View
              style={[styles.divider, {backgroundColor: BaseColor.black40}]}
            />
          </View>
        </View>
      </ScrollView>

      <TouchableOpacity
        style={[styles.addToCartView, {backgroundColor: BaseColor.orange}]}
        activeOpacity={0.7}
        onPress={() => {
          if (productDetails?.handle) {
            if (link && link.includes('https://')) {
              getEcomProductView(productDetails.id, 'clicked');
              openInAppBrowser(link);
            } else {
              Toast.show('Not valid Link');
            }
          } else if (
            productDetails?.product_link &&
            productDetails?.product_link.includes('https://')
          ) {
            getEcomProductView(productDetails.id, 'clicked');
            openInAppBrowser(productDetails?.product_link);
          } else {
            Toast.show('Not valid Link');
          }
        }}>
        <FAIcon name="shopping-cart" color={BaseColor.whiteColor} size={24} />
        <Text style={[styles.addToCartText, {color: BaseColor.whiteColor}]}>
          Buy now
        </Text>
      </TouchableOpacity>
    </View>
  );
}
