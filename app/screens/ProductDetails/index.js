import React, {useState} from 'react';
import {useEffect} from 'react';
import {BackHandler, Dimensions, Image, Platform, Text} from 'react-native';
import {View} from 'react-native-animatable';
import Barcode from '@kichiyaki/react-native-barcode-generator';
import styles from './styles';
import {FontFamily} from '../../config/typography';
import Toast from 'react-native-simple-toast';
import BaseColor from '../../config/colors';
import CButton from '../../components/CButton';
import {translate} from '../../lang/Translate';
import CustomHeader from '../../components/CustomHeader/CustomHeader';
import {Images} from '../../config/Images';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
let backPressed = 0;

const {width, height} = Dimensions.get('window');
function ProductDetails({navigation, route}) {
  const {type, detail} = route?.params;
  const [loader, setLoader] = useState(false);
  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  // Device Connected to My Qeridoo API Integration..
  const submitDevice = async () => {
    setLoader(true);
    try {
      const deviceData = {
        trailer_id: detail?.trailer_id,
        platform: Platform.OS === 'android' ? 'ANDROID' : 'IOS',
        invoice_doc: '',
        bike_model: '',
      };
      const response = await getApiData(
        BaseSetting.endpoints.addProductDevice,
        'POST',
        deviceData,
        '',
        true,
      );
      if (response.success) {
        if (detail?.trailer_type === 'trailer') {
          navigation.navigate('SucessTrailerSetup');
        } else if (detail?.trailer_type === 'tracker') {
          navigation.navigate('SmartTag');
        } else if (detail?.trailer_type === 'camera') {
          navigation.navigate('SucessTrailerSetup');
          // navigation.navigate('AddCameraSetup');
        }
        setLoader(false);
      } else {
        setLoader(false);
        Toast.show(response?.message);
        navigation.replace('ConnectedProductInfo', {
          type: detail?.trailer_type,
        });
      }
    } catch (err) {
      setLoader(false);
      console.log('ERRR==', err);
    }
  };

  return (
    <View style={[styles.root]}>
      <CustomHeader
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
        leftIconColor={BaseColor.blackColor}
        backBtn
        title={
          type === 'camera'
            ? translate('ChildCam')
            : type === 'tracker'
            ? translate('TrackerInfo')
            : translate('TrailerInfo')
        }
      />
      <View
        style={{
          marginTop: 20,
          marginHorizontal: 20,
        }}>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 20,
          }}>
          <Image
            source={{uri: detail?.buggy_image}}
            width={width / 2}
            height={height / 3.5}
            resizeMode="contain"
          />
        </View>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 20,
          }}>
          <Barcode
            value={String(detail?.ean_barcode)}
            format="codabar"
            maxWidth={width / 2}
          />
        </View>
        <View style={{justifyContent: 'center', marginHorizontal: 15}}>
          <Text
            style={{
              fontSize: 20,
              fontFamily: FontFamily.bold,
              marginBottom: 20,
              color: BaseColor.blackColor,
            }}>
            {detail?.buggy_name}
          </Text>
          <Text
            style={{
              fontSize: 15,
              fontFamily: FontFamily.default,
              lineHeight: 18,
              color: BaseColor.blackColor,
            }}>
            {detail?.trailer_description}
          </Text>
        </View>
        <View style={{marginTop: '25%'}}>
          <CButton
            loader={loader}
            style={{width: '80%'}}
            title={translate('Add & Start Now ')}
            onPress={() => {
              submitDevice();
            }}
          />
        </View>
      </View>
    </View>
  );
}

export default ProductDetails;
