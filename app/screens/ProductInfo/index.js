import React, {memo, useRef} from 'react';
import {useEffect} from 'react';
import {BackHandler, StatusBar, Text} from 'react-native';
import {View} from 'react-native-animatable';
import styles from './styles';
import {FontFamily} from '../../config/typography';
import Toast from 'react-native-simple-toast';
import SubHeader from '../../components/SubHeader';
import {translate} from '../../lang/Translate';
let backPressed = 0;

function ProductInfo({navigation}) {
  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={[styles.root]}>
      <StatusBar translucent />
      <SubHeader
        title={translate('Product Info')}
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <View style={{justifyContent: 'center', flex: 1, alignItems: 'center'}}>
        <Text style={{fontSize: 20, fontFamily: FontFamily.default}}>
          Coming Soon
        </Text>
      </View>
    </View>
  );
}

export default ProductInfo;
