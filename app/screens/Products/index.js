/* eslint-disable quotes */
import {useTheme} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  Image,
  Modal,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Toast from 'react-native-simple-toast';
import {flattenDeep, isArray, isEmpty} from 'lodash';
import CButton from '../../components/CButton';
import DropDown from '../../components/DropDown';
import BaseSetting from '../../config/setting';
import {FontFamily} from '../../config/typography';
import {translate} from '../../lang/Translate';
import {getApiData} from '../../utils/apiHelper';
import styles from './styles';
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from '../../utils/commonFunction';
import SubHeader from '../../components/SubHeader';
import {useSelector} from 'react-redux';

/**
 *
 *@module Product
 *
 */
export default function Products({navigation, route}) {
  const catalouge = route?.params?.catalouge;
  const token = useSelector(state => state.auth.accessToken);
  const colors = useTheme();
  const BaseColor = colors.colors;
  const [filterModal, setfilterModal] = useState(false);
  const [selectedCat, setselectedCat] = useState('');
  const [selectedFilter, setselectedFilter] = useState('');
  const [selectedChara, setselectedChara] = useState('');
  const [filterData, setFilterData] = useState('');
  const [sortingObj, setSortingObj] = useState({});
  const [refreshing, setRefreshing] = useState(false);
  const [pageLoad, setPageLoad] = useState(true);
  const [page, setPage] = useState(1);
  const [nextPage, setNextPage] = useState(false);
  const [nextLoading, setNextLoading] = useState(false);
  const [categoryList, setCategoryList] = useState([]);
  const [ecomProductList, setEcomProductList] = useState([]);
  const [relInfo, setRel] = useState('');
  const [pageInfo, setPageInfo] = useState('');
  const [filterListF, setFilterList] = useState([]);

  const characterstics = [
    {id: 1, name: 'Alphabetically - A Z'},
    {id: 2, name: 'Alphabeticall - Z A'},
    {id: 3, name: 'Date - Old to recent'},
    {id: 4, name: 'Date - Recent to old'},
  ];
  useEffect(() => {
    setPage(1);
    setEcomProductList([]);
    getEComProductList();
    getProductCategories();
  }, []);

  /** this function for get Product List
   * @function getEComProductList
   * @param {object} data token, per_page, page, category_id
   */
  const getEComProductList = type => {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };
    setPageLoad(true);
    if (relInfo !== 'privious') {
      const data = {
        collection_id: catalouge.collection_id,
        limit: 50,
        rel: relInfo,
        page_info: pageInfo,
        per_page: 50,
        page,
        product_tag: type === 'reset' ? '' : filterData || '',
        sort_obj: type === 'reset' ? '' : sortingObj || '',
        platform: 'app',
      };

      getApiData(BaseSetting.endpoints.ecomProductList, 'POST', data, headers)
        .then(response => {
          if (response.success) {
            const tempPArr = flattenDeep([
              type === 'filter' || type === 'reset' ? [] : ecomProductList,
              response.data,
            ]);
            setTimeout(() => {
              setEcomProductList(tempPArr);
            }, 1000);
            if (response.page_info_next) {
              setPageInfo(response.page_info_next);
            }
            if (response.rel) {
              setRel(response.rel);
            }
            if (response.filter_for) {
              if (response.filter_for.length > 0) {
                if (filterData === '') {
                  const tempPArrFilter = flattenDeep([
                    filterListF,
                    response.filter_for,
                  ]);
                  setFilterList(tempPArrFilter);
                }
              }
            }
            if (response?.next_enable === 1) {
              setNextPage(true);
            } else {
              setNextPage(false);
            }
            setNextLoading(false);
            setPageLoad(false);
          } else {
            setPageLoad(false);
            Toast.show(response.message);
          }
        })
        .catch(err => {
          setPageLoad(false);
          Toast.show('Something went wrong while getting ECOM product list');
          sendErrorReport(err, 'get_product_list');
        });
    }
  };

  // this function for get product categories
  /** this function for get product categories
   * @function getProductCategories
   * @param {object} data {}
   */
  async function getProductCategories() {
    try {
      const response = await getApiData(
        BaseSetting.endpoints.getProductCategories,
        'POST',
        {},
      );
      if (response.success) {
        if (isArray(response.data)) {
          setCategoryList(response.data);
        }
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      sendErrorReport(error, 'get_prod_cat');
    }
  }

  // this function is used when list data reached to limit while scrolling
  const onEndReached = () => {
    setNextLoading(true);
    if (pageInfo !== '') {
      getEComProductList();
    }
    if (nextPage) {
      const tempPage = page + 1;
      setPage(tempPage);
    }
  };

  // this function is used when list is refresh from top
  const onRefresh = React.useCallback(() => {
    setRel('');
    setPageInfo('');
    getEComProductList();
  }, []);

  /** this function for get Product View
   * @function getEcomProductView
   * @param {object} data product_id, type
   */
  async function getEcomProductView(id, type) {
    try {
      const response = await getApiData(
        BaseSetting.endpoints.addEcomProductAction,
        'POST',
        {
          product_id: id,
          type,
        },
        '',
        true,
      );
      if (response.success) {
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      sendErrorReport(error, 'addEcomProductAction2');
      console.log('feed post error ===', error);
    }
  }

  // this function is used to find discount from product list
  const getDiscount = item => {
    let discount = 0;
    if (item?.variants) {
      item?.variants.map(i => {
        if (i?.compare_at_price !== null) {
          if (i?.compare_at_price > 0) {
            if (i?.inventory_quantity > 0) {
              discount = Math.round(
                (100 * (i?.compare_at_price - i?.price)) / i?.compare_at_price,
              ).toString();
            }
          }
        }
      });
    }
    return discount;
  };

  // get Price
  const getPrice = item => {
    let price = 0;
    if (item?.variants) {
      item?.variants.map(i => {
        if (i?.inventory_quantity > 0) {
          if (i?.price) {
            price = i?.price;
          }
        }
      });
    }
    return price;
  };

  // get Price
  const getCompareAtPrice = item => {
    let cprice = 0;
    if (item?.variants) {
      item?.variants.map(i => {
        if (i?.inventory_quantity > 0) {
          if (i?.compare_at_price) {
            cprice = i?.compare_at_price;
          }
        }
      });
    }
    return cprice;
  };

  // this function is used to render products listing design
  const renderProducts = ({item, index}) => (
    <TouchableOpacity
      activeOpacity={0.7}
      style={[
        {
          backgroundColor: BaseColor.whiteColor,
          paddingBottom: 20,
          width: Dimensions.get('window').width / 2,
        },
      ]}
      onPress={() => {
        getEcomProductView(item.id, 'viewed');
        navigation.navigate('ProductDetail', {
          productDetail: item,
          collectionTitle: catalouge.handle,
        });
      }}>
      <View style={{marginStart: 15}}>
        <View>
          <View style={{width: Dimensions.get('window').width / 2.4}}>
            <Image
              source={
                item?.image
                  ? {uri: item?.image.src}
                  : require('../../assets/images/logo.png')
              }
              style={[
                {
                  borderColor: BaseColor.black60,
                  height: Dimensions.get('window').height / 5,
                  width: Dimensions.get('window').width / 2.4,
                  resizeMode: 'cover',
                },
              ]}
            />
            {getDiscount(item) !== 0 ? (
              <View
                style={{
                  backgroundColor: BaseColor.orange,
                  paddingVertical: 5,
                  paddingHorizontal: 5,
                  position: 'absolute',
                  right: 0,
                  top: 0,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text style={{color: BaseColor.whiteColor, fontSize: 10}}>
                  {getDiscount(item) !== 0 ? `Save ${getDiscount(item)}%` : ''}
                </Text>
              </View>
            ) : null}
          </View>
          <View style={{justifyContent: 'space-around'}}>
            <View style={{paddingTop: 5}}>
              <Text style={[styles.nameStyle, {color: BaseColor.blackColor}]}>
                {item?.title}
              </Text>
            </View>
          </View>
          <View
            style={{
              ...styles.rowStyle,
              alignItems: 'center',
              paddingVertical: 5,
            }}>
            {getCompareAtPrice(item) > 0 ? (
              <>
                <Text
                  style={{
                    ...styles.valueStyle,
                    color: BaseColor.blackColor,
                    textDecorationLine: 'line-through',
                    paddingEnd: 5,
                  }}>
                  €{getCompareAtPrice(item)}
                </Text>
              </>
            ) : null}
            <View style={{...styles.rowStyle}}>
              <Text style={{...styles.valueStyle, color: BaseColor.blackColor}}>
                €{getPrice(item)}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  enableAnimateInEaseOut();

  return (
    <View style={[styles.root, {backgroundColor: BaseColor.whiteColor}]}>
      <View style={styles.root}>
        <SubHeader
          title={catalouge.title}
          leftIconName="left-arrow"
          onLeftPress={() => {
            navigation.goBack();
          }}
        />
        {isEmpty(ecomProductList) ? <Text>{ecomProductList}</Text> : null}
        {pageLoad ? (
          <View style={{justifyContent: 'center', flex: 1}}>
            <ActivityIndicator color={BaseColor.primary} />
          </View>
        ) : (
          <FlatList
            keyExtractor={(item, index) => index}
            numColumns={2}
            data={ecomProductList}
            renderItem={renderProducts}
            contentContainerStyle={{flexGrow: 1, paddingBottom: 24}}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            onEndReachedThreshold={0.4}
            onEndReached={onEndReached}
          />
        )}
      </View>
      <Modal
        style={{flex: 1}}
        visible={filterModal}
        transparent
        animationType="slide"
        onRequestClose={() => {
          setfilterModal(false);
        }}>
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: BaseColor.black40,
            justifyContent: 'flex-end',
          }}
          onPress={() => {
            setfilterModal(false);
          }}>
          <View
            style={{
              backgroundColor: BaseColor.blueDark,
              padding: 24,
              borderTopEndRadius: 16,
              borderTopStartRadius: 16,
            }}>
            <Text
              style={{
                color: BaseColor.whiteColor,
                fontSize: 16,
                marginVertical: 8,
                fontFamily: FontFamily.default,
                fontWeight: 'bold',
              }}>
              FILTER
            </Text>
            <DropDown
              placeholder="Sorting"
              data={characterstics}
              style={{borderRadius: 12, marginEnd: 4}}
              valueProp="name"
              onSelect={val => {
                setselectedChara(val);
                let obj = {};
                if (val.name === 'Alphabetically - A Z') {
                  obj = {sort_value: 'title', sort_type: 'ascend'};
                } else if (val.name === 'Alphabeticall - Z A') {
                  obj = {sort_value: 'title', sort_type: 'desc'};
                } else if (val.name === 'Date - Old to recent') {
                  obj = {
                    sort_value: 'created_at',
                    sort_type: 'ascend',
                  };
                } else if (val.name === 'Date - Recent to old') {
                  obj = {sort_value: 'created_at', sort_type: 'desc'};
                }
                setSortingObj(obj);
              }}
              selectedObject={selectedChara}
            />
            <View style={{marginTop: 16}}>
              <DropDown
                placeholder="Filter"
                data={filterListF}
                style={{borderRadius: 12, marginEnd: 4}}
                valueProp="product_tag"
                disable
                onSelect={val => {
                  setPageInfo('');
                  setselectedFilter(val);
                  setFilterData(val.product_tag);
                }}
                selectedObject={selectedFilter}
              />
            </View>
            <View style={{flexDirection: 'row', marginBottom: 16}}>
              <CButton
                title="SEARCH"
                style={{
                  backgroundColor: BaseColor.whiteColor,
                  borderRadius: 8,
                  marginTop: 16,
                  marginEnd: 4,
                  flex: 1,
                }}
                titleStyle={{
                  color: BaseColor.blackColor,
                  fontWeight: 'bold',
                }}
                onPress={() => {
                  setfilterModal(false);
                  setEcomProductList([]);
                  setPage(1);
                  setPageLoad(true);
                  getEComProductList('filter');
                }}
              />
              <CButton
                title="RESET"
                style={{
                  backgroundColor: BaseColor.orange,
                  borderRadius: 8,
                  marginTop: 16,
                  marginStart: 4,
                  flex: 1,
                }}
                titleStyle={{
                  color: BaseColor.whiteColor,
                  fontWeight: 'bold',
                }}
                onPress={() => {
                  setselectedCat({});
                  setPage(1);
                  setRel('');
                  setPageInfo('');
                  getEComProductList('reset');
                }}
              />
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}
