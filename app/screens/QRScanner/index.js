/* eslint-disable no-nested-ternary */
/* eslint-disable eqeqeq */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
/* eslint-disable no-fallthrough */
/* eslint-disable max-len */
/* eslint-disable no-console */
/* eslint-disable quotes */
/**
 * Sample BLE React Native App
 *
 * @format
 * @flow strict-local
 */

import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  NativeModules,
  NativeEventEmitter,
  Platform,
  PermissionsAndroid,
  BackHandler,
  Text,
  Dimensions,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import QRCodeScanner from 'react-native-qrcode-scanner';
import _, {
  find,
  findIndex,
  isArray,
  isBoolean,
  isEmpty,
  isObject,
  isUndefined,
} from 'lodash';
import BleManager from 'react-native-ble-manager';
import {useDispatch, useSelector} from 'react-redux';
import Toast from 'react-native-simple-toast';
import {
  openSettings,
  check,
  PERMISSIONS,
  // requestMultiple,
  request,
} from 'react-native-permissions';
import _BackgroundTimer from 'react-native-background-timer';
import WifiManager from 'react-native-wifi-reborn';
import {CustomIcon} from '../../config/LoadIcons';
import styles from './styles';
import CHeader from '../../components/CHeader';
import {translate} from '../../lang/Translate';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {sendErrorReport} from '../../utils/commonFunction';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';
import AuthAction from '../../redux/reducers/auth/actions';
import SVGCheckScan from '../../assets/images/checkScan.svg';
import InstructWifiModal from '../../components/WifiModal/InstructWifiModal';
import {SvgFromXml, SvgXml} from 'react-native-svg';
import commonSvg from '../../assets/commonSvg/commonnSvg';
import SubHeader from '../../components/SubHeader';

const BleManagerModule = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);

/**
 *
 *@module QRScanner
 *
 */
const QRScanner = ({navigation, route}) => {
  const {from} = route?.params;
  const {width, height} = Dimensions.get('window');
  const dispatch = useDispatch();
  const token = useSelector(state => state.auth.accessToken);
  const languageData = useSelector(state => state.language);
  const [isScanning, setIsScanning] = useState(false);
  const peripherals = new Map();
  const [list, setList] = useState([]);
  const connectedDeviceDetail = useSelector(
    state => state.bluetooth.connectedDeviceDetail,
  );
  const [imgState, setImgState] = useState('');
  const {isClickAddQr, bleDeviceList, isSkipShow, isBleConnected} = useSelector(
    state => state.bluetooth,
  );
  const {
    setStep4Done,
    setStep4bDone,
    setStep5Done,
    setStep5bDone,
    setStep5cDone,
    setStep5dDone,
    setStep5eDone,
    setStep5fDone,
    setStep6Done,
    setStep7,
    setStep8Done,
  } = AuthAction;
  const step4Done = useSelector(state => state.auth.step4Done);
  const step4bDone = useSelector(state => state.auth.step4bDone);
  const step8Done = useSelector(state => state.auth.step8Done);
  const closeOnboarding = useSelector(state => state.auth.closeOnboarding);
  const [isRefreshing, setisRefreshing] = useState(false);
  const [refresh, setrefresh] = useState(false);
  const [deviceId, setDeviceId] = useState(null);
  const [skip, setSkip] = useState(false);
  const [scanningText, setScanningText] = useState(
    translate('searchingQRCode'),
  );
  const [showStep4, setShowStep4] = useState(false);
  const [showStep4b, setShowStep4b] = useState(false);

  const [isOpenMOdal, setIsOpenModal] = useState(false);

  useEffect(() => {
    if (Platform.OS === 'android' && Platform.Version >= 31) {
      check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
        .then(res => {
          console.log('BLUETOOTH_CONNECT--q-', res);
          if (res === 'granted') {
            // setBPermission(true);
          }
        })
        .catch(e => {
          console.log('bluetooth_', e);
        });
      request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
        .then(result => {
          console.log('BLUETOOTH_CONNECT----1', result);
        })
        .then(statuses => {
          console.log(
            'BLUETOOTH_CONNECT--2',
            statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT],
          );
        });
    }
  }, []);
  useEffect(() => {
    // if (Platform.OS == "android") {
    setTimeout(() => {
      setSkip(true);
    }, 6000);
    // }
  }, []);

  const startScan = () => {
    if (!isScanning) {
      BleManager.scan([], 3, true)
        .then(results => {
          setIsScanning(true);
          setrefresh(false);
        })
        .catch(err => {
          setrefresh(false);
          console.error(err);
          sendErrorReport(err, 'scan_error');
        });
    }
  };

  useEffect(() => {
    setTimeout(() => {
      startScan();
    }, 1500);
  }, []);

  useEffect(() => {
    dispatch(BluetoothAction.setBleDeviceList(list));
  }, [list]);

  const handleStopScan = () => {
    setIsScanning(false);
    setisRefreshing(false);
    dispatch(BluetoothAction.setClickAddQr(false));
  };

  const handleDiscoverPeripheral = peripheral => {
    if (!peripheral.name) {
      peripheral.name = 'NO NAME';
    }
    peripherals.set(peripheral.id, peripheral);
    setList(Array.from(peripherals.values()));
  };

  useEffect(() => {
    /* Listening to IOS Background events as per the docs - Not Tested */
    if (isClickAddQr || isRefreshing) {
      bleManagerEmitter.addListener(
        'BleManagerCentralManagerWillRestoreState',
        data => {
          console.log(
            'BLE ==> BleManagerCentralManagerWillRestoreState ===> ',
            data,
          );
        },
      );

      bleManagerEmitter.addListener(
        'BleManagerDiscoverPeripheral',
        handleDiscoverPeripheral,
      );
      bleManagerEmitter.addListener('BleManagerStopScan', handleStopScan);

      if (Platform.OS === 'android' && Platform.Version >= 23) {
        console.log('called---7');
        PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ).then(result => {
          if (result) {
            console.log('Permission is OK');
          } else {
            PermissionsAndroid.request(
              PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            ).then(res => {
              if (res) {
                console.log('User accept');
              } else {
                console.log('User refuse');
              }
            });
          }
        });
      }
    }

    return () => {
      console.log('unmount');
      // bleManagerEmitter.removeListener(
      //   'BleManagerDiscoverPeripheral',
      //   handleDiscoverPeripheral
      // );
      // bleManagerEmitter.removeListener('BleManagerStopScan', handleStopScan);
      // bleManagerEmitter.removeListener(
      //   'BleManagerDisconnectPeripheral',
      //   handleDisconnectedPeripheral
      // );
      // bleManagerEmitter.removeListener(
      //   'BleManagerDidUpdateValueForCharacteristic',
      //   handleUpdateValueForCharacteristic
      // );
    };
  }, [isClickAddQr, isRefreshing]);

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  /** this function for onReadQR
   * @function onReadQR
   * @param {object} data device_bluetooth_name
   */
  const onReadQR = async e => {
    setScanningText(translate('readingQRCode'));
    setImgState('reading');
    if (e.data === '') {
      setImgState('');
      Toast.show('Invalid QR Code.Please try again.');
      return;
    }
    // Scan QR Code and Find SSID
    let qrSSID = '';
    if (e?.data && _.includes(e.data, 'http://')) {
      const readD = _.split(e.data, 'http://');
      const readD1 = _.split(readD[1], ',');
      qrSSID = readD1.splice(0, 1).toString();
    } else {
      const readD = _.split(e.data, ',');
      const d = readD.splice(0, 1).toString();
      if (d) {
        qrSSID = d.replace(' ', '0');
      }
    }

    console.log('wifiiii', qrSSID);

    WifiManager.getCurrentWifiSSID().then(
      ssid => {
        console.log('Your current connected wifi SSID is ' + ssid);
        if (ssid === qrSSID) {
          Toast.show('Connected!');
          setImgState('right');
          dispatch(BluetoothAction.setQRWifiSSID(qrSSID));
          dispatch(BluetoothAction.setIsBleConnected(true));
          setTimeout(() => {
            dispatch(BluetoothAction.setIsConnectLoad(false));
            BluetoothAction.setPath('fromConnect'); // true
            navigation.navigate('Connect', {
              product_id: qrSSID,
              device_id: qrSSID,
              device_data: 'Qeridoo-CAM',
              device_ssid: qrSSID,
            });
          }, 1000);
        } else {
          console.log('called---5');
          if (Platform.OS === 'android' && Platform.Version >= 29) {
            setIsOpenModal(true);
            setImgState('');
          } else {
            console.log('called---8');
            autoConnectWifi(qrSSID);
          }
        }
      },
      () => {
        console.log('called---6');
        if (Platform.OS === 'android' && Platform.Version >= 29) {
          console.log('called---7');
          setIsOpenModal(true);
          setImgState('');
        } else {
          console.log('called---9');
          autoConnectWifi(qrSSID);
        }
      },
    );
  };

  const autoConnectWifi = async qrSSID => {
    console.log('Connecting to SSID: ' + qrSSID);
    await WifiManager.connectToProtectedSSID(
      qrSSID,
      'Test12345',
      false,
      1000,
      false,
    )
      .then(
        async c => {
          setImgState('right');
          dispatch(BluetoothAction.setQRWifiSSID(qrSSID));
          dispatch(BluetoothAction.setIsBleConnected(true));
          setTimeout(() => {
            dispatch(BluetoothAction.setIsConnectLoad(false));
            BluetoothAction.setPath('fromConnect'); // true
            navigation.navigate('Connect', {
              product_id: qrSSID,
              device_id: qrSSID,
              device_data: 'Qeridoo-CAM',
              device_ssid: qrSSID,
            });
          }, 2500);
        },
        err => {
          console.log(
            'Accounts / connectToSSID / status ===> Connection failed! ',
            err,
          );
          setImgState('');
          console.log('Error ===> ', err);
          sendErrorReport(err, ' Connection failed!');
          // Alert.alert(JSON.stringify(err));
          Toast.show('connection failed!, please try again');
        },
      )
      .catch(e => {
        setImgState('');
        // Toast.show("connection failed!");
        // Alert.alert(JSON.stringify(e));
        console.log('wifi error connecting!', e);
        sendErrorReport(e, ' Connection failed2');
      });
  };

  const onReadQRList = async item => {
    sendErrorReport(item, 'item__item');
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getDevice,
        'POST',
        {
          device_ssid: item?.name,
          product_id: item?.id,
          lang_code: languageData?.languageData || 'en',
        },
        headers,
      );

      if (response.success && !isEmpty(response.data)) {
        setDeviceId(response?.data?.id);
        if (isArray(connectedDeviceDetail)) {
          const obj = {...response?.data};
          obj.product_id = item?.id;

          const arr = [...connectedDeviceDetail] || [];
          const index = findIndex(
            arr,
            lt => lt?.product_id === obj?.product_id,
          );
          if (index > -1) {
            arr[index] = obj;
          } else {
            arr.unshift(obj);
          }
          dispatch(BluetoothAction.setConnectedDeviceDetail(arr));
          dispatch(BluetoothAction.setConnectedDeviceDetails(arr));
          dispatch(BluetoothAction.setSwiperKey(obj?.product_id));
          dispatch(BluetoothAction.setActiveDeviceId(obj));
          // dispatch(BluetoothAction.setConnectedDeviceDetails(...connectedDeviceDetails, obj));
        }
        // dispatch(BluetoothAction.setConnectedDeviceDetail(deviceArr));
        // dispatch(BluetoothAction.setConnectedDeviceId(response?.data?.id));
        // dispatch(BluetoothAction.setConnectedDeviceName(e.data));
        if (isObject(item) && !isEmpty(item)) {
          dispatch(BluetoothAction.setDeviceID(''));
          setTimeout(() => {
            dispatch(BluetoothAction.setDeviceID(item.id));
            dispatch(BluetoothAction.setLastDeviceId(item.id));
            dispatch(BluetoothAction.setIsConnectLoad(false)); // true
            dispatch(BluetoothAction.setClickAddQr(false));
            navigation.navigate('Connect', {
              product_id: item?.id,
              device_id: response?.data?.id || deviceId,
              device_data: item?.name,
              device_ssid: item?.name,
            });
          }, 2500);
        } else {
          setIsScanning(false);
          // Toast.show("Can't find any device. Please try again");
          Toast.show(translate('cannotFindDevice'));
          startScan();
        }
      } else {
        Toast.show(response.message);
        setScanningText(translate('searchingQRCode'));
      }
    } catch (error) {
      console.log('error device detail ===', error);
      sendErrorReport(error, 'on_read_qr');
    }
  };

  // Device Connect to User...
  const onGetTrackerDetail = async barcodeVal => {
    try {
      const response = await getApiData(
        BaseSetting.endpoints.getTrailerDetail + `?ean_barcode=${barcodeVal}`,
        'GET',
      );
      if (response.success) {
        navigation.navigate('ProductDetails', {
          type: response?.trailer_type,
          detail: response?.data,
        });
      } else {
        navigation.goBack();
        Toast.show(response?.message);
      }
    } catch (err) {
      console.log('ERRR==', err);
    }
  };

  const NoPermissionViewIos = (
    <View
      style={{
        width: Dimensions.get('window').width * 0.8,
        alignSelf: 'center',
      }}>
      <Text style={styles.qrTextStyle}>{translate('noCameraAcces')}</Text>
      <TouchableOpacity onPress={() => openSettings()}>
        <Text style={styles.openSettingsText}>{translate('openSettings')}</Text>
      </TouchableOpacity>
    </View>
  );

  const renderItem = ({item}) => (
    // console.log("renderItem -> item", list);
    <View
      style={{
        padding: 12,
        borderRadius: 8,
        backgroundColor: '#fff',
        shadowColor: '#000',
        margin: 8,
        sshadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 8,
        }}>
        {/* <Text style={{ fontSize: 18, fontWeight: "700" }}>NAME : </Text> */}
        <Text
          style={{
            fontSize: 18,
            marginStart: 8,
            flex: 1,
            fontWeight: '700',
          }}>
          {item?.name == 'NO NAME' ? 'N/A' : item?.name}
        </Text>
        <TouchableOpacity
          style={{
            backgroundColor:
              item?.advertising?.isConnectable == 1 ? 'green' : 'green',
            padding: 8,
            borderRadius: 8,
          }}
          activeOpacity={0.7}
          onPress={() => {
            onReadQRList(item);
          }}>
          <Text style={{color: '#fff'}}>CONNECT</Text>
        </TouchableOpacity>
      </View>
      <View style={{flexDirection: 'row'}}>
        <Text style={{fontSize: 15, marginStart: 8, flex: 1}}>{item?.id}</Text>
      </View>
    </View>
  );

  const onRefresh = () => {
    setisRefreshing(true);
    BleManager.stopScan().then(() => {
      // Success code
      console.log('Scan stopped');
    });
    setTimeout(() => {
      startScan();
    }, 1000);
  };

  return (
    <View style={styles.root}>
      <SubHeader
        title={translate('camera')}
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <View
        style={{
          alignItems: 'center',
          marginBottom: 20,
        }}>
        <Text style={[styles.qrTextStyle]}>Scanning...</Text>
      </View>
      <View style={{}}>
        <QRCodeScanner
          ref={node => {}}
          onRead={e => {
            if (from === 'productInfo') {
              onGetTrackerDetail(e?.data);
            } else {
              onReadQR(e);
            }
          }}
          reactivate
          reactivateTimeout={5000}
          cameraProps={{height: Dimensions.get('window').height}}
          notAuthorizedView={NoPermissionViewIos}
        />
        {imgState === '' && (
          <CustomIcon
            name="Scanner"
            size={height / 2}
            color={BaseColor.whiteColor}
            style={{
              paddingTop: height / 7,
              alignSelf: 'center',
            }}
          />
        )}
        {imgState === 'reading' && (
          <>
            <ActivityIndicator color={BaseColor.whiteColor} size="large" />
            <Text
              style={{
                color: BaseColor.whiteColor,
                fontSize: 24,
                fontWeight: '700',
                position: 'absolute',
                justifyContent: 'center',
                alignContent: 'center',
                alignSelf: 'center',
                bottom: '5%',
              }}>
              Connecting.....
              <Text>Please wait for a few seconds...</Text>
            </Text>
          </>
        )}
        {imgState === 'right' && (
          <>
            <View
              style={{
                position: 'absolute',
                justifyContent: 'center',
                alignContent: 'center',
                alignSelf: 'center',
                top: '47%',
              }}>
              <SvgFromXml xml={commonSvg.SVGCheckScan} width={45} height={30} />
            </View>
            <Text
              style={{
                color: BaseColor.whiteColor,
                fontSize: 24,
                fontWeight: '700',
                position: 'absolute',
                justifyContent: 'center',
                alignContent: 'center',
                alignSelf: 'center',
                bottom: '5%',
              }}>
              Connected!
            </Text>
          </>
        )}
      </View>
      {/* )} */}
      {/* <View style={styles.bottomViewStyle}>
        <View style={styles.scanDescViewStyle}>
          <Text style={[styles.qrTextStyle, {fontFamily: FontFamily.default}]}>
            {translate('scanQRText')}
          </Text>
        </View>
      </View> */}
      <InstructWifiModal
        visible={isOpenMOdal}
        onClose={() => {
          setIsOpenModal(false);
        }}
      />
    </View>
  );
};

export default QRScanner;
