import React, {memo, useCallback, useRef, useState} from 'react';
import {useEffect} from 'react';
import {
  ActivityIndicator,
  Alert,
  BackHandler,
  Dimensions,
  FlatList,
  Image,
  Modal,
  ScrollView,
  Text,
  TouchableOpacity,
} from 'react-native';
import {View} from 'react-native-animatable';
import styles from './styles';
import {FontFamily} from '../../config/typography';
import Toast from 'react-native-simple-toast';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {CustomIcon} from '../../config/LoadIcons';
import BaseColor from '../../config/colors';
import CInput from '../../components/CInput';
import {translate} from '../../lang/Translate';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {isEmpty} from 'lodash';
import {useFocusEffect, useIsFocused} from '@react-navigation/native';
import {downloadFile, savePageViewData} from '../../utils/commonFunction';
import TrackerConnect from '../../components/TrackerConnected';
import {useSelector} from 'react-redux';
import NewAlert from '../../components/NewAlertModal';
import ShopAccessories from '../../components/ShopAccessories';
let backPressed = 0;

function Qeridoo({navigation}) {
  const isFocused = useIsFocused();
  const {connectedDeviceList, sliderProgress} = useSelector(sta => sta.modal);
  const [productArr, setProductArry] = useState([]);
  const [connectedList, setConnectedList] = useState([]);
  const [selectItem, setSelectItem] = useState({});
  const [productModal, setProductModal] = useState(false);
  const [collapse, setCollapse] = useState(true);
  const [connectedDevice, setConnectedDevice] = useState([]);
  const [deviceLoading, setDeviceLoading] = useState(false);
  const [addChildModal, setaddChildModal] = useState(false);
  // Render
  const renderData = ({item, ind}) => {
    return (
      <TouchableOpacity
        onPress={() => {
          if (item?.trailer_type === 'add') {
            if (sliderProgress < 0.6) {
              setaddChildModal(true);
            } else {
              setProductModal(true);
              if (
                connectedDeviceList?.trailer_detail < 1 ||
                isEmpty(connectedDeviceList)
              ) {
                getDeviceList('', 'trailer');
              } else {
                getDeviceList('');
              }
            }
          } else {
            setSelectItem(item);
          }
        }}
        activeOpacity={0.7}
        style={[
          styles.deviceContainer,
          {
            borderWidth: selectItem.id === item?.id ? 3 : 1.5,
            borderColor:
              item?.trailer_type === 'add' || selectItem.id === item?.id
                ? BaseColor.primary
                : '#D6DADA',
            borderStyle: item?.trailer_type === 'add' ? 'dashed' : 'solid',
          },
        ]}>
        {item?.trailer_type === 'add' ? (
          <CustomIcon name="plus" size={20} color={BaseColor.primary} />
        ) : (
          <Image
            source={{uri: item?.buggy_image}}
            style={{width: 50, height: 50}}
          />
        )}
      </TouchableOpacity>
    );
  };

  // Device List API Integration
  const getDeviceList = async (text = '', deviceType = '') => {
    try {
      const url =
        BaseSetting.endpoints.getDeviceList +
        `?allData=1&buggy_name=${text}&trailer_type=${deviceType}`;
      const response = await getApiData(url, 'GET');
      if (response?.success) {
        setProductArry(response?.data);
      }
    } catch (err) {
      console.log('ERRR==', err);
    }
  };
  // End

  // Get Connected Device List
  const getConnectedList = async () => {
    setDeviceLoading(true);
    try {
      const response = await getApiData(
        BaseSetting.endpoints.getConnectedList + `?allData=1`,
        'GET',
      );
      if (response?.success) {
        const tempArr = response?.data;
        setConnectedDevice(isEmpty(response?.data) ? [] : response?.data);
        const arr = {trailer_type: 'add'};
        tempArr.push(arr);
        setConnectedList(tempArr);
        setSelectItem(tempArr && tempArr[0]);
      } else {
        setConnectedList([{trailer_type: 'add'}]);
      }
      setDeviceLoading(false);
    } catch (err) {
      setDeviceLoading(false);
      console.log('ERRR==', err);
    }
  };
  // End

  useEffect(() => {
    getConnectedList();
  }, [isFocused]);

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  useFocusEffect(
    useCallback(() => {
      savePageViewData('dashboard');
    }, []),
  );

  return (
    <View style={[styles.root]}>
      {deviceLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="default" color={BaseColor.primary} />
        </View>
      ) : (
        <ScrollView
          style={{
            marginVertical: getStatusBarHeight() + 20,
            marginHorizontal: 15,
          }}
          bounces={false}
          showsVerticalScrollIndicator={false}>
          <Text style={styles.deviceText}>
            {connectedDeviceList?.tracker_detail >= 1 &&
            connectedDeviceList?.trailer_detail >= 1 &&
            connectedDeviceList?.camera_detail >= 1
              ? `Welcome to My Qeridoo!`
              : connectedDeviceList?.trailer_detail >= 1 &&
                connectedDeviceList?.camera_detail >= 1
              ? `Camera added successfully, you can add any other devices you own by tapping on the “+” icon.`
              : connectedDeviceList?.trailer_detail >= 1
              ? `Trailer added successfully, you can add camera, tracker and otherdevices you own by tapping on the “+” icon.`
              : `you can add trailer, camera, tracker and otherdevices you own by tapping on the “+” icon.`}
          </Text>

          <View style={{marginVertical: 20}}>
            <FlatList
              horizontal
              data={connectedList}
              keyExtractor={(item, index) => index}
              renderItem={renderData}
              showsHorizontalScrollIndicator={false}
            />
          </View>

          {selectItem?.trailer_doc && (
            <TouchableOpacity
              onPress={() =>
                downloadFile(
                  selectItem?.trailer_doc,
                  `${selectItem?.buggy_name}.pdf`,
                )
              }
              activeOpacity={0.8}
              style={styles.downloadManualContainer}>
              <Text style={styles.downloadManualText}>
                {translate('DownloadUserManual')}
              </Text>
              <CustomIcon name="pdf" size={20} color={'#75ACAA'} />
            </TouchableOpacity>
          )}
          {!isEmpty(connectedDevice) && (
            <View
              style={[
                styles.collapseContainer,
                {
                  paddingBottom: !collapse ? 10 : 30,
                },
              ]}>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => setCollapse(!collapse)}
                style={styles.collapseTitleContainer}>
                <Text style={styles.collapseTitle}>
                  {selectItem.trailer_type === 'trailer'
                    ? translate('TrailerInfo')
                    : selectItem.trailer_type === 'camera'
                    ? translate('CameraInfo')
                    : translate('TrackerInfo')}
                </Text>
                <CustomIcon
                  name={collapse ? 'arrow-down' : 'arrow-up'}
                  size={20}
                  style={{
                    color: collapse ? BaseColor.primary : BaseColor.blackColor,
                  }}
                />
              </TouchableOpacity>
              {collapse && (
                <>
                  <CInput
                    textInputWrapper={{borderWidth: 0}}
                    value={selectItem?.sku_code}
                    inputStyle={{color: BaseColor.disableInput}}
                    editable={false}
                  />
                  <CInput
                    textInputWrapper={{borderWidth: 0}}
                    value={'23/10/24'}
                    inputStyle={{color: BaseColor.disableInput}}
                    editable={false}
                  />
                  <Text style={{fontSize: 12, color: '#BCBBBE'}}>
                    Next Renewal/Registration Date
                  </Text>
                  <CInput
                    textInputWrapper={{borderWidth: 0}}
                    value={selectItem?.email}
                    inputStyle={{color: BaseColor.disableInput}}
                    editable={false}
                  />
                  <Text style={{fontSize: 12, color: '#BCBBBE'}}>
                    email registered at Qeridoo.com
                  </Text>
                </>
              )}
            </View>
          )}
          {selectItem?.trailer_type === 'tracker' && (
            <View style={{marginBottom: 20}}>
              <TrackerConnect navigation={navigation} />
            </View>
          )}
          {selectItem?.trailer_type === 'trailer' && (
            <ShopAccessories navigation={navigation} />
          )}
        </ScrollView>
      )}
      <Modal
        visible={productModal}
        onRequestClose={() => setProductModal(false)}
        transparent>
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            setProductModal(false);
          }}
          style={styles.addDeviceModal}>
          <View style={styles.modalContent}>
            <Text style={styles.inputText}>Choose your Qeridoo Product</Text>
            <CInput
              textInputWrapper={{
                borderRadius: 6,
                height: 38,
                justifyContent: 'center',
              }}
              onChangeText={val => {
                if (
                  connectedDeviceList?.trailer_detail < 1 ||
                  isEmpty(connectedDeviceList)
                ) {
                  getDeviceList(val, 'trailer');
                } else {
                  getDeviceList(val);
                }
              }}
              inputStyle={{
                color: BaseColor.primary,
              }}
            />
            {!isEmpty(productArr) ? (
              <ScrollView style={styles.listContainer}>
                {productArr &&
                  productArr.map(item => (
                    <TouchableOpacity
                      activeOpacity={0.7}
                      style={styles.listItem}
                      onPress={() => {
                        if (item?.trailer_type === 'tracker') {
                          navigation.navigate('SmartTag', {details: item?.id});
                        } else {
                          navigation.navigate('ConnectedProductInfo', {
                            type: item?.trailer_type,
                          });
                        }
                        setProductModal(false);
                        setProductArry([]);
                      }}>
                      <Text
                        style={{
                          color: '#4B5959',
                          fontFamily: FontFamily.regular,
                        }}>
                        {item?.buggy_name}
                      </Text>
                    </TouchableOpacity>
                  ))}
              </ScrollView>
            ) : null}
          </View>
        </TouchableOpacity>
      </Modal>
      <NewAlert
        qeridoo
        visible={addChildModal}
        onRequestClose={() => setaddChildModal(false)}
        onCancelPress={() => setaddChildModal(false)}
        onOkPress={() => {
          navigation.navigate('ChildInfo', {type: 'add'});
          setaddChildModal(false);
        }}
        alertMessage={'To add a device, please first create a child profile.'}
        messageStyle={{
          color: BaseColor.blackColor,
          fontSize: 18,
          textAlign: 'center',
          width: '76%',
          lineHeight: 20,
          fontFamily: FontFamily.regular,
        }}
      />
    </View>
  );
}

export default Qeridoo;
