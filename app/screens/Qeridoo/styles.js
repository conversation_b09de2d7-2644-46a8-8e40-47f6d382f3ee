import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
  },
  deviceContainer: {
    width: 71,
    height: 69,
    marginRight: 15,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  deviceText: {
    fontSize: 12,
    fontFamily: FontFamily.regu,
    lineHeight: 15,
    color: BaseColor.blackColor,
  },
  downloadManualContainer: {
    marginVertical: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  downloadManualText: {
    fontSize: 18,
    fontFamily: FontFamily.regular,
    textDecorationLine: 'underline',
    color: '#75ACAA',
    marginRight: 10,
  },
  collapseContainer: {
    backgroundColor: '#F8F8F8',
    padding: 10,
    paddingVertical: 10,
    borderRadius: 8,
  },
  collapseTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  collapseTitle: {
    fontFamily: FontFamily.bold,
    fontSize: 22,
    color: BaseColor.blackColor,
  },
  addDeviceModal: {
    flex: 1,
    backgroundColor: 'rgba(10, 10, 10, 0.68)',
    paddingTop: '50%',
  },
  modalContent: {
    marginHorizontal: 24,
    borderRadius: 12,
    paddingVertical: 12,
    maxHeight: Dimensions.get('window').height / 1.5,
  },
  inputText: {
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.regular,
    paddingVertical: 8,
  },
  listContainer: {
    borderRadius: 6,
    marginTop: 10,
    backgroundColor: BaseColor.whiteColor,
    padding: 5,
  },
  listItem: {
    backgroundColor: '#ECF4F4',
    padding: 8,
    marginVertical: 2,
    borderRadius: 3,
  },
});
export default styles;
