/* eslint-disable global-require */
/* eslint-disable quotes */
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  BackHandler,
  ImageBackground,
  Modal,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
  Image,
  Dimensions,
  Platform,
} from 'react-native';
// import Animated, {
//   useAnimatedStyle,
//   useSharedValue,
//   withTiming,
// } from "react-native-reanimated";
import Toast from 'react-native-simple-toast';
import {SvgXml} from 'react-native-svg';
import {useDispatch, useSelector} from 'react-redux';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import styles from './styles';
import CButton from '../../components/CButton';
import {initTranslate, translate} from '../../lang/Translate';
import {CustomIcon} from '../../config/LoadIcons';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';
import langArr from '../../assets/flagSvg/flags';
import languageActions from '../../redux/reducers/language/actions';
import {store} from '../../redux/store/configureStore';
import AuthAction from '../../redux/reducers/auth/actions';
import {requestLocationPermission} from '../../utils/commonFunction';
import {Images} from '../../config/Images';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import MultiStepSlider from '../../components/MultiSlider/MultiSlider';

let backPressed = 0;
/**
 *
 *@module Welcome
 *
 */
const RedirectLS = ({navigation}) => {
  const dispatch = useDispatch();
  const {setLanguage} = languageActions;
  const languageData = useSelector(state => state.language.languageData);
  const allowBTDone = useSelector(state => state.auth.allowBTDone);
  const {setStepAllowBTDone} = AuthAction;

  // Get safe area insets for proper spacing
  const insets = useSafeAreaInsets();

  // const logoAnimT = useSharedValue(0);
  // const logoAnimO = useSharedValue(0);
  // const textAnimT = useSharedValue(0);
  // const btnAnimT = useSharedValue(0);
  // const langAnimT = useSharedValue(0);

  const flagsArr = langArr;
  const selectedLang = flagsArr.filter(item => item.code === languageData);

  const [selectedLanguage, setselectedLanguage] = useState(selectedLang[0]);
  const [langModal, setlangModal] = useState(false);
  const [refresh, setRefresh] = useState(false);
  // const [showAllowBT, setShowAllowBT] = useState(false);

  const {width, height} = Dimensions.get('window');

  // Calculate proper bottom spacing considering safe area and potential tab bar
  const bottomSpacing =
    Platform.OS === 'android'
      ? Math.max(insets.bottom, 20) + 60 // Add extra space for tab bar on Android
      : insets.bottom + 60;

  // Responsive sizing based on screen dimensions
  const isSmallScreen = height < 700;
  const isLargeScreen = height > 900;

  // Adjust image sizes based on screen size
  const logoSize = {
    width: isSmallScreen ? width / 2.5 : width / 2,
    height: isSmallScreen ? height / 8 : height / 6,
  };

  const sloganSize = {
    width: isSmallScreen ? width / 1.3 : width / 1.5,
    height: isSmallScreen ? height / 9 : height / 7,
  };

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    requestLocationPermission();
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  // useEffect(() => {
  //   if (!allowBTDone) {
  //     setTimeout(() => {
  //       setShowAllowBT(true);
  //     }, 1000);
  //   }
  // }, []);
  // const logoStyleAnim = useAnimatedStyle(() => ({
  //   transform: [
  //     {
  //       translateY: withTiming(logoAnimT.value, {
  //         duration: 1000,
  //       }),
  //     },
  //   ],
  //   opacity: withTiming(logoAnimO.value, {
  //     duration: 1000,
  //   }),
  // }));

  // const textStyleAnim = useAnimatedStyle(() => ({
  //   transform: [
  //     {
  //       translateY: withTiming(textAnimT.value, {
  //         duration: 1000,
  //       }),
  //     },
  //   ],
  //   opacity: withTiming(logoAnimO.value, {
  //     duration: 1000,
  //   }),
  // }));

  // const btnStyleAnim = useAnimatedStyle(() => ({
  //   transform: [
  //     {
  //       translateY: withTiming(btnAnimT.value, {
  //         duration: 1000,
  //       }),
  //     },
  //   ],
  //   opacity: withTiming(logoAnimO.value, {
  //     duration: 1000,
  //   }),
  // }));

  // const langStyleAnim = useAnimatedStyle(() => ({
  //   transform: [
  //     {
  //       translateY: withTiming(langAnimT.value, {
  //         duration: 1000,
  //       }),
  //     },
  //   ],
  //   opacity: withTiming(logoAnimO.value, {
  //     duration: 1000,
  //   }),
  // }));

  // useEffect(() => {
  //   logoAnimT.value = -250;
  //   textAnimT.value = -100;
  //   btnAnimT.value = -50;
  //   logoAnimO.value = 1;
  //   langAnimT.value = -50;
  // }, []);

  /** this function for change app language
   * @function changeLanguage
   * @param {object} data code, name
   */
  const changeLanguage = (code, name) => {
    dispatch(setLanguage(code, name));
    setTimeout(() => {
      initTranslate(store, true);
    }, 100);
  };

  return (
    <>
      <StatusBar
        backgroundColor="transparent"
        barStyle="dark-content"
        translucent
      />
      <ImageBackground
        style={styles.root}
        source={require('../../assets/images/intro3.png')}
        imageStyle={{
          opacity: 0.15,
          backgroundColor: 'rgba(255, 255, 255, 0.6)',
        }}>
        {!refresh ? (
          <View style={[styles.mainContainer, {paddingBottom: bottomSpacing}]}>
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                // marginBottom: 20,
              }}>
              <View
                style={{
                  marginTop: getStatusBarHeight() + (isSmallScreen ? 30 : 50),
                }}>
                <Image
                  style={{
                    width: logoSize.width,
                    height: logoSize.height,
                    resizeMode: 'contain',
                  }}
                  source={Images.q_img}
                />
              </View>
              <View style={{marginTop: isSmallScreen ? 5 : 10}}>
                <Image
                  source={Images.new_qeridoo_white}
                  style={{
                    height: isSmallScreen ? height / 25 : height / 20,
                    resizeMode: 'contain',
                  }}
                />
              </View>
              <View
                style={{
                  marginVertical: isSmallScreen ? 15 : 20,
                }}>
                <Image
                  style={{
                    width: sloganSize.width,
                    height: sloganSize.height,
                    resizeMode: 'contain',
                  }}
                  source={Images.slogan}
                />
              </View>
            </View>
            <View
              style={{
                justifyContent: 'flex-end',
              }}>
              <View
                style={{
                  marginVertical: isSmallScreen ? 15 : 20,
                  paddingHorizontal: 20,
                }}>
                <Text
                  style={{
                    textAlign: 'center',
                    fontFamily: FontFamily.regular,
                    fontSize: isSmallScreen ? 16 : 18,
                    lineHeight: isSmallScreen ? 20 : 22,
                    color: BaseColor.textColor,
                  }}>
                  {`Customize your Qeridoo experience, specifically for you and your \n little one's adventures.`}
                </Text>
              </View>
              <Text
                style={{
                  marginBottom: isSmallScreen ? 15 : 20,
                  textAlign: 'center',
                  fontFamily: FontFamily.robotoLight,
                  fontSize: isSmallScreen ? 14 : 16,
                  color: BaseColor.textColor,
                }}>
                {`Let’s begin..`}
              </Text>
            </View>
            <View
              style={{
                marginHorizontal: 20,
                marginTop: 20,
                flexDirection: 'row',
                justifyContent: 'space-around',
              }}>
              <CButton
                anim
                title={translate('loginToSignup')}
                style={styles.getStrtedBtn}
                titleStyle={{color: BaseColor.primary}}
                onPress={() => {
                  navigation.navigate('Signup');
                }}
              />
              <CButton
                title={translate('loginBtn')}
                style={styles.getStrtedBtn}
                titleStyle={styles.loginTxt}
                onPress={() => {
                  navigation.navigate('Login');
                }}
              />
            </View>
          </View>
        ) : null}
        {!refresh ? (
          <>
            <View style={{marginBottom: 40, marginHorizontal: width / 9}}>
              <MultiStepSlider />
            </View>
            <View
              style={[
                styles.container,
                {
                  position: 'absolute',
                  bottom: bottomSpacing - 20,
                  left: 0,
                  right: 0,
                  zIndex: 1000,
                  elevation: 1000, // For Android
                },
              ]}>
              <TouchableOpacity
                style={styles.langBtn}
                activeOpacity={0.7}
                onPress={() => {
                  setlangModal(true);
                }}>
                <SvgXml xml={selectedLanguage.svg} width={20} height={20} />
                <Text
                  style={{
                    color: BaseColor.blackColor,
                    marginHorizontal: 8,
                    fontFamily: FontFamily.default,
                  }}>
                  {selectedLanguage.title}
                </Text>
                <CustomIcon name="expand-button" color={BaseColor.primary} />
              </TouchableOpacity>
            </View>
          </>
        ) : null}
        <Modal
          transparent
          style={{flex: 1}}
          visible={langModal}
          animationType="fade">
          <TouchableOpacity
            style={{flex: 1}}
            onPress={() => {
              setlangModal(false);
            }}>
            <View style={[styles.modalCont, {bottom: bottomSpacing}]}>
              {flagsArr.map((item, index) => (
                <TouchableOpacity
                  key={`${index}`}
                  style={styles.langBtn}
                  activeOpacity={0.7}
                  onPress={() => {
                    setlangModal(false);
                    setTimeout(() => {
                      setRefresh(true);
                    }, 500);
                    setTimeout(() => {
                      changeLanguage(item.code, item.title);
                    }, 2000);
                    setselectedLanguage(item);
                  }}>
                  <View style={styles.flagDesign}>
                    <SvgXml xml={item.svg} width={20} height={20} />
                    <Text
                      style={{
                        color: BaseColor.blackColor,
                        marginHorizontal: 8,
                        fontFamily: FontFamily.default,
                      }}>
                      {item.title}
                    </Text>
                  </View>
                  <CustomIcon
                    name="expand-button"
                    color={BaseColor.blackColor}
                  />
                </TouchableOpacity>
              ))}
            </View>
          </TouchableOpacity>
        </Modal>

        <Modal
          transparent
          style={{flex: 1}}
          visible={refresh}
          animationType="fade">
          <TouchableOpacity
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: BaseColor.white50,
            }}
            onPress={() => setRefresh(false)}>
            <View
              style={{
                padding: 12,
                backgroundColor: BaseColor.whiteColor,
                borderRadius: 12,
              }}>
              <Text
                style={{
                  fontFamily: FontFamily.default,
                  fontWeight: 'bold',
                  marginBottom: 12,
                }}>
                Changing language
              </Text>
              <ActivityIndicator size="small" color={BaseColor.blueDark} />
            </View>
          </TouchableOpacity>
        </Modal>
      </ImageBackground>
    </>
  );
};

export default RedirectLS;
