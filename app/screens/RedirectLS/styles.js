/* eslint-disable quotes */
import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const {width, height} = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
  },
  container: {
    marginHorizontal: 20,
    justifyContent: 'flex-end',
  },
  // row: {
  //   flexDirection: 'row',
  //   width: '100%',
  // },
  getStrtedBtn: {
    width: '45%',
  },
  loginBtn: {
    flex: 1,
    marginStart: 8,
    borderWidth: 1,
    borderColor: BaseColor.primary,
    backgroundColor: BaseColor.primary,
    height: '35%',
    borderRadius: 29,
  },
  signupBtn: {
    flex: 1,
    marginEnd: 8,
    height: '35%',
    borderRadius: 29,
    borderColor: BaseColor.primary,
    borderWidth: 1.5,
  },
  loginTxt: {
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.robotoLight,
    fontSize: 14,
    fontWeight: '300',
  },
  signupTxt: {
    color: BaseColor.primary3,
    fontFamily: FontFamily.robotoLight,
    fontSize: 14,
    fontWeight: '300',
  },
  langBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 8,
  },
  modalCont: {
    backgroundColor: BaseColor.whiteColor,
    position: 'absolute',
    // bottom: 24,
    bottom: 40,
    alignSelf: 'center',
    borderRadius: 16,
    padding: 12,
    margin: 25,
  },
  flagDesign: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    minWidth: 140,
  },
  welcome: {
    fontSize: 17,
    color: BaseColor.primary,
    fontFamily: FontFamily.regular,
    lineHeight: 25,
  },
});

export default styles;
