/* eslint-disable quotes */
import {Dimensions, StyleSheet, Platform} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const {width, height} = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
    minHeight: height, // Ensure full height coverage
  },
  container: {
    marginHorizontal: 20,
    justifyContent: 'center',
    alignItems: 'center',
    // Remove flex-end to prevent conflicts with absolute positioning
  },
  // row: {
  //   flexDirection: 'row',
  //   width: '100%',
  // },
  getStrtedBtn: {
    width: '45%',
  },
  loginBtn: {
    flex: 1,
    marginStart: 8,
    borderWidth: 1,
    borderColor: BaseColor.primary,
    backgroundColor: BaseColor.primary,
    height: '35%',
    borderRadius: 29,
  },
  signupBtn: {
    flex: 1,
    marginEnd: 8,
    height: '35%',
    borderRadius: 29,
    borderColor: BaseColor.primary,
    borderWidth: 1.5,
  },
  loginTxt: {
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.robotoLight,
    fontSize: 14,
    fontWeight: '300',
  },
  signupTxt: {
    color: BaseColor.primary3,
    fontFamily: FontFamily.robotoLight,
    fontSize: 14,
    fontWeight: '300',
  },
  langBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 8,
    backgroundColor: BaseColor.whiteColor,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    // Add shadow for better visibility
    ...Platform.select({
      ios: {
        shadowColor: BaseColor.blackColor,
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  modalCont: {
    backgroundColor: BaseColor.whiteColor,
    position: 'absolute',
    alignSelf: 'center',
    borderRadius: 16,
    padding: 12,
    margin: 25,
    maxHeight: height * 0.4, // Limit modal height
    // Add shadow for better visibility
    ...Platform.select({
      ios: {
        shadowColor: BaseColor.blackColor,
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.2,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  flagDesign: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    minWidth: 140,
  },
  welcome: {
    fontSize: 17,
    color: BaseColor.primary,
    fontFamily: FontFamily.regular,
    lineHeight: 25,
  },
});

export default styles;
