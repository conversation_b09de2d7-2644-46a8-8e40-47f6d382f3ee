import React, {memo, useRef} from 'react';
import {useEffect} from 'react';
import {BackHandler, Dimensions, StatusBar, Text} from 'react-native';
import {View} from 'react-native-animatable';
import styles from './styles';
import Toast from 'react-native-simple-toast';
import MultiStepSlider from '../../components/MultiSlider/MultiSlider';
import commonSvg from '../../assets/commonSvg/commonnSvg';
import {SvgXml} from 'react-native-svg';
import CButton from '../../components/CButton';
import {translate} from '../../lang/Translate';
let backPressed = 0;

const {width, height} = Dimensions.get('window');
function RegisterSucess({navigation}) {
  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={styles.root}>
      <SvgXml xml={commonSvg.Sucess} width={width} height={height / 1.8} />
      <View
        style={[
          styles.textContainer,
          {
            top: height / 2.3,
          },
        ]}>
        <Text style={styles.regitserTxt}>{translate('youregister')}</Text>
        <Text style={styles.childtxt}>{`${translate(
          'addChildProfile',
        )} >`}</Text>
      </View>
      <View style={styles.btnContainer}>
        <CButton
          style={{width: '45%'}}
          title={translate('setupProfile')}
          onPress={() => {
            navigation.push('ChildInfo', {type: 'registerSucess'});
          }}
        />
        <Text
          style={styles.skiptxt}
          onPress={() => navigation.push('DrawerNav')}>
          {translate('Skip for now')}
        </Text>
      </View>
      <View style={{marginBottom: 30, marginHorizontal: width / 9}}>
        <MultiStepSlider />
      </View>
    </View>
  );
}

export default RegisterSucess;
