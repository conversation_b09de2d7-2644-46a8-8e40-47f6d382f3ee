import React, { useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NativeModules, NativeEventEmitter } from 'react-native';

function SampleCheckIOS() {
    const { NutSdkModule } = NativeModules;
    const nutSdkEmitter = new NativeEventEmitter(NutSdkModule);

    console.log("Get All Native Function-->>", NutSdkModule);

    const checkinngReturnComingFromNativeModule = async () => {
        try {
            const response = await NutSdkModule.createEventInNativeIOS("Ashar", "Nadiad");
            console.log("Response : ", response);
            Alert.alert("Response : ", response);
        } catch (error) {
            console.error("Error : ", error);
        }
    };

    const startScanning = () => {
        try {
            NutSdkModule.startScan();
            console.log("Scanning started");
        } catch (error) {
            console.error("Error starting scan: ", error);
        }
    };

    useEffect(() => {
        const handleDiscoveredDevice = (deviceData) => {
            console.log('Discovered Device:', deviceData);
            Alert.alert('Discovered Device', JSON.stringify(deviceData));
        };

        nutSdkEmitter.addListener('discoveredDevice', handleDiscoveredDevice);

        // Clean up the event listener on component unmount
        return () => {
            nutSdkEmitter.removeAllListeners('discoveredDevice');
        };
    }, []);

    return (
        <SafeAreaView>
            <View>
                <Text style={{ fontSize: 20, textAlign: 'center' }}>IOS Native Module Check</Text>
                <Button title="Check Native" onPress={checkinngReturnComingFromNativeModule} />
                <Button title="Start Scan" onPress={startScanning} />
            </View>
        </SafeAreaView>
    );
}

export default SampleCheckIOS;
