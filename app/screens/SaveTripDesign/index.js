import React, {useEffect, useState} from 'react';
import {
  Dimensions,
  ImageBackground,
  Modal,
  Text,
  View,
  StyleSheet,
  FlatList,
  TextInput,
  Clipboard,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import BaseColor from '../../config/colors';
import GetLocation from 'react-native-get-location';
import {sendErrorReport} from '../../utils/commonFunction';
import {FontFamily} from '../../config/typography';
import CButton from '../../components/CButton';
import {CustomIcon} from '../../config/LoadIcons';
import PlaceAction from '../../redux/reducers/place/actions';
import {useDispatch, useSelector} from 'react-redux';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {Images} from '../../config/Images';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {parseString} from 'react-native-xml2js';
import commonSvg from '../../assets/commonSvg/commonnSvg';
import {SvgFromXml} from 'react-native-svg';
import Share from 'react-native-share';
import Feather from 'react-native-vector-icons/Feather';
import Toast from 'react-native-simple-toast';
import moment from 'moment';
import Iconin from 'react-native-vector-icons/Ionicons';
import FIcon from 'react-native-vector-icons/Feather';
import ReviewModal from '../../components/ReviewModal/ReviewModal';
import MapView, {Marker, Polyline, PROVIDER_GOOGLE} from 'react-native-maps';
import mapStyle from '../../config/mapCustomStyle';
import {translate} from '../../lang/Translate';

const {width, height} = Dimensions.get('window');
function SaveTrip({navigation, route}) {
  const {setPlaceLocation, setTripPlaceName} = PlaceAction;
  const {userData, accessToken} = useSelector(state => state.auth);
  const {
    currentTripName,
    currentTripDuration,
    currentTripTravelledDistance,
    currentTripPlaceDetails,
    currentTripId,
  } = useSelector(state => state.trip);
  const {placeTripName} = useSelector(state => state.place);
  const dispatch = useDispatch();
  const [shareModal, setShareModal] = useState(false);
  const [shareUrl, setShareUrl] = useState(
    'https://qeridoo.com/user-trip/9876jhgsd-wet-45',
  );
  const staticArray = [
    {
      name: 'Distance',
      value: '0',
    },
    {
      name: 'Time',
      value: '0',
    },
    {
      name: 'Elev Gain',
      value: '0',
    },
  ];
  const [loc, setLoc] = useState({});
  const [loader, setLoader] = useState(false);
  const [tripArray, setTripArray] = useState(staticArray);
  const [detailsData, setDetailsData] = useState({});
  const [isReviewModal, setIsReviewModal] = useState(false);
  const [remainingPath, setRemainingPath] = useState([]);
  const [region, setRegion] = useState(null);
  const [mapLoder, setMapLoder] = useState(false);
  const [disableCurrent, setDisableCurrent] = useState(false);

  // Add function to calculate dynamic delta values
  const calculateDynamicDelta = coordinates => {
    if (!coordinates || coordinates.length === 0) {
      return {latitudeDelta: 0.005, longitudeDelta: 0.005};
    }

    // Find the min and max lat/lng values
    let minLat = coordinates[0].latitude;
    let maxLat = coordinates[0].latitude;
    let minLng = coordinates[0].longitude;
    let maxLng = coordinates[0].longitude;

    coordinates.forEach(coord => {
      minLat = Math.min(minLat, coord.latitude);
      maxLat = Math.max(maxLat, coord.latitude);
      minLng = Math.min(minLng, coord.longitude);
      maxLng = Math.max(maxLng, coord.longitude);
    });

    // Calculate the deltas with some padding (20%)
    const latDelta = (maxLat - minLat) * 1.2;
    const lngDelta = (maxLng - minLng) * 1.2;

    // Ensure minimum values for very small routes
    return {
      latitudeDelta: Math.max(latDelta, 0.001),
      longitudeDelta: Math.max(lngDelta, 0.001),
    };
  };

  // save trip API Integration
  const saveTrip = async () => {
    setLoader(true);
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };
    const data = {
      trip_id: currentTripId,
      user_id: userData.id,
      trip_name: placeTripName,
      travel_distance: currentTripTravelledDistance,
      duration: currentTripDuration,
    };
    if (currentTripPlaceDetails?.placeName === 'N/A') {
      data.trip_type = 'random_trip';
    } else {
      data.trip_type = 'custom_trip';
      if (currentTripPlaceDetails) {
        currentTripPlaceDetails.placeReview = [];
        data.placeDetails = {
          ...currentTripPlaceDetails,
        };
      }
    }
    try {
      const response = await getApiData(
        BaseSetting.endpoints.saveTrip,
        'POST',
        data,
        headers,
      );
      if (response.success) {
        setIsReviewModal(true);
      } else {
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
    }
  };
  // End

  // trip details API Integration
  const tripDetails = async () => {
    try {
      const url = `${BaseSetting.endpoints.tripDetails}?trip_id=${currentTripId}`;
      const response = await getApiData(url, 'GET');
      if (response.success) {
        if (response?.data) {
          const seconds = response?.data?.trip_duration;
          const hours = Math.floor(seconds / 3600);
          const minutes = Math.floor((seconds % 3600) / 60);
          const sec = Math.floor(seconds % 60);
          const arr = [
            {
              name: 'Distance',
              value: response?.data?.trip_distance,
            },
            {
              name: 'Time',
              value: `${hours} hr ${minutes} min ${sec} sec`,
            },
            {
              name: 'Elev Gain',
              value: response?.data?.elevation_gain,
            },
          ];
          setTripArray(arr);
          setDetailsData(response?.data);
        }
      }
    } catch (error) {
      console.log('error', error);
    }
  };
  // End

  async function getCurrentLocation() {
    GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: 0,
    })
      .then(location => {
        sendErrorReport(location, 'SMS_location');
        setLoc(location);
      })
      .catch(async error => {
        const {code, message} = error;
        sendErrorReport(code, 'SMS_code');
        if (code === 'UNAVAILABLE') {
          Toast.show(
            'Please enable your location service to send emergency alert.',
          );
          GetLocation.openAppSettings();
        }
      });
  }

  useEffect(() => {
    getCurrentLocation();
    tripDetails();
  }, []);

  const shareOption = [
    {name: 'Twitter', icon: 'twitter'},
    {name: 'Facebook', icon: 'facebook'},
    {name: 'Instagram', icon: 'instagram'},
    {name: 'Whatsapp', icon: 'whatsapp'},
  ];

  const renderItem = ({item, index}) => {
    return (
      <View
        style={{
          marginBottom: 30,
        }}>
        <View
          style={{
            alignItems: 'center',
          }}>
          <Text
            style={{
              fontSize: 16,
              fontFamily: FontFamily.bold,
              color: BaseColor.primary,
              marginBottom: 15,
            }}>
            {item.name}
          </Text>
          <Text
            style={{
              fontSize: 16,
              color: '#5F7B7B',
              fontFamily: FontFamily.regular,
            }}>
            {item.value}
          </Text>
        </View>
      </View>
    );
  };

  const copyToClipboard = () => {
    Clipboard.setString(shareUrl);
  };

  const handleShare = async platform => {
    try {
      const shareOptions = {
        title: 'Share via',
        message: `Check out my trip on Qeridoo!\n${
          currentTripName || 'My Trip'
        }\nDistance: ${currentTripTravelledDistance || '0'} km\nDuration: ${
          currentTripDuration || '0'
        } minutes`,
        url: shareUrl,
      };

      if (platform === 'whatsapp') {
        await Share.shareSingle({
          ...shareOptions,
          social: Share.Social.WHATSAPP,
        });
      } else if (platform === 'facebook') {
        await Share.shareSingle({
          ...shareOptions,
          social: Share.Social.FACEBOOK,
        });
      } else if (platform === 'twitter') {
        await Share.shareSingle({
          ...shareOptions,
          social: Share.Social.TWITTER,
        });
      } else if (platform === 'instagram') {
        await Share.shareSingle({
          ...shareOptions,
          social: Share.Social.INSTAGRAM,
        });
      }

      const result = await Share.share(shareOptions);

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
          console.log('shared with activity type');
        } else {
          // shared
          console.log('shared');
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
        console.log('dismissed');
      }
    } catch (error) {
      console.log('+ error.message', error.message);
      Toast.show('Error sharing: ' + error.message);
    }
  };

  useEffect(() => {
    if (detailsData?.gpx_file_url) {
      const fetchGPX = async () => {
        setMapLoder(true);
        try {
          const response = await fetch(detailsData?.gpx_file_url);
          const gpxData = await response.text();

          parseString(gpxData, (err, result) => {
            if (err || !result?.gpx?.trk?.[0]?.trkseg?.[0]?.trkpt) {
              console.error('Error parsing GPX data:', err || 'Invalid format');
              return;
            }

            // Extract track points from all segments
            const trackPoints = result.gpx.trk[0].trkseg.map(segment =>
              segment.trkpt.map(point => ({
                latitude: parseFloat(point.$.lat),
                longitude: parseFloat(point.$.lon),
              })),
            );
            if (trackPoints.length > 0) {
              setRemainingPath(trackPoints);
              const rawTrackPoints = trackPoints.flat();
              if (rawTrackPoints.length <= 1) {
                setDisableCurrent(true);
              } else {
                setDisableCurrent(false);
              }
              // Calculate center point of the route
              const centerLat =
                rawTrackPoints.reduce((sum, point) => sum + point.latitude, 0) /
                rawTrackPoints.length;
              const centerLng =
                rawTrackPoints.reduce(
                  (sum, point) => sum + point.longitude,
                  0,
                ) / rawTrackPoints.length;
              // Calculate dynamic delta values
              const deltas = calculateDynamicDelta(rawTrackPoints);
              setMapLoder(false);

              setRegion({
                latitude: centerLat,
                longitude: centerLng,
                latitudeDelta: deltas?.latitudeDelta,
                longitudeDelta: deltas?.longitudeDelta,
              });
            } else {
              setMapLoder(false);
              console.error('No valid track points found in the GPX data.');
            }
          });
        } catch (error) {
          setMapLoder(false);
          console.error('Error fetching GPX:', error);
        }
      };

      fetchGPX();
    }
  }, [detailsData]);

  useEffect(() => {
    if (detailsData) {
      setDetailsData(detailsData);
    }
  }, [detailsData]);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: BaseColor.whiteColor,
      }}>
      <CustomIcon
        name="left-arrow-thin"
        size={25}
        color={BaseColor.primary}
        style={{marginHorizontal: 20, marginTop: getStatusBarHeight() + 20}}
        onPress={() => {
          navigation.navigate(translate('dashboard'));
        }}
      />
      <ImageBackground
        style={{width: '100%', height: height / 9, marginBottom: 20}}
        source={Images.tripQeridoo}
        imageStyle={{resizeMode: 'contain'}}>
        <Text style={styles.titleText}>
          {`Congratulations ${userData?.full_name}, \n on completing a ${
            detailsData?.trip_distance
          }-${detailsData?.distance_unit} ${
            detailsData?.location_type || ''
          } trail!`}
        </Text>
      </ImageBackground>
      <View>
        <View
          style={{
            borderRadius: 10,
            overflow: 'hidden',
            shadowColor: '#000',
            height: Dimensions.get('window').height / 2.8,
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.25,
            shadowRadius: 3.84,
            elevation: 2,
            backgroundColor: BaseColor.whiteColor,
            position: 'relative',
            marginHorizontal: 10,
          }}>
          {mapLoder ? (
            <ActivityIndicator
              style={{justifyContent: 'center', alignItems: 'center', flex: 1}}
            />
          ) : (
            <MapView
              provider={PROVIDER_GOOGLE}
              style={[styles.map]}
              customMapStyle={mapStyle}
              region={region}
              pitchEnabled={false}
              rotateEnabled={false}
              scrollEnabled={true}
              zoomEnabled={true}>
              {remainingPath.length > 0 && (
                <Marker
                  coordinate={remainingPath[0][0]}
                  title="Start Point"
                  description="This is the starting location.">
                  <View style={styles.startMarker}>
                    <Iconin name="location" size={25} color="#5255F1" />
                  </View>
                </Marker>
              )}
              {remainingPath?.map((segment, index) => (
                <Polyline
                  key={index}
                  coordinates={segment}
                  strokeWidth={3}
                  strokeColor={'#FA6912'}
                />
              ))}
              {remainingPath.length === 1 && !disableCurrent && (
                <Marker
                  coordinate={remainingPath[0][remainingPath[0].length - 1]}
                  title="End Point">
                  <View style={[styles.markerImage]}>
                    <FIcon name={'navigation-2'} color={'#6a8889'} size={16} />
                  </View>
                </Marker>
              )}
              {remainingPath.length > 1 && (
                <Marker
                  coordinate={remainingPath[remainingPath.length - 1][0]}
                  title="End Point">
                  <View style={[styles.markerImage]}>
                    <FIcon name={'navigation-2'} color={'#6a8889'} size={16} />
                  </View>
                </Marker>
              )}
            </MapView>
          )}
        </View>
        <View style={{marginHorizontal: 50, marginVertical: 24}}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <Text style={styles.timeText}>{`Start: ${moment(
              detailsData?.trip_start_time,
            ).format('hh:mm a')}`}</Text>
            <Text style={styles.timeText}>{`End: ${moment(
              detailsData?.trip_end_time,
            ).format('hh:mm a')}`}</Text>
          </View>
          <View
            style={{
              marginTop: 23,
              justifyContent: 'space-between',
            }}>
            <FlatList
              data={tripArray}
              renderItem={renderItem}
              numColumns={2}
              columnWrapperStyle={{justifyContent: 'space-between'}}
            />
            <SvgFromXml
              xml={commonSvg.tripSave}
              width={40}
              height={40}
              style={{position: 'absolute', left: width / 3, top: 30}}
            />
          </View>
          <View style={styles.buttonContainer}>
            <CButton
              title={'Save Trip'}
              iconBg={BaseColor.whiteColor}
              titleStyle={{
                fontFamily: FontFamily.bold,
              }}
              onPress={() => {
                saveTrip();
              }}
              loader={loader}
            />
            <CButton
              title="Share Achievement! "
              style={styles.buttonStyle}
              titleStyle={styles.btntitleStyle}
              rightCustomIcon
              rightIcon={'achivemetns'}
              rightIconColor={BaseColor.primary}
              rightIconSize={24}
              onPress={() => setShareModal(true)}
              anim
            />
          </View>
        </View>
      </View>
      <ReviewModal
        visible={isReviewModal}
        navigation={navigation}
        setVisible={setIsReviewModal}
        tripDetailData={detailsData}
        savedTrip
      />
      <Modal
        visible={shareModal}
        transparent
        style={{
          flex: 1,
        }}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <CustomIcon
              onPress={() => {
                setShareModal(false);
              }}
              name="cancle"
              style={{
                color: BaseColor.primary,
                alignSelf: 'flex-end',
              }}
              size={18}
            />
            <Text style={styles.shareText}>Share this post</Text>
            <Text style={styles.contentStyle}>
              If you like this post share it with your friends
            </Text>
            <View style={styles.socialContainer}>
              {shareOption &&
                shareOption.map(li => {
                  return (
                    <TouchableOpacity
                      activeOpacity={0.8}
                      style={{alignItems: 'center'}}
                      onPress={() => handleShare(li.icon)}>
                      <View style={[styles.socialIconContainer]}>
                        <CustomIcon
                          name={li.icon}
                          size={20}
                          color={'#222222'}
                        />
                      </View>
                      <Text style={{fontSize: 10, color: '#567B79'}}>
                        {li.name}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
            </View>
            <View>
              <TextInput
                editable={false}
                style={styles.textInputStyle}
                value={shareUrl}
              />
              <View
                style={{
                  position: 'absolute',
                  right: 15,
                  top: 10,
                }}>
                <Feather
                  name="copy"
                  size={18}
                  color="#567B79"
                  onPress={() => {
                    copyToClipboard();
                    Toast.show('Share URL copied to clipboard');
                  }}
                />
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  customView: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 10,
    width: 200,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  description: {
    fontSize: 12,
  },
  titleText: {
    fontSize: 16,
    fontFamily: FontFamily.bold,
    textAlign: 'center',
    color: BaseColor.primary,
    paddingTop: 25,
  },
  timeText: {
    fontSize: 14,
    color: '#096761',
    fontFamily: FontFamily.default,
  },
  buttonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    marginHorizontal: 10,
  },
  buttonStyle: {
    marginTop: 20,
    borderWidth: 1.5,
    borderColor: BaseColor.primary,
  },
  btntitleStyle: {
    fontSize: 16,
    color: BaseColor.primary,
    fontFamily: FontFamily.bold,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.23)',
    justifyContent: 'flex-end',
    marginBottom: 10,
  },
  modalContent: {
    backgroundColor: BaseColor.whiteColor,
    borderTopLeftRadius: 26,
    borderTopRightRadius: 26,
    padding: 20,
  },
  shareText: {
    fontSize: 20,
    fontFamily: FontFamily.robotoLight,
    color: BaseColor.blackColor,
    marginBottom: 5,
    fontWeight: '700',
  },
  contentStyle: {
    fontSize: 10,
    fontFamily: FontFamily.robotoLight,
    color: '#888888',
  },
  textInputStyle: {
    borderWidth: 1,
    borderColor: '#C0CDCD',
    height: 40,
    borderRadius: 90,
    paddingHorizontal: 10,
    fontSize: 11,
  },
  socialContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 20,
    marginTop: 30,
  },
  socialIconContainer: {
    width: 55,
    height: 55,
    // padding: 25,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(34, 34, 34, 0.06)',
    borderRadius: 50,
    marginVertical: 10,
  },
  map: {height: height / 2.8, width: '100%'},
  saveTripPoint: {
    width: 12,
    height: 12,
    borderRadius: 10,
    backgroundColor: '#87B5B3',
  },
  markerImage: {
    width: 30,
    height: 30,
    borderRadius: 30,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
  startMarker: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#bfbdfc',
  },
});

export default SaveTrip;
