import React, {useEffect, useRef, useState} from 'react';
import {
  AppState,
  BackHandler,
  Image,
  ImageBackground,
  StatusBar,
  Text,
  View,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import BaseColor from '../../config/colors';
import GetLocation from 'react-native-get-location';
import {sendErrorReport} from '../../utils/commonFunction';
import {FontFamily} from '../../config/typography';
import Iconin from 'react-native-vector-icons/Ionicons';
import CButton from '../../components/CButton';
import {translate} from '../../lang/Translate';
import {useFocusEffect} from '@react-navigation/native';
import CInput from '../../components/CInput';
import ReviewModal from '../../components/ReviewModal/ReviewModal';
import {CustomIcon} from '../../config/LoadIcons';
import PlaceAction from '../../redux/reducers/place/actions';
import TripAction from '../../redux/reducers/trip/actions';
import {useDispatch, useSelector} from 'react-redux';
import CustomHeader from '../../components/CustomHeader/CustomHeader';
import {getApiData} from '../../utils/apiHelper';
import Toast from 'react-native-simple-toast';
import BaseSetting from '../../config/setting';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import GPXMapScreen from '../../components/MapRendering';
import BackgroundGeolocation from 'react-native-background-geolocation';

function SearchedTrip({navigation, route}) {
  const {
    setPlaceLocation,
    setTripPlaceName,
    setTimerState,
    setIsPause,
    setDirectTripStart,
  } = PlaceAction;
  const {setTripStartDate, setTripId} = TripAction;
  const {placeTripName, placeTitle} = useSelector(state => state.place);
  const {currentTripId} = useSelector(state => state.trip);
  const {accessToken, userLatLong} = useSelector(state => state.auth);
  const dispatch = useDispatch();
  const tripEnd = route?.params?.tripEnd ?? false;
  const gpxId = route?.params?.gpxId || '';
  const [isEdit, setIsEdit] = useState(false);
  const [tripName, setTripName] = useState(placeTripName || '');
  const [placeDetails, setPlaceDetails] = useState(
    route?.params?.locationDetail || {},
  );
  const [currentPosition, setCurrentPosition] = useState({});
  const [detailsResponce, setDetailsResponce] = useState({});
  const [isBookmarked, setIsBookmarked] = useState(false);

  // Back Navigation
  const shouldNavigateToHomeRef = useRef(false);

  useFocusEffect(() => {
    const onBackPress = () => {
      if (shouldNavigateToHomeRef.current) {
        navigation.navigate(translate('home'));
        return true;
      } else {
        navigation.navigate(translate('dashboard'));
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      onBackPress,
    );

    return () => backHandler.remove();
  });

  useEffect(() => {
    const unsubscribe = navigation.addListener('state', e => {
      const currentRouteName = e.data.state.routes.slice(-1)[0].name;
      if (currentRouteName === 'SearchTrip') {
        // Update the flag indicating that pressing back should navigate to Home
        shouldNavigateToHomeRef.current = true;
      } else {
        shouldNavigateToHomeRef.current = false;
      }
    });

    return unsubscribe;
  }, [navigation]);

  // Back Navigation End--
  const [loc, setLoc] = useState({});
  const [appState, setAppState] = useState(true);

  // ------App state
  useEffect(() => {
    console.log('App ddddd');
    const handleAppStateChange = nextAppState => {
      console.log(`App State: ${nextAppState}`);
      if (nextAppState === 'active') {
        console.log('App State======', true);
        // sendErrorReport(true, "app_state_active");
        setAppState(true);
      } else if (nextAppState === 'background') {
        console.log('App State======', false);
        // sendErrorReport(false, "app_state_background");
        setAppState(false);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => subscription.remove();
  }, []);

  async function getCurrentLocation() {
    GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: !appState ? 15000 : 0,
    })
      .then(location => {
        sendErrorReport(location, 'SMS_location');
        setLoc(JSON.parse(location));
      })
      .catch(async error => {
        const {code, message} = error;
        sendErrorReport(code, 'SMS_code');
        if (code === 'UNAVAILABLE') {
          Toast.show(
            'Please enable your location service to send emergency alert.',
          );
          GetLocation.openAppSettings();
        }
      });
  }

  // API for get Details of Searched Page...
  const getDetails = async () => {
    try {
      const url = BaseSetting.endpoints.gpxMapDetail + `?gpx_id=${gpxId}`;
      const response = await getApiData(url, 'GET');
      if (response?.success) {
        setDetailsResponce(response?.data);
      } else {
        Toast.show(response?.message);
      }
    } catch (error) {
      console.log('error for device list ===', error);
      sendErrorReport(error, 'saving_trip_name');
    }
  };
  // End

  useEffect(() => {
    getCurrentLocation();
    if (gpxId) {
      getDetails(); // Details Trip
    }
  }, [gpxId]);

  //created review modal
  const [loader, setLoader] = useState(false);

  const saveAndStartTrip = async (ty = '') => {
    try {
      const data = {
        trip_name: tripName,
        start_date: new Date().toISOString().slice(0, 10),
        lat: loc?.latitude || '',
        lng: loc?.longitude || '',
      };
      if (ty === 'edit') {
        data.trip_id = currentTripId;
      }
      const response = await getApiData(
        BaseSetting.endpoints.saveTripName,
        'POST',
        data,
        '',
        true,
      );
      console.log('🚀 ~ saveAndStartTrip ~ response:', response);

      if (response?.success) {
        dispatch(setTripStartDate(new Date().toISOString().slice(0, 10)));
        dispatch(setTripId(response?.data?.id));
        setIsEdit(!isEdit);
      } else {
        console.log(
          '🚀 ~ file: index.js ~ line 15ewe ~ getBadgeCount ~ response',
          response,
        );
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      console.log('error for device list ===', error);
      sendErrorReport(error, 'saving_trip_name');
    } finally {
      setLoader(false);
    }
  };

  // start trip api integration..
  const startTrip = async () => {
    setLoader(true);
    try {
      const data = {
        trip_id: currentTripId,
        lat: loc?.latitude || userLatLong?.latitude || '',
        lng: loc?.longitude || userLatLong?.longitude || '',
      };
      const response = await getApiData(
        BaseSetting.endpoints.startTrip,
        'POST',
        data,
        '',
        true,
      );
      if (response?.success) {
        const state = await BackgroundGeolocation.getState();
        if (!state.enabled) {
          await BackgroundGeolocation.start();
        }

        // Force immediate tracking
        await BackgroundGeolocation.changePace(true);
        dispatch(setTripPlaceName(tripName));
        dispatch(setTimerState(true));
        navigation.navigate(translate('dashboard'), {
          locationDetail: placeDetails,
          detais: detailsResponce,
        });
        dispatch(
          setPlaceLocation({
            latitude: placeDetails.latitude,
            longitude: placeDetails.longitude,
          }),
        );
        Toast.show(response?.message);
      } else {
        Toast.show(response?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      console.log('error for device list ===', error);
      sendErrorReport(error, 'saving_trip_name');
    } finally {
      setLoader(false);
    }
  };
  // end trip api integration..

  const formatReviewCount = count => {
    if (count >= 1000000) {
      return (count / 1000000).toFixed(1) + 'M';
    } else if (count >= 1000) {
      return (count / 1000).toFixed(1) + 'K';
    }
    return count.toString();
  };

  // Location Array...
  const array = [
    {
      name: 'Rating',
      icon: 'star',
      value: detailsResponce?.rating
        ? `${detailsResponce?.rating} (${formatReviewCount(
            detailsResponce?.rating,
          )})`
        : 'N/A',
    },
    {
      name: 'Distance',
      icon: 'distance-stats',
      value: detailsResponce?.distance ? `${detailsResponce?.distance}` : 'N/A',
    },
    {
      name: 'Dest.',
      icon: 'location-pin-outline',
      value: 'N/A',
    },
  ];

  return (
    <View style={styles.container}>
      <StatusBar translucent />
      <View
        style={{
          marginTop: getStatusBarHeight() + 20,
        }}
      />
      <ScrollView
        contentContainerStyle={[
          {
            flexGrow: 1,
          },
        ]}
        bounces={false}
        showsVerticalScrollIndicator={false}>
        <View>
          <View
            style={{
              zIndex: 1,
              position: 'absolute',
              top: 0,
            }}>
            <CustomHeader
              transparentView
              leftIconName="left-arrow"
              borderCircular
              onLeftPress={() => {
                navigation.goBack();
              }}
              backBtn
            />
          </View>
          <View style={styles.mapContainer}>
            <GPXMapScreen
              uri={detailsResponce?.file_url}
              searchedTrip
              getCurrentPositions={position => setCurrentPosition(position)}
              mapContaierStyle={styles.mapStyle}
            />
          </View>

          {tripEnd && (
            <>
              <TouchableOpacity
                style={[
                  styles.bookmarkContainer,
                  {
                    borderColor: isBookmarked
                      ? BaseColor.primary2
                      : BaseColor.whiteColor,
                  },
                ]}
                onPress={() => setIsBookmarked(!isBookmarked)}>
                <CustomIcon
                  name={
                    isBookmarked
                      ? 'bookmark-selected'
                      : 'bookmark-unselectedsvg'
                  }
                  size={18}
                  color={BaseColor.primary}
                />
              </TouchableOpacity>
              <View style={styles.mainTouchContainer}>
                <TouchableOpacity
                  style={{
                    backgroundColor: BaseColor.whiteColor,
                    paddingHorizontal: 6,
                    paddingVertical: 4,
                    borderRadius: 8,
                    borderBottomLeftRadius: 0,
                    borderBottomRightRadius: 0,
                  }}>
                  <ImageBackground
                    source={require('../../assets/bookmarkDummyImages/i4.png')}
                    style={{
                      height: 50,
                      width: 50,
                      borderRadius: 8,
                      overflow: 'hidden',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <Iconin
                      name="play"
                      size={30}
                      color={BaseColor.whiteColor}
                    />
                  </ImageBackground>
                </TouchableOpacity>
                <TouchableOpacity
                  style={{
                    backgroundColor: BaseColor.whiteColor,
                    paddingHorizontal: 6,
                    paddingVertical: 4,
                    borderRadius: 8,
                  }}>
                  <Image
                    source={require('../../assets/bookmarkDummyImages/i1.png')}
                    style={{
                      height: 62,
                      width: 62,
                      borderRadius: 8,
                    }}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={{
                    backgroundColor: BaseColor.whiteColor,
                    paddingHorizontal: 6,
                    paddingVertical: 4,
                  }}>
                  <Image
                    source={require('../../assets/bookmarkDummyImages/i2.png')}
                    style={{
                      height: 50,
                      width: 50,
                      borderRadius: 8,
                    }}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={{
                    backgroundColor: BaseColor.whiteColor,
                    paddingHorizontal: 6,
                    paddingVertical: 4,
                    borderRadius: 8,
                    borderTopLeftRadius: 0,
                    borderTopRightRadius: 0,
                  }}>
                  <ImageBackground
                    source={require('../../assets/bookmarkDummyImages/i3.png')}
                    style={{
                      height: 50,
                      width: 50,
                      borderRadius: 8,
                      overflow: 'hidden',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    blurRadius={20}>
                    <Text
                      style={{
                        fontFamily: 'Montserrat',
                        fontSize: 22,
                        color: BaseColor.whiteColor,
                      }}>
                      10+
                    </Text>
                  </ImageBackground>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>

        <View style={{marginHorizontal: 20, marginVertical: 24}}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            {isEdit ? (
              <View style={{flex: 1}}>
                <CInput
                  textInputWrapper={{
                    backgroundColor: '#F6F6F6',
                    borderWidth: 0,
                    height: 48,
                    borderRadius: 10,
                  }}
                  inputStyle={{
                    fontFamily: FontFamily.regular,
                    fontSize: 14,
                    color: BaseColor.textGrey1,
                  }}
                  placeholder="Type name here"
                  placeholderTextColor={BaseColor.textGrey}
                  value={tripName}
                  onChangeText={val => {
                    setTripName(val);
                  }}
                />
              </View>
            ) : (
              <Text
                style={{
                  color: BaseColor.primary,
                  fontSize: 18,
                  maxWidth: '90%',
                  fontFamily: FontFamily.regular,
                }}>
                {tripName}
              </Text>
            )}
            {isEdit && (
              <TouchableOpacity
                style={{
                  width: 35,
                  height: 35,
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderRadius: 30,
                  backgroundColor: BaseColor.whiteColor,
                  elevation: 1,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.25,
                  shadowRadius: 3.84,
                  marginHorizontal: 10,
                }}
                onPress={() => {
                  tripName
                    ? setIsEdit(false)
                    : setTripName(route?.params.tripName);
                }}>
                <Iconin
                  name="close-outline"
                  size={25}
                  color={BaseColor.primary}
                />
              </TouchableOpacity>
            )}
            <TouchableOpacity
              style={styles.editContainer}
              onPress={() => {
                isEdit
                  ? saveAndStartTrip('edit')
                  : tripName
                  ? setIsEdit(!isEdit)
                  : alert('Trip Name cannot be empty..');
              }}>
              {isEdit ? (
                <Iconin
                  name="checkmark-done-outline"
                  size={25}
                  color={BaseColor.primary}
                />
              ) : (
                <CustomIcon
                  name="pencil-1"
                  size={20}
                  color={BaseColor.primary}
                />
              )}
            </TouchableOpacity>
          </View>
          <View style={styles.textContainer}>
            <Text style={styles.placeText} numberOfLines={1}>
              {placeDetails?.title
                ? placeDetails.title
                : placeTitle
                ? placeTitle
                : ''}
            </Text>
          </View>
          {/* Icons Star Distance , Dest. */}
          <View style={styles.arrayContainer}>
            {array &&
              array.map(li => {
                return (
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <View style={styles.arrayIconContainer}>
                      <CustomIcon
                        name={li.icon}
                        size={20}
                        color={BaseColor.primary}
                      />
                    </View>
                    <View style={{marginLeft: 6}}>
                      <Text style={styles.name}>{li.name}</Text>
                      <Text style={styles.value}>{li.value}</Text>
                    </View>
                  </View>
                );
              })}
          </View>
          {/* Some Info  */}
          <View style={{marginTop: 32}}>
            <Text style={styles.description}>
              {detailsResponce?.description ||
                `Embarking on a new bike journey is akin to opening a fresh chapter filled with exhilaration, exploration, and personal growth. As you pedal away, each turn of the wheel marks not just the distance covered but also the unfolding of new landscapes, the embrace of the wind, and the rhythm of your heartbeat syncing with the serene or bustling surroundings. Enjoy!`}
            </Text>
          </View>
        </View>
      </ScrollView>
      <View style={styles.startBtnContainer}>
        <CButton
          title={'Start Trip'}
          iconBg={BaseColor.whiteColor}
          style={styles.btnStyle}
          titleStyle={styles.btnText}
          onPress={() => {
            startTrip();
          }}
          loader={loader}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
  },
  mapContainer: {
    marginVertical: 10,
    borderRadius: 10,
    overflow: 'hidden',
    shadowColor: '#000',
    height: Dimensions.get('window').height / 2.5,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 2,
    backgroundColor: BaseColor.whiteColor,
    position: 'relative',
    marginHorizontal: 10,
  },
  mapStyle: {
    marginHorizontal: 0,
    height: Dimensions.get('window').height / 2,
  },
  bookmarkContainer: {
    borderWidth: 1,
    backgroundColor: BaseColor.whiteColor,
    position: 'absolute',
    width: 38,
    height: 38,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
    top: 18,
    right: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  mainTouchContainer: {
    position: 'absolute',
    bottom: 27,
    left: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  editContainer: {
    width: 35,
    height: 35,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 30,
    backgroundColor: BaseColor.whiteColor,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  placeText: {
    color: BaseColor.textGrey1,
    fontSize: 15,
    marginLeft: 6,
    width: '90%',
    fontFamily: FontFamily.regular,
  },
  textContainer: {flexDirection: 'row', alignItems: 'center', marginTop: 6},
  arrayContainer: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 5,
  },
  arrayIconContainer: {
    width: 35,
    height: 35,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 30,
    backgroundColor: BaseColor.whiteColor,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  name: {
    fontSize: 10,
    color: BaseColor.textGrey1,
  },
  value: {
    fontSize: 12,
    color: BaseColor.blackColor,
  },
  description: {
    fontSize: 16,
    fontFamily: FontFamily.regular,
    color: '#39414B',
    lineHeight: 22,
  },
  startBtnContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  btnStyle: {
    height: 54,
    width: '80%',
    backgroundColor: BaseColor.primary,
    borderRadius: 8,
    marginHorizontal: 16,
  },
  btnText: {
    fontSize: 20,
    fontFamily: FontFamily.bold,
  },
});

export default SearchedTrip;
