/* eslint-disable quotes */
import React, {useEffect, useState, useCallback} from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  FlatList,
  ScrollView,
  BackHandler,
  Linking,
  Modal,
  ActivityIndicator,
  Image,
} from 'react-native';
import ToggleSwitch from 'toggle-switch-react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome5';
import {isEmpty, isObject} from 'lodash';
import {useDispatch, useSelector} from 'react-redux';
import {useFocusEffect, useTheme} from '@react-navigation/native';
import Toast from 'react-native-simple-toast';
import DeviceInfo from 'react-native-device-info';
import {SvgXml} from 'react-native-svg';
import styles from './styles';
import {CustomIcon} from '../../config/LoadIcons';
import {translate, initTranslate} from '../../lang/Translate';
import AuthAction from '../../redux/reducers/auth/actions';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import {savePageViewData, sendErrorReport} from '../../utils/commonFunction';
import {FontFamily, FontWeight} from '../../config/typography';
import langArr from '../../assets/flagSvg/flags';
import languageActions from '../../redux/reducers/language/actions';
import {store} from '../../redux/store/configureStore';
import Orientation from 'react-native-orientation-locker';
import Avatar from '../../components/Avatar/Avatar';
import NewAlert from '../../components/NewAlertModal';
import ModalAuth from '../../redux/reducers/modal/actions';
import PlaceAction from '../../redux/reducers/place/actions';
import TimerAction from '../../redux/reducers/timer/action';

/**
 *@module Setting
 */

const Setting = ({navigation}) => {
  const {
    setAccessToken,
    setUserData,
    setUserId,
    setUUid,
    setBaseColor,
    setIsFarenheit,
  } = AuthAction;
  const {setLastDeviceId, setIsConvert} = BluetoothAction;
  const dispatch = useDispatch();
  const colors = useTheme();
  const BaseColor = colors.colors;
  const {
    uuid,
    accessToken,
    darkmode,
    userData,
    isFarenheit,
    notificationCount,
  } = useSelector(state => state.auth);
  const languageData = useSelector(state => state.language.languageData);
  const {setLanguage} = languageActions;
  const {setClearModalRedux} = ModalAuth;
  const {setPlaceLocation, setClearTripData} = PlaceAction;
  const {setStartCountDown} = TimerAction;
  const socketData = useSelector(state => state.socket.socketData);
  const isPush = userData?.push_notification_sent;
  const flagsArr = langArr;
  const selectedLang = flagsArr.filter(item => item.code === languageData);
  const [selectedLanguage, setselectedLanguage] = useState(selectedLang[0]);
  const [langModal, setlangModal] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [btnLoader, setBtnLoader] = useState(false);
  const [AlerModal, setAlerModal] = useState({
    visible: false,
    title: '',
    message: '',
  });

  // Account Setting Array...
  const Data = [
    {
      id: 1,
      Cicon: 'profile',
      title: translate('myAccount'),
      text: 'Make changes to your account',
      icon1: 'chevron-right',
      navigation: 'MyAccount',
    },
    {
      id: 2,
      Cicon: 'profile',
      title: translate('Ecommorce'),
      text: 'Check out our latest products',
      icon1: 'chevron-right',
      size: 20,
      navigation: 'ProductCatalouge',
    },
    {
      id: 3,
      Cicon: 'profile',
      title: translate('savedProfile'),
      text: 'Manage all saved child profiles',
      icon1: 'chevron-right',
      navigation: 'Devices',
    },
    {
      id: 4,
      Cicon: 'shield-check',
      title: translate('updatePasswordScreen'),
      text: 'Further secure your account for safety',
      icon1: 'chevron-right',
      size: 20,
      navigation: 'UpdatePassword',
      navigationfrom: 'from myAccout',
    },
    // {
    //   id: 5,
    //   Cicon: 'smart-device',
    //   title: translate('myDevices'),
    //   text: 'Manage all your devices',
    //   icon1: 'chevron-right',
    //   navigation: 'MyDevice',
    // },
    // {
    //   id: 6,
    //   Cicon: 'gps-connected-',
    //   title: translate('smartTag'),
    //   text: 'Smart Tag New Feature',
    //   icon1: 'chevron-right',
    //   navigation: 'SmartTag',
    // },
    {
      id: 7,
      icon: 'baby-carriage',
      title: translate('tripHistory'),
      text: 'Past Trip History',
      icon1: 'chevron-right',
      navigation: 'TripHistory',
    },
    {
      id: 8,
      Cicon: 'bell',
      title: translate('pushText'),
      mode: translate('modeOff'),
      modeOn: translate('modeOn'),
    },
    {
      id: 9,
      icon: 'comment-dots',
      title: translate('customerText'),
      text: 'Ask our customer support team',
      icon1: 'chevron-right',
      badge: true,
      navigation: 'ChatScreen',
    },
    // {
    //   id: 10,
    //   Cicon: 'qrcode',
    //   title: translate('MyQRcode'),
    //   text: 'Your unique QR code',
    //   icon1: 'chevron-right',
    //   navigation: 'MyQRcode',
    // },
    {
      id: 11,
      Cicon: 'warning',
      title: translate('userManual'),
      text: 'Know how to use',
      icon1: 'chevron-right',
      size: 20,
      navigation: 'UserManual',
    },
    // {
    //   id: 12,
    //   Cicon: 'warning',
    //   title: translate('firmwareSettingGuide'),
    //   text: 'Something Something Something',
    //   icon1: 'chevron-right',
    //   size: 20,
    //   navigation: 'FirmwareSetting',
    // },
    // {
    //   id: 14,
    //   title: `${userData?.state}, ${userData?.country}`,
    //   text: 'Location',
    //   size: 20,
    //   image: `${
    //     userData?.country_code
    //       ? `https://flagcdn.com/w320/${(userData?.country_code).toLowerCase()}.png`
    //       : ''
    //   }`,
    // },
    {
      id: 13,
      Cicon: 'logout',
      title: translate('logout'),
      text: 'Further secure your account for safety',
      icon1: 'chevron-right',
      size: 20,
    },
  ];

  // More Data Array...
  const moreData = [
    {
      id: 1,
      Cicon: 'heart-unfilled',
      title: translate('about'),
      icon1: 'chevron-right',
    },
    {
      id: 2,
      Cicon: 'bell',
      title: translate('support'),
      icon1: 'chevron-right',
    },
    {
      id: 3,
      Cicon: 'bell',
      title: translate('privacypolicy'),
      icon1: 'chevron-right',
    },
    {
      id: 4,
      Cicon: 'remove-user',
      title: translate('deleteAccount'),
      icon1: 'chevron-right',
    },
  ];

  const changeLanguage = (code, name) => {
    dispatch(setLanguage(code, name));
    setTimeout(() => {
      initTranslate(store, true);
    }, 100);
    setTimeout(() => {
      setRefresh(false);
    }, 500);
  };
  const [AlerModal1, setAlerModal1] = useState({
    visible: false,
    title: '',
    message: '',
  });

  useFocusEffect(
    useCallback(() => {
      savePageViewData('settings');
    }, []),
  );

  useFocusEffect(
    useCallback(() => {
      Orientation.lockToPortrait();
    }, []),
  );
  /** this function for update notification status
   * @function updateNotificationStatus
   * @param {object} data {}
   */
  async function updateNotificationStatus() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.notificationStatus,
        'POST',
        {},
        headers,
      );
      if (response.success) {
        if (isObject(response.data) && !isEmpty(response.data)) {
          dispatch(setUserData(response.data));
        }
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      console.log('notification error ===', error);
      sendErrorReport(error, 'update_noti_status');
    }
  }

  const check = async (item, navigationFrom = '') => {
    if (item?.id === 13) {
      setAlerModal({
        visible: true,
        title: 'LOGOUT',
        message: 'Are you sure you want to logout?',
      });
      const {setLeadId} = AuthAction;
      dispatch(setLeadId(null));
    } else if (item?.id === 8) {
      updateNotificationStatus();
    } else {
      navigation.navigate(item?.navigation, navigationFrom);
    }
  };

  const render = ({item, index}) => (
    <View style={[styles.settingContent]}>
      <View style={[styles.settingIcon]}>
        {item.Cicon ? (
          <CustomIcon
            name={item.Cicon}
            size={item?.size ? item?.size : 20}
            color={BaseColor.primary}
          />
        ) : item?.id === 14 ? (
          <Image
            source={{uri: item?.image}}
            style={{
              width: 40,
              height: 40,
              borderRadius: 40,
            }}
          />
        ) : (
          <FAIcon name={item.icon} size={20} color={BaseColor.primary} />
        )}
      </View>

      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => {
          check(item, item?.navigationfrom);
        }}
        style={styles.settingName}>
        <View style={{flexDirection: 'column'}}>
          <Text style={[styles.infoText, {color: '#181D27'}]}>
            {item?.title}
          </Text>
          {item.mode ? (
            <Text
              style={{
                ...styles.infoText,
                fontSize: 14,
                color: item.id === 8 && isPush ? BaseColor.blueDark : 'red',
              }}>
              {item.id === 8 && isPush
                ? translate('modeOn')
                : translate('modeOff')}
            </Text>
          ) : item.text ? (
            <Text
              style={{
                ...styles.infoText,
                fontSize: 11,
                color: '#ABABAB',
              }}>
              {item.text}
            </Text>
          ) : null}
        </View>

        {item.temp ? (
          <View
            style={[
              styles.tempC,
              {backgroundColor: isFarenheit ? '#dd2c00' : '#cddc39'},
            ]}>
            <Text style={[styles.tempTxt, {color: BaseColor.whiteColor}]}>
              {!isFarenheit ? translate('tempC') : translate('tempF')}
            </Text>
          </View>
        ) : null}

        {item.mode ? (
          <ToggleSwitch
            size="medium"
            isOn={
              (item.id === 8 && isPush) || (item.id === 3 && darkmode) || false
            }
            onToggle={val => {
              if (item.id === 8) {
                updateNotificationStatus();
              }
            }}
            onColor={BaseColor.whiteColor}
            thumbOnStyle={{backgroundColor: BaseColor.blueDark}}
            thumbOffStyle={{backgroundColor: BaseColor.whiteColor}}
            trackOnStyle={{
              borderWidth: 1,
              borderColor: BaseColor.blueLight,
            }}
            trackOffStyle={{borderWidth: 1, borderColor: '#ecf0f1'}}
          />
        ) : null}

        {item.badge && notificationCount.notification_count > 0 ? (
          <View
            style={{
              backgroundColor: BaseColor.blueLight,
              width: 30,
              height: 30,
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 30,
            }}>
            <Text style={{color: '#fff'}}>
              {notificationCount.notification_count}
            </Text>
          </View>
        ) : null}

        {item.icon1 ? (
          <FAIcon
            name="chevron-right"
            size={12}
            color={BaseColor.textGrey}
            style={{marginRight: 0}}
          />
        ) : null}
      </TouchableOpacity>
    </View>
  );

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  /** this function for logout
   * @function removeToken
   * @param {object} data token, socket_id, user_id
   */
  async function removeToken(token) {
    setBtnLoader(true);
    const data = {
      token,
      socket_id: socketData?.socket_id || '',
      user_id: userData.id,
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.deleteToken,
        'POST',
        data,
        '',
        true,
      );
      if (response?.success) {
        dispatch(setAccessToken(''));
        dispatch(setUUid(''));
        dispatch(setClearModalRedux());
        dispatch(setPlaceLocation({}));
        dispatch(setStartCountDown(0));
        dispatch(setClearTripData());
        navigation.navigate('RedirectLS');
        navigation.closeDrawer();
      }
      setBtnLoader(false);
    } catch (err) {
      console.log('ERRR==', err);
    }
  }

  async function deleteAccount() {
    setBtnLoader(true);
    try {
      const response = await getApiData(
        BaseSetting.endpoints.deleteAccount,
        'POST',
      );
      if (response?.success) {
        dispatch(setAccessToken(''));
        dispatch(setUUid(''));
        dispatch(setClearModalRedux());
        navigation.navigate('RedirectLS');
      }
      setBtnLoader(false);
    } catch (err) {
      setBtnLoader(false);
      console.log('ERRR==', err);
    }
  }
  return (
    <View style={{flex: 1}}>
      <View style={styles.root}>
        {/* <Text style={styles.settingTxt}>Settings</Text> */}
        <View style={styles.profileContainer}>
          <Avatar userData={userData} />
          <View style={{marginLeft: 10}}>
            <Text
              style={[
                styles.ProfilecontainerTxt,
                {
                  fontFamily: FontFamily.bold,
                  fontSize: 14,
                },
              ]}>
              {userData?.full_name ? userData.full_name : 'User'}{' '}
            </Text>
            <Text
              style={[
                styles.ProfilecontainerTxt,
                {
                  marginTop: 2,
                  marginBottom: 5,
                },
              ]}>
              {userData?.email ? userData.email : '<EMAIL>'}
            </Text>
            <Text style={styles.ProfilecontainerTxt}>
              {userData?.phone ? userData.phone : ''}
            </Text>
          </View>
        </View>
        <ScrollView contentContainerStyle={{flexGrow: 1}} bounces={false}>
          <View
            style={[
              styles.flatListView,
              {backgroundColor: BaseColor.whiteColor},
            ]}>
            <FlatList
              data={Data}
              renderItem={render}
              keyExtractor={item => item.id}
              bounces={false}
            />
          </View>
          <View style={{marginHorizontal: 22, marginBottom: 10}}>
            <Text style={{fontSize: 16, color: 'black'}}>More</Text>
          </View>
          <View style={{paddingHorizontal: 16}}>
            {moreData &&
              moreData.map(li => {
                return (
                  <TouchableOpacity
                    activeOpacity={0.7}
                    style={[styles.settingContent, {paddingVertical: 13}]}
                    onPress={() => {
                      if (li.id === 1) {
                        Linking.openURL('https://qeridoo.de/');
                      } else if (li.id === 2) {
                        navigation.navigate('FAQScreen');
                      } else if (li.id === 3) {
                        Linking.openURL('https://qeridoo.de/impressum');
                      } else {
                        setAlerModal1({
                          visible: true,
                          title: 'Delete Account',
                          message:
                            'Are you sure you want to delete your account?',
                        });
                        const {setLeadId} = AuthAction;
                        dispatch(setLeadId(null));
                      }
                    }}>
                    <View style={[styles.settingIcon]}>
                      <CustomIcon
                        name={li.Cicon}
                        size={20}
                        color={BaseColor.primary}
                      />
                    </View>
                    <Text
                      style={[styles.infoText, {color: BaseColor.blackColor}]}>
                      {li.title}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            <TouchableOpacity
              activeOpacity={0.7}
              style={[styles.settingContent]}
              onPress={() => {
                setlangModal(true);
              }}>
              <View style={[styles.settingIcon]}>
                <View style={styles.flagContainer}>
                  <SvgXml xml={selectedLanguage.svg} width={45} height={45} />
                </View>
              </View>
              <Text style={[styles.infoText]}>{selectedLanguage.title}</Text>
            </TouchableOpacity>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                marginVertical: 10,
                marginBottom: 40,
              }}>
              <Text
                style={{
                  ...styles.aboutText,
                  fontSize: 10,
                  color: BaseColor.textGrey1,
                  top: 5,
                  marginBottom: 20,
                }}>
                {`Version ${DeviceInfo.getVersion()} (${DeviceInfo.getBuildNumber()}))`}
              </Text>
            </View>
          </View>
        </ScrollView>
      </View>
      <NewAlert
        visible={AlerModal.visible}
        onRequestClose={() =>
          setAlerModal({
            ...AlerModal,
            visible: false,
          })
        }
        onCancelPress={() =>
          setAlerModal({
            ...AlerModal,
            visible: false,
          })
        }
        loader={btnLoader}
        onOkPress={() => {
          removeToken(uuid);
        }}
        alertTitle={AlerModal.title}
        alertMessage={AlerModal.message}
        agreeTxt={translate('agree')}
      />
      <NewAlert
        visible={AlerModal1.visible}
        onRequestClose={() =>
          setAlerModal1({
            ...AlerModal1,
            visible: false,
          })
        }
        onCancelPress={() =>
          setAlerModal1({
            ...AlerModal1,
            visible: false,
          })
        }
        loader={btnLoader}
        onOkPress={async () => {
          await deleteAccount();
          dispatch(setUserData({}));
          dispatch(setUserId(''));
          dispatch(setLastDeviceId(''));
        }}
        alertTitle={AlerModal1.title}
        alertMessage={AlerModal1.message}
        agreeTxt={translate('delete')}
      />
      <Modal transparent visible={langModal} animationType="fade">
        <TouchableOpacity
          style={{flex: 1}}
          onPress={() => {
            setlangModal(false);
          }}>
          <View style={styles.modalCont}>
            {flagsArr.map((item, index) => (
              <TouchableOpacity
                key={`${index}`}
                style={styles.langBtn}
                activeOpacity={0.7}
                onPress={() => {
                  setlangModal(false);
                  setTimeout(() => {
                    setRefresh(true);
                  }, 500);
                  setTimeout(() => {
                    changeLanguage(item.code, item.title);
                  }, 2000);
                  setselectedLanguage(item);
                }}>
                <View style={styles.flagDesign}>
                  <SvgXml xml={item.svg} width={20} height={20} />
                  <Text
                    style={{
                      color: BaseColor.blackColor,
                      marginHorizontal: 8,
                      fontFamily: FontFamily.default,
                    }}>
                    {item.title}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
      <Modal
        transparent
        style={{
          flex: 1,
        }}
        visible={refresh}
        animationType="fade">
        <TouchableOpacity
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: BaseColor.white50,
          }}
          onPress={() => setRefresh(false)}>
          <View
            style={{
              padding: 12,
              backgroundColor: BaseColor.whiteColor,
              borderRadius: 12,
              borderWidth: 0.5,
              borderColor: BaseColor.textGrey,
            }}>
            <Text
              style={{
                fontFamily: FontFamily.default,
                fontWeight: 'bold',
                marginBottom: 12,
              }}>
              Changing language
            </Text>
            <ActivityIndicator size="small" color={BaseColor.blueDark} />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

export default Setting;
