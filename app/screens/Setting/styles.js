import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
    marginTop: getStatusBarHeight() + 20,
  },
  flatListView: {
    backgroundColor: BaseColor.whiteColor,
    marginHorizontal: 16,
    marginVertical: 10,
    marginTop: 15,
    borderRadius: 16,
    borderColor: BaseColor.textGrey,
    // borderWidth: 0.5,
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    paddingVertical: 16,
    // borderBottomColor: BaseColor.textGrey,
    // borderBottomWidth: 0.5
  },
  settingName: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  tempC: {
    width: 32,
    height: 32,
    backgroundColor: '#dd2c00',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tempTxt: {
    textAlign: 'center',
    textAlignVertical: 'center',
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.default,
    fontWeight: 'bold',
  },
  settingIcon: {
    width: 40,
    height: 40,
    backgroundColor: '#F9FBFB',
    textAlignVertical: 'center',
    textAlign: 'center',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColor.textGrey,
  },
  infoText: {
    color: '#181D27',
    fontSize: 13,
    paddingHorizontal: 16,
    fontFamily: FontFamily.regular,
    lineHeight: 19,
  },
  securityCheckView: {
    width: 50,
    alignSelf: 'center',
  },
  aboutText: {
    fontSize: 16,
    color: BaseColor.whiteColor,
    paddingBottom: 7,
    fontFamily: FontFamily.default,
    // fontWeight: 'bold',
  },
  modalCont: {
    backgroundColor: BaseColor.whiteColor,
    borderWidth: 1,
    borderColor: BaseColor.primary,
    position: 'absolute',
    bottom: 40,
    alignSelf: 'flex-start',
    borderRadius: 16,
    padding: 12,
    margin: 25,
  },
  flagDesign: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    minWidth: 140,
    padding: 5,
  },
  settingTxt: {
    fontFamily: FontFamily.bold,
    fontSize: 20,
    color: '#000000',
    paddingLeft: 20,
  },
  profileContainer: {
    backgroundColor: BaseColor.primary,
    height: 90,
    marginHorizontal: 20,
    marginVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 5,
  },
  ProfilecontainerTxt: {
    color: BaseColor.whiteColor,
    fontSize: 11,
    fontFamily: FontFamily.regular,
  },
  flagContainer: {
    borderRadius: 15,
    backgroundColor: BaseColor.blackColor,
    overflow: 'hidden',
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
  },
});

export default styles;
