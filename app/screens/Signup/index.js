/* eslint-disable max-len */
/* eslint-disable quotes */
import React, {useEffect, useRef, useState} from 'react';
import {Text, View, BackHandler, Platform, Dimensions} from 'react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import Toast from 'react-native-simple-toast';
import {useDispatch, useSelector} from 'react-redux';
import {isObject} from 'lodash';
import {useTheme} from '@react-navigation/native';
import GetLocation from 'react-native-get-location';
import CButton from '../../components/CButton';
import CInput from '../../components/CInput';
import styles from './styles';
import {FontFamily} from '../../config/typography';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {getSliderDetail, sendErrorReport} from '../../utils/commonFunction';
import AuthAction from '../../redux/reducers/auth/actions';
import {translate} from '../../lang/Translate';
import BaseColors from '../../config/colors';
import CustomHeader from '../../components/CustomHeader/CustomHeader';
import {PERMISSIONS, RESULTS, check, request} from 'react-native-permissions';
import environment from '../../config/environment';
import MultiStepSlider from '../../components/MultiSlider/MultiSlider';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import langArr from '../../assets/flagSvg/flags';

/**
 *
 * @module SignUp
 */
const Signup = ({navigation}) => {
  const IOS = Platform.OS === 'ios';
  const colors = useTheme();
  const BaseColor = colors.colors;
  const brandToken = useSelector(state => state.auth.brandToken);
  const langList = useSelector(state => state.auth.langList);
  const languageData = useSelector(state => state.language.languageData);

  const [loader, setloader] = useState(false);
  const [loading, setloading] = useState(false);

  const [nameError, setNameError] = useState(false);
  const [showCheckList, setShowCheckList] = useState(false);
  const [isUpperCase, setIsUpperCase] = useState(false);
  const [isLowerCase, setIsLowerCase] = useState(false);
  const [isNu, setIsNumber] = useState(false);
  const [isSpecial, setIsSp] = useState(false);
  const [isTwelve, setIsTwelve] = useState(false);
  const [passwordError, setPasswordError] = useState(false);
  const [conformPassErr, setConformPassErr] = useState({err: false, txt: ''});
  const [mailError, setMailError] = useState(false);
  const [numError, setNumError] = useState(false);
  const [nameErrorTxt, setnameErrorTxt] = useState('');
  const [lastNameError, setLastNameError] = useState({err: false, txt: ''});
  const [passwordErrorTxt, setPasswordErrorTxt] = useState('');
  const [mailErrorTxt, setMailErrorTxt] = useState('');
  const [numErrorTxt, setNumErrorTxt] = useState('');

  const fNameRef = useRef();
  const passwordRef = useRef();
  const emailRef = useRef();
  const pNumRef = useRef();

  const dispatch = useDispatch();
  const {setUserId, setIsFarenheit, setUserLocalAddress} = AuthAction;
  const findIndex =
    langArr && langArr.findIndex(li => li.code === languageData);
  const [state, setstate] = useState({
    fullName: '',
    lastName: '',
    password: '',
    email: '',
    pNum: '',
    country: '',
    selectedCountry: 'ES',
    selectedCountryName: 'Spain',
    countryCode: '34',
    agree: true,
    language_code: languageData,
    selLang: langList && langList[findIndex],
    lat: '',
    lng: '',
    state: '',
    confirmPass: '',
  });
  const [hidePwd, setHidePwd] = useState({pass: true, confirmPass: true});

  const requestLocationPermission = async () => {
    const locationPermission = Platform.select({
      ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    });

    try {
      // Check the current permission status
      const result = await check(locationPermission);
      console.log('Current permission status:', result);

      if (result === RESULTS.GRANTED) {
        console.log('Permission already granted');
        return true;
      } else if (result === RESULTS.DENIED || result === RESULTS.LIMITED) {
        // Request permission if not granted
        const requestResult = await request(locationPermission);
        console.log('Request permission result:', requestResult);
        return requestResult === RESULTS.GRANTED;
      } else {
        console.log('Permission denied permanently or blocked');
        return false;
      }
    } catch (error) {
      console.error('Permission check/request error:', error);
      // Handle permission request error appropriately (e.g., show a toast or alert)
      return false;
    }
  };
  useEffect(() => {
    const checkAndRequestLocationPermission = async () => {
      const isLocationPermissionGranted = await requestLocationPermission();
      console.log('isLocationPermissionGranted', isLocationPermissionGranted);
      if (isLocationPermissionGranted) {
        await getLocation();
      }
    };

    checkAndRequestLocationPermission();
  }, []);

  const getLocation = async () => {
    try {
      setloading(true);
      GetLocation.getCurrentPosition({
        enableHighAccuracy: true,
        maximumAge: 10000,
      })
        .then(async position => {
          console.log('posiii0000000000000', position);

          const response = await fetch(
            `https://maps.googleapis.com/maps/api/geocode/json?latlng=${position.latitude},${position.longitude}&key=${environment.mapKey}`,
          );
          const responseJson = await response?.json();

          if (responseJson?.results && responseJson?.results?.length > 0) {
            let stateV = '';
            const {address_components} = responseJson?.results?.[0] || [];
            address_components.forEach(item => {
              if (item?.types?.includes('administrative_area_level_1')) {
                stateV = item?.long_name;
              }
            });

            setstate({
              ...state,
              lat: position?.coords?.latitude,
              lng: position?.coords?.longitude,
              state: stateV,
            });

            let localAddress1 = '';
            let localAddress2 = '';
            let localAddress3 = '';
            if (responseJson?.results?.[1]) {
              const address_components_2 =
                responseJson?.results?.[1]?.address_components;
              address_components_2.forEach(item => {
                if (item.types.includes('route')) {
                  localAddress1 = item?.long_name;
                } else if (item.types.includes('locality')) {
                  localAddress3 = item?.long_name;
                } else if (item.types.includes('premise')) {
                  localAddress2 = item?.long_name;
                }
              });
            }

            dispatch(
              setUserLocalAddress({
                localAddress1,
                localAddress2,
                localAddress3,
              }),
            );

            setloading(false);
          } else {
            setloading(false);
            throw new Error('No results found for the given location.');
          }
        })
        .catch(error => {
          const {code, message} = error;
          sendErrorReport(error, 'get_location');
          console.warn(code, message);
        });
    } catch (error) {
      setloading(false);
      console.error('Error getting location:', error);
      sendErrorReport(error, 'get_location');
    }
  };

  function handleBackButtonClick() {
    navigation.navigate('RedirectLS');
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  const Validation = () => {
    const passVal =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,15}$/;
    const numVal = /^[0-9]+$/;
    const emailVal =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    // enableAnimateInEaseOut();

    if (state?.fullName === '') {
      allErrorFalse();
      setNameError(true);
      // setnameErrorTxt("Please enter FullName");
      setnameErrorTxt(translate('enterName'));
    } else if (state?.lastName === '') {
      setLastNameError({err: true, txt: translate('enterLastName')});
    } else if (state?.password === '') {
      allErrorFalse();
      setPasswordError(true);
      // setPasswordErrorTxt("Please enter Password");
      setPasswordErrorTxt(translate('enterPasswrd'));
    } else if (!passVal.test(String(state?.password))) {
      allErrorFalse();
      setPasswordError(true);
      // setPasswordErrorTxt(
      //   "Password must contain 8-15 characters, 1 x Upper case, 1 x Lower case, 1 x number and 1 x special character such as !,?,&"
      // );
      setPasswordErrorTxt(translate('passwrdValid'));
    } else if (state?.password !== state?.confirmPass) {
      setConformPassErr({err: true, txt: translate('confirmPassValid')});
    } else if (state?.email === '') {
      allErrorFalse();
      setMailError(true);
      // setMailErrorTxt("Please enter email");
      setMailErrorTxt(translate('enterEmail'));
    } else if (!emailVal.test(String(state?.email))) {
      allErrorFalse();
      setMailError(true);
      // setMailErrorTxt("Please enter valid Email");
      setMailErrorTxt(translate('enterEmailvalid'));
    } else if (state?.pNum === '') {
      allErrorFalse();
      setNumError(true);
      // setNumErrorTxt("Please enter Phone number");
      setNumErrorTxt(translate('enterPhone'));
    } else if (
      !numVal.test(String(state?.pNum)) ||
      state?.pNum.length < 6 ||
      state?.pNum.length > 12
    ) {
      allErrorFalse();
      setNumError(true);
      // setNumErrorTxt("Please enter valid Phone number");
      setNumErrorTxt(translate('enterPhonevalid'));
    } else if (!state?.agree) {
      allErrorFalse();
      // Toast.show("Please accept Terms and Conditions", Toast.SHORT, [
      //   "UIAlertController",
      // ]);
      Toast.show(translate('accept'), Toast.SHORT, ['UIAlertController']);
    } else {
      allErrorFalse();
      userSignUp();
    }
  };

  const allErrorFalse = () => {
    setNameError(false);
    setNumError(false);
    setPasswordError(false);
    setMailError(false);
  };

  /** this function for user SignUp
   * @function userSignUp
   * @param {object} data full_name, password, email, phone, phone_code, country, token, language_id, latitude, longitude, state
   */
  const userSignUp = () => {
    setloader(true);
    const data = {
      full_name: `${state?.fullName} ${state?.lastName}`,
      // last_name: state?.lastName,
      password: state?.password,
      email: state?.email,
      phone: state?.pNum,
      phone_code: `+${state?.countryCode}`,
      country: state?.selectedCountryName,
      country_code: state?.selectedCountry,
      token: brandToken,
      language_code: languageData,
      // language_id: state?.selLang.id,
      latitude: state?.lat,
      longitude: state?.lng,
      state: state?.state,
      brand_name: 'Qeridoo',
      app_name: 'qeridoo',
      confirm_password: state.confirmPass,
    };

    getApiData(BaseSetting.endpoints.signUp, 'POST', data, '', true)
      .then(response => {
        const uId =
          response && isObject(response?.data) && response?.data?.id
            ? response?.data?.id
            : null;
        dispatch(setUserId(uId));
        if (response.success) {
          getSliderDetail(); // Slider API
          dispatch(setIsFarenheit(false));
          navigation.navigate('Otp', {type: 'Signup'});
          // Toast.shokenw(response.message);
        } else {
          Toast.show(response?.message?.message || response?.message || '');
        }
        setloader(false);
      })
      .catch(err => {
        Toast.show('Something went wrong while signup');
        setloader(false);
        console.log('ERR====>>>>', err);
        sendErrorReport(err, 'sign_up');
      });
  };
  function isUpper(str) {
    return /^(?=.*?[A-Z])/.test(str);
  }
  function isLower(str) {
    return /^(?=.*?[a-z])/.test(str);
  }
  function isNumericCheck(str) {
    return /^(?=.*?[0-9])/.test(str);
  }
  function isSpCheck(str) {
    return /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(str);
  }
  function isTw(str) {
    if (str?.length >= 8) {
      return true;
    } else {
      return false;
    }
  }

  return (
    <View style={styles.root}>
      <CustomHeader
        transparentView
        leftIconName="left-arrow"
        borderCircular
        backgroundColor={'#F1F5F9'}
        onLeftPress={() => {
          navigation.navigate('RedirectLS');
        }}
        headerContainer={{marginBottom: 0}}
        backBtn
      />
      <KeyboardAwareScrollView
        bounces={false}
        contentContainerStyle={{
          flexGrow: 1,
          paddingHorizontal: 20,
        }}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}>
        <Text style={styles.loginTxt}>{translate('loginToSignup')}</Text>
        <View
          style={{
            flex: 1,
            marginTop: IOS ? 10 : 5,
          }}>
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              marginBottom: 5,
            }}>
            <View style={{width: '48%', marginRight: 15}}>
              <CInput
                title={translate('firstName')}
                ref={fNameRef}
                placeholder={translate('firstName')}
                value={state.fullName}
                onChangeText={val => {
                  setstate({...state, fullName: val});
                }}
                placeholderTextColor={BaseColors.textGrey}
                showError={nameError}
                errorMsg={nameErrorTxt}
              />
            </View>
            <View style={{width: '48%'}}>
              <CInput
                title={translate('lastName')}
                ref={fNameRef}
                placeholder={translate('lastName')}
                value={state.lastName}
                onChangeText={val => {
                  setstate({...state, lastName: val});
                }}
                placeholderTextColor={BaseColors.textGrey}
                showError={lastNameError.err}
                errorMsg={lastNameError.txt}
              />
            </View>
          </View>
          <View style={{marginBottom: IOS && 5}}>
            <CInput
              title={translate('emailId')}
              ref={emailRef}
              placeholder={translate('enterYourEmail')}
              value={state.email}
              onChangeText={val => {
                setstate({...state, email: val});
              }}
              placeholderTextColor={BaseColors.textGrey}
              iconSize={20}
              keyboardType="email-address"
              showError={mailError}
              errorMsg={mailErrorTxt}
            />
          </View>
          <View style={{marginBottom: IOS && 5}}>
            <CInput
              ref={pNumRef}
              isSuffix
              phoneNumber
              onChangeText={val => {
                setstate({...state, pNum: val});
              }}
              suffixStyle={{
                backgroundColor: BaseColors.transparent,
              }}
              onSelect={val =>
                setstate({
                  ...state,
                  selectedCountry: val.cca2,
                  selectedCountryName: val.name,
                  countryCode: val.callingCode[0],
                })
              }
              inputStyle={{
                paddingHorizontal: 45,
              }}
              title={translate('forgotInput')}
              placeholder={translate('forgotInput')}
              value={state.pNum}
              keyboardType="number-pad"
              returnKeyType="done"
              countryCode={state.selectedCountry}
              showError={numError}
              errorMsg={numErrorTxt}
            />
          </View>
          <View style={{marginBottom: IOS && 5}}>
            <CInput
              title={translate('loginPassword')}
              ref={passwordRef}
              placeholder={translate('loginPassword')}
              value={state.password}
              onChangeText={val => {
                setstate({...state, password: val});
                const lower = isLower(val);
                if (lower) {
                  setIsLowerCase(true);
                } else {
                  setIsLowerCase(false);
                }
                const upper = isUpper(val);
                if (upper) {
                  setIsUpperCase(true);
                } else {
                  setIsUpperCase(false);
                }
                const num = isNumericCheck(val);
                if (num) {
                  setIsNumber(true);
                } else {
                  setIsNumber(false);
                }

                const sp = isSpCheck(val);
                if (sp) {
                  setIsSp(true);
                } else {
                  setIsSp(false);
                }
                const tw = isTw(val);
                if (tw) {
                  setIsTwelve(true);
                } else {
                  setIsTwelve(false);
                }

                if (val.length <= 0) {
                  setIsUpperCase(false);
                  setIsLowerCase(false);
                  setIsNumber(false);
                  setIsTwelve(false);
                  setIsSp(false);
                }
                if (
                  isUpper(val) &&
                  isLower(val) &&
                  isNumericCheck(val) &&
                  isSpCheck(val) &&
                  isTw(val)
                ) {
                  setShowCheckList(false);
                } else {
                  setShowCheckList(true);
                }
              }}
              onFocus={() => {
                console.log('on focus');
                setShowCheckList(true);
              }}
              onBlur={() => {
                console.log(' on blur');
                setShowCheckList(false);
              }}
              placeholderTextColor={BaseColors.textGrey}
              rightIcon
              iconName={hidePwd.pass ? 'eye-close' : 'eye'}
              iconColor={
                hidePwd.pass ? BaseColors.primary : BaseColors.blackColor
              }
              iconSize={20}
              onRightIconPress={() =>
                setHidePwd({...hidePwd, pass: !hidePwd.pass})
              }
              secureTextEntry={hidePwd.pass}
              showError={passwordError}
              errorMsg={passwordErrorTxt}
            />
            {showCheckList && (
              <View
                style={{
                  marginHorizontal: 10,
                  padding: 20,
                  borderRadius: 10,
                  shadowColor: 'purple',
                  height: 150,
                  width: '90%',
                  backgroundColor: 'white',
                  justifyContent: 'center',
                  elevation: 10,
                  shadowOffset: {width: 1, height: 1},
                  shadowRadius: 3,
                  shadowOpacity: 0.5,
                }}>
                <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                  {translate('pswdReq')}
                </Text>
                <View style={{flexDirection: 'row'}}>
                  <FAIcon
                    name={isLowerCase ? 'check' : 'remove'}
                    size={18}
                    color={isLowerCase ? BaseColor.green : BaseColor.alertRed}
                  />
                  <Text style={{marginLeft: 5}}>
                    {translate('oneLowerCase')}
                  </Text>
                </View>

                <View style={{flexDirection: 'row'}}>
                  <FAIcon
                    name={isUpperCase ? 'check' : 'remove'}
                    size={18}
                    color={isUpperCase ? BaseColor.green : BaseColor.alertRed}
                  />
                  <Text style={{marginLeft: 5}}>
                    {translate('oneUpperCase')}
                  </Text>
                </View>

                <View style={{flexDirection: 'row'}}>
                  <FAIcon
                    name={isNu ? 'check' : 'remove'}
                    size={18}
                    color={isNu ? BaseColor.green : BaseColor.alertRed}
                  />
                  <Text style={{marginLeft: 5}}>{translate('oneNumber')}</Text>
                </View>

                <View style={{flexDirection: 'row'}}>
                  <FAIcon
                    name={isSpecial ? 'check' : 'remove'}
                    size={18}
                    color={isSpecial ? BaseColor.green : BaseColor.alertRed}
                  />
                  <Text style={{marginLeft: 5}}>{translate('oneSp')}</Text>
                </View>
                <View style={{flexDirection: 'row'}}>
                  <FAIcon
                    name={isTwelve ? 'check' : 'remove'}
                    size={18}
                    color={isTwelve ? BaseColor.green : BaseColor.alertRed}
                  />
                  <Text style={{marginLeft: 5}}>{translate('twLong')}</Text>
                </View>
              </View>
            )}
          </View>
          <View style={{marginBottom: IOS && 5}}>
            <CInput
              title={translate('confirmPassword')}
              ref={passwordRef}
              placeholder={translate('confirmPassword')}
              value={state.confirmPass}
              onChangeText={val => {
                setstate({...state, confirmPass: val});
              }}
              rightIcon
              iconName={hidePwd.confirmPass ? 'eye-close' : 'eye'}
              iconColor={
                hidePwd.confirmPass ? BaseColors.primary : BaseColors.blackColor
              }
              iconSize={20}
              onRightIconPress={() =>
                setHidePwd({...hidePwd, confirmPass: !hidePwd.confirmPass})
              }
              placeholderTextColor={BaseColors.textGrey}
              secureTextEntry={hidePwd.confirmPass}
              showError={conformPassErr.err}
              errorMsg={conformPassErr.txt}
            />
          </View>
          <View>
            <View style={{marginTop: 15}}>
              <CButton
                style={styles.loginBtn}
                title={translate('Next')}
                onPress={() => {
                  Validation();
                }}
                loader={loader}
              />
            </View>
            <View style={{marginTop: '5%'}}>
              <Text
                style={{
                  textAlign: 'center',
                  fontFamily: FontFamily.regular,
                  fontSize: 12,
                  color: BaseColor.primary,
                  lineHeight: 20,
                }}>
                {translate('continueAgree')}{' '}
                <Text
                  onPress={() => console.log('terms--------->>>>')}
                  style={{
                    fontSize: 14,
                    fontFamily: FontFamily.regular,
                    color: BaseColors.primary3,
                    textDecorationLine: 'underline',
                    marginRight: 20,
                  }}>
                  {translate('termsPrivacy')}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: FontFamily.regular,
                    color: BaseColors.primary3,
                  }}>
                  {' '}
                  {'and'}{' '}
                </Text>
                <Text
                  onPress={() => console.log('privacy<<<<<--------->>>>')}
                  style={{
                    fontSize: 14,
                    fontFamily: FontFamily.regular,
                    color: BaseColors.primary3,
                    textDecorationLine: 'underline',
                  }}>
                  {`${translate('privacypolicy')}.`}
                </Text>
              </Text>
            </View>
          </View>
        </View>
        <View
          style={{
            marginBottom: 30,
            marginHorizontal: Dimensions.get('window').width / 9,
          }}>
          <MultiStepSlider />
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
};

export default Signup;
