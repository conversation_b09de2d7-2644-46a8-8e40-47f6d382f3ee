import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const {height: dHeight, width: dWidth} = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
  },
  mainContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: 'flex-start',
    position: 'absolute',
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
    top: 60,
    left: 20,
  },
  loginBtn: {
    width: '80%',
  },
  mainInputStyle: {
    // flex: 1,
    justifyContent: 'center',
  },
  myQeridoo: {
    fontFamily: FontFamily.regular,
    color: BaseColor.whiteColor,
    fontSize: 38,
    lineHeight: 49,
    textAlign: 'right',
  },
  loginTxt: {
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    fontSize: 30,
    textAlign: 'center',
  },

  rememberText: {
    fontSize: 12,
    fontFamily: FontFamily.default,
    color: BaseColor.whiteColor,
    marginStart: 8,
  },
  countryPickerStyle: {
    paddingLeft: 20,
    marginTop: '2%',
    borderColor: BaseColor.primary,
    borderWidth: 0.5,
    height: 50,
    borderRadius: 8,
    paddingVertical: 8,
    justifyContent: 'center',
    backgroundColor: BaseColor.whiteColor,
  },
  logoImg: {
    height: 60,
    width: 60,
    alignSelf: 'center',
  },
});

export default styles;
