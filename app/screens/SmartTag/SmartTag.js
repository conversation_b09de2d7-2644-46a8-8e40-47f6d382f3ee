import React, {useEffect, useState} from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  BackHandler,
  FlatList,
  Image,
  KeyboardAvoidingView,
  NativeModules,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  DeviceEventEmitter,
  Vibration,
  ActivityIndicator,
  Platform,
  NativeEventEmitter,
  Modal,
} from 'react-native';
import {View} from 'react-native-animatable';
import MapView, {MapMarker, PROVIDER_GOOGLE} from 'react-native-maps';
import BaseColor from '../../config/colors';
import GetLocation from 'react-native-get-location';
import {sendErrorReport} from '../../utils/commonFunction';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {FontFamily} from '../../config/typography';
import SmartTagIcon from '../../assets/smartTag/MapsGlobal.svg';
import CInput from '../../components/CInput';
import CButton from '../../components/CButton';
import CustomHeader from '../../components/CustomHeader/CustomHeader';
import {
  BluetoothStatus,
  useBluetoothStatus,
} from 'react-native-bluetooth-status';
import Toast from 'react-native-simple-toast';
import {Alert} from 'react-native';
import {isArray, isEmpty} from 'lodash';
import SelectDropdown from 'react-native-select-dropdown';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  PERMISSIONS,
  checkMultiple,
  request,
  check,
} from 'react-native-permissions';
import {CustomIcon} from '../../config/LoadIcons';
import bluetoothActions from '../../redux/reducers/bluetooth/actions';
import {useDispatch, useSelector} from 'react-redux';
import useBluetoothState from '../../customHook/useBluetoothConstantlyListenState';
import useBluetoothConstantlyListenState from '../../customHook/useBluetoothConstantlyListenState';
import useCheckBluetoothStatus from '../../customHook/useCheckBlutoothStaus';
import {connect} from 'socket.io-client';
import FIcon from 'react-native-vector-icons/Feather';
import {enable} from 'react-native-bluetooth-state-manager';
import environment from '../../config/environment';
import {SvgFromXml} from 'react-native-svg';
import commonSvg from '../../assets/commonSvg/commonnSvg';
import {translate} from '../../lang/Translate';
import smartTagMapStyle from '../../config/smartTagMapStyle';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';

function SmartTag({navigation, route}) {
  const details = route?.params?.details;
  const [loc, setLoc] = useState({});
  const [appState, setAppState] = useState(true);

  //------------------------
  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    // setIsScanning(false);

    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  // ------App state
  useEffect(() => {
    // console.log('App ddddd');
    const handleAppStateChange = nextAppState => {
      // console.log(`App State: ${nextAppState}`);
      if (nextAppState === 'active') {
        // console.log('App State======', true);
        // sendErrorReport(true, "app_state_active");
        setAppState(true);
      } else if (nextAppState === 'background') {
        // console.log('App State======', false);
        // sendErrorReport(false, "app_state_background");
        setAppState(false);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => subscription.remove();
  }, []);

  const getCurrentLocation = async () => {
    // console.log("AB HUMARI BARI HAIII---->> CHALNE KII.......");
    const myApiKey = environment.mapKey;
    // console.log("myApiKey", myApiKey);
    GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 10000,
    })
      .then(location => {
        // console.log("location tada---->>>", location);
        fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?address=${location.latitude},${location.longitude}&key=${myApiKey}`,
        )
          .then(response => response.json())
          .then(responseJson => {
            // console.log("responseJson------>>>>>>", responseJson);
            let localAddress1 = '';
            let localAddress2 = '';
            let localAddress3 = '';
            const {address_components} = responseJson.results[1];
            address_components.map(item => {
              if (item.types[0] === 'route') {
                localAddress1 = item.long_name;
              } else if (item.types[0] === 'locality') {
                localAddress3 = item.long_name;
              } else if (item.types[0] === 'premise') {
                localAddress2 = item.long_name;
              }
            });
            setLoc(location);
            // console.log("localAddress1", localAddress1);
            // console.log("localAddress2", localAddress2);
            // console.log("localAddress3", localAddress3);
            dispatch(
              bluetoothActions.setSmartTagDeviceLocation({
                latitude: location?.latitude,
                longitude: location?.longitude,
                localAddress1,
                localAddress2,
                localAddress3,
              }),
            );
          });
      })
      .catch(error => {
        console.warn('error -- location', error.message);
        if (error?.message !== 'Location cancelled by another request') {
          Toast.show(
            'Please enable your location service to send emergency alert.',
          );
        }
      });
  };

  const [intialBluetoothState, setIntialBluetoothState] = useState(false);
  const [scannedDeviceList, setScannedDeviceList] = useState([]);
  const [selectedScannedDevice, setSelectedScannedDevice] = useState(null);
  const [isScanning, setIsScanning] = useState(false);
  const [promtText, setPrommtText] = useState(
    'Start Scanning To Search For Device',
  );
  const [isConnectingLoading, setIsConnectingLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [batteryLevel, setBatteryLevel] = useState(0);
  const [currentBluetoothStatus, setCurrentBluetoothStatus] = useState(false);
  const [enableAntiLost, setEnableAntiLost] = useState(true);
  const [isRing, setisRing] = useState(false);
  const [loader, setLoader] = useState(false);
  const {
    pairedSmartTagDevice,
    lastKnownSmartTagLocation,
    isSmartTagDeviceConnected,
    isSmartTagConnectingLoading,
    scannedSmartTagDevice,
  } = useSelector(state => state.bluetooth);
  const dispatch = useDispatch();
  const {NutSdkModule} = NativeModules;

  const connectedArr = [
    {
      id: 1,
      name: 'Ring Device',
    },
    {
      id: 2,
      name: 'Unpair Device',
    },
    {
      id: 3,
      name: 'Enable Anti-lost Feature',
    },
  ];

  const checkBluetoothStatus = async () => {
    try {
      const {NutSdkModule} = NativeModules;
      let isEnabled;
      if (Platform.OS === 'android') {
        isEnabled = await NutSdkModule.isBluetoothEnabled();

        if (!isEnabled) {
          setIsConnected(false);
          dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
        }
        setIntialBluetoothState(isEnabled);
        // console.log("Bluetooth is enabled:", isEnabled);
      }
    } catch (error) {
      console.error('Error checking Bluetooth status:', error);
    }
  };

  useEffect(() => {
    checkBluetoothStatus();
    if (!isEmpty(pairedSmartTagDevice) && pairedSmartTagDevice?.address) {
      // console.log(
      //   "DEVICE DATA IN REDUX PREVIOSULY NOW CHECKING IS CONNECTED OR NOT->>"
      // );
      checkingIsDeviceConnect(pairedSmartTagDevice?.address); //when component will mount will ccheck for pairedDevice if pairedDevice is there then
    }
  }, [
    currentBluetoothStatus,
    scannedDeviceList,
    scannedSmartTagDevice,
    appState,
  ]);

  useBluetoothConstantlyListenState(isEnabled => {
    // console.log("Bluetooth state changed:", isEnabled);
    setCurrentBluetoothStatus(isEnabled);
    // Take appropriate action based on Bluetooth state
  });

  const requestBluetoothPermissions = async () => {
    try {
      const result = await checkMultiple([PERMISSIONS.ANDROID.BLUETOOTH_SCAN]);
      // console.log("Permission status:", result);

      if (result !== 'granted') {
        const requestResult = await request(PERMISSIONS.ANDROID.BLUETOOTH_SCAN);
        // console.log("Request result:", requestResult);

        if (requestResult === 'granted') {
          // console.log("Bluetooth permission granted.");
          // Do something after permission is granted
        } else {
          // console.log("Bluetooth permission denied.");
          // Handle the case when permission is denied
        }
      } else {
        // console.log("Bluetooth permission already granted.");
        // Do something if permission is already granted
      }
    } catch (error) {
      console.error(
        'Error checking or requesting Bluetooth permission:',
        error,
      );
      // Handle error
    }
  };

  async function getBluetoothState() {
    const isEnabled = await BluetoothStatus.state();
    // console.log("🚀 ~ getBluetoothState ~ isEnabled:", isEnabled);
    if (!isEnabled) {
      // Alert.alert(
      //   "Enable Bluetooth",
      //   "Please enable bluetooth in order to use Tracking Feature."
      // );
      setIsConnected(false);
      dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
    }
  }

  //intitialization & clean up effecct of stop scanning if component unmount
  useEffect(() => {
    getBluetoothState();
    const initializeBleManager = async () => {
      // Request Bluetooth permissions if running on Android
      if (Platform.OS === 'android') {
        await requestBluetoothPermissions();
      }

      // Initialize the BleDeviceManager
      const bleManagerInitialized = await NutSdkModule.initializeBleManager();
      // console.log("bleManagerInitialized-->", bleManagerInitialized);
      // Binding
      // await NutSdkModule.bindBleService();
    };

    initializeBleManager();

    return () => {
      // Stop scanning when the component unmounts
      // NutSdkModule.stopScanning();
    };
  }, []);

  //write a function here in the useEffect to catch the event coming from the native module
  //that is the scanned bluetooth nut ble devices
  // and store it in a state
  useEffect(() => {
    // Listen for the "ScannedDevices" event emitted from the native module
    const subscription = DeviceEventEmitter.addListener(
      'ScannedDevices',
      scannedDevicesArray => {
        // console.log(
        //   "scanned Devicec--->>>>",
        //   scannedDevicesArray,
        //   typeof scannedDevicesArray
        // );

        setScannedDeviceList(scannedDevicesArray);
      },
    );

    // Unsubscribe when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  //For iOS only for getting device list..
  useEffect(() => {
    if (Platform.OS === 'ios') {
      // Check if NutSdkModule exists (optional)
      if (NativeModules.NutSdkModule) {
        try {
          const eventEmitter = new NativeEventEmitter(NutSdkModule);
          const deviceDiscoveredListener = eventEmitter.addListener(
            'discoveredDevice',
            deviceData => {
              const existingDeviceIndex = scannedDeviceList.findIndex(
                device => device.device === deviceData.device,
              );

              if (existingDeviceIndex === -1) {
                // Device not found in list
                // Update scannedDeviceList with unique device
                setScannedDeviceList([...scannedDeviceList, deviceData]);
              } else {
                // Optionally update device data if needed (e.g., latest RSSI)
                console.log('Duplicate Device:', deviceData); // Optional logging
              }
            },
          );

          const stateUpdateListener = eventEmitter.addListener(
            'didUpdateState',
            stateData => {
              // console.log('Central Manager State Updated:', stateData);
            },
          );

          const clickListener = eventEmitter.addListener('didClicked', data => {
            // console.log('Device clicked:', data);
          });

          const connectionFailureListener = eventEmitter.addListener(
            'didFailedToConnect',
            data => {
              // console.log('Failed to Connect:', data);
            },
          );

          const batteryUpdateListener = eventEmitter.addListener(
            'didUpdateBattery',
            data => {
              // console.log('Battery Updated:', data);
              const {battery} = data;
              setBatteryLevel(battery);
            },
          );

          const rssiUpdateListener = eventEmitter.addListener(
            'didUpdateRSSI',
            data => {
              // console.log('RSSI Updated:', data);
            },
          );

          const connectListener = eventEmitter.addListener(
            'deviceDidConnect',
            data => {
              console.log(
                'Device Connected from Listener connectListner: jŚSSSSSS',
                data,
              );
              if (!isEmpty(pairedSmartTagDevice)) {
                if (data?.device === pairedSmartTagDevice?.address) {
                  setIsConnected(true);
                  dispatch(
                    bluetoothActions.setSmartTagDeviceConnectionState(true),
                  );
                }
              }
            },
          );

          const disconnectListener = eventEmitter.addListener(
            'deviceDidDisconnected',
            data => {
              if (data?.device === pairedSmartTagDevice?.address) {
                setIsConnected(false);
                dispatch(
                  bluetoothActions.setSmartTagDeviceConnectionState(false),
                );
                setScannedDeviceList([]);
                NutSdkModule.startScanning(); // Start scanning
              }

              console.log('Device Disconnected:Listener connectListner:', data);
            },
          );

          return () => {
            deviceDiscoveredListener.remove();
            stateUpdateListener.remove();
            clickListener.remove();
            connectionFailureListener.remove();
            batteryUpdateListener.remove();
            rssiUpdateListener.remove();
            connectListener.remove();
            disconnectListener.remove();
          };
        } catch (error) {
          console.error('Error subscribing to event:', error);
        }
      } else {
        console.warn('NutSdkModule not available');
      }
    }
  }, []);

  useEffect(() => {
    // console.log("HUM CHAKLTE HAI>>>>>>>>");
    if (scannedDeviceList.length > 0) {
      // console.log("HUM YAHA B CHAKLTE HAI>>>>>>>>");
      setIsScanning(false);
      NutSdkModule.stopScanning(); // Stop scanning
    }
  }, [scannedDeviceList]);

  async function checkLocationPermission() {
    GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: !appState ? 15000 : 0,
    })
      .then(location => {
        // console.log("location tada---->>>", location);
        return true;
      })
      .catch(async error => {
        console.warn('error -- location', error);
        // Toast.show(
        //   "Please enable your location service to send emergency alert."
        // );
        return false;
      });
  }

  const handleScanToggle = async () => {
    const isEnabled = await BluetoothStatus.state();
    // console.log("🚀 ~ handleScanToggle ~ isEnabled:", isEnabled);
    await checkLocationPermission();

    if (!isEnabled) {
      Alert.alert(
        'Enable Bluetooth',
        'Please enable bluetooth & location in order to use Tracking Feature.',
      );
      return;
    }

    if (!isScanning) {
      NutSdkModule.startScanning(); // Start scanning
    } else {
      NutSdkModule.stopScanning(); // Stop scanning
      // setScannedDevices([]); // Clear scanned devices
    }
    setIsScanning(prevIsScanning => !prevIsScanning);
  };

  const connectToDevice = async deviceAddress => {
    const isEnabled = await BluetoothStatus.state();
    if (!isEnabled) {
      Alert.alert(
        'Enable Bluetooth',
        'Please enable bluetooth in order to use Tracking Feature.',
      );
      dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
      setIsConnected(false);
      return;
    }
    // console.log("Device Address~~~~~>>>>>", deviceAddress);
    // console.log("Device Address---->>>RC:", selectedScannedDevice?.address);
    setIsConnectingLoading(true);
    dispatch(bluetoothActions.setSmartTagDeviceConnectionLoading(true));
    if (!deviceAddress) {
      Toast.show('Please select device first');
      return;
    }

    if (!selectedScannedDevice?.address) {
      // console.log(
      //   "No device selected BUT WILL CHECK IF THE REDUX HAS DEVICE STORED THEN WILL CONNECTB THAT--->>>"
      // );
    }

    if (!isEmpty(pairedSmartTagDevice)) {
      // console.log("SO THERE IS PAIRED DEVICE AND WILL CONNECT TO THAT--->>>");
    }
    NutSdkModule.connectToDevice(deviceAddress)
      .then(res => {
        // console.log("Connected to device successfully.", res);
        let connectedDeviceInfo = res;
        if (Platform.OS === 'android') {
          connectedDeviceInfo = JSON.parse(res);
        }
        // console.log("🚀 ~ .then ~ connectedDeviceInfo:", connectedDeviceInfo);
        //if gets connected then will store in redux
        // dispatch(setSmartTagDevice(connectedDeviceInfo));
        dispatch(
          bluetoothActions.setSmartTagDevice({
            name: connectedDeviceInfo.name,
            customName: 'Qeridoo Smart Tag',
            address: connectedDeviceInfo.address,
            id: connectedDeviceInfo.id,
            product_id: connectedDeviceInfo?.productId,
          }),
        );
        setTimeout(() => {
          setIsConnectingLoading(false);
          dispatch(bluetoothActions.setSmartTagDeviceConnectionLoading(false));
        }, 5000);
        getCurrentLocation();
        setIsConnected(true);
        dispatch(bluetoothActions.setSmartTagDeviceConnectionState(true));

        // Perform any additional actions after successful connection
      })
      .catch(error => {
        // console.log("Error connecting to device:IOOOSS", error.message);
        if (
          error.message === 'No devices scanned. Please start scanning first.'
        ) {
          console.warn('Please start scanning before connecting');
          Toast.show('Please Unpair and statrt scanning first');
        } else {
          console.error('Error connecting to device:', error);
          Toast.show(JSON.stringify(error));
        }
        setIsConnectingLoading(false);
        dispatch(bluetoothActions.setSmartTagDeviceConnectionLoading(false));
      });
  };

  const checkingIsDeviceConnect = async deviceAddress => {
    //check if paired device exist in redux
    if (!isEmpty(pairedSmartTagDevice)) {
      //if pairedDeviceList is not empty then check if saved device is connected or not
      // console.log(
      //   "NOW CHECKING FUNCTION EXCEUTED----->>>>>",
      //   pairedSmartTagDevice
      // );

      NutSdkModule.isConnected(deviceAddress)
        .then(isConnected => {
          // console.log(` REDUX STORED DEVICE STATUS--->>>>`, isConnected);
          setIsConnected(isConnected);
          dispatch(
            bluetoothActions.setSmartTagDeviceConnectionState(isConnected),
          );

          // console.log(
          //   "IF DEVICE IS NOT CONNECTED THEN WILL CHECK SCANNED RESULT FOR DEVICE---->>",
          //   isConnected,
          //   scannedDeviceList
          // );

          if (!isConnected && scannedDeviceList.length > 0) {
            // console.log(
            //   "DEVICE NOT CONECTED BUT WE HAVE THE DEVICE IN THE LIST--->>"
            // );
            const deviceOnButNotConnected = scannedDeviceList.find(
              device => device.id === pairedSmartTagDevice?.id,
            );
            // console.log(
            //   "NOW WE WILL FIND THE DEVICE FROM SCANNED LIST ANDTHEN START IT CONNECTION PROCESS--->>>",
            //   deviceOnButNotConnected
            // );

            if (deviceOnButNotConnected) {
              // console.log("NOW EXECCUTING CONNECTTODEVICE FUNCTION---->>>");
              connectToDevice(deviceOnButNotConnected?.address);
            }
          }
        })
        .catch(error => {
          console.error(error);
        });
    } else {
      // console.log("No paired device found");
      setIsConnected(false);
      dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
    }

    // console.log(
    //   "when bt status change then is thsis checkIngCOnnect run again"
    // );
    if (!deviceAddress) {
      return;
    }
  };

  //Ring Device
  const ringDevice = async () => {
    setisRing(true);
    const isEnabled = await BluetoothStatus.state();
    if (!isEnabled) {
      Alert.alert(
        'Enable Bluetooth',
        'Please enable bluetooth in order to use Tracking Feature.',
      );
      dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
      setIsConnected(false);
      return;
    }
    if (Platform.OS === 'android') {
      NutSdkModule.ringDevice(
        (!isEmpty(pairedSmartTagDevice) && pairedSmartTagDevice.address) ||
          pairedSmartTagDevice?.address,
      )
        .then(() => {
          // console.log("Device ringing...");
        })
        .catch(error => {
          console.error('Error ringing device:', error);
        });
    } else {
      NutSdkModule.beepDevice(pairedSmartTagDevice?.address, true, 30)
        .then(result => {
          // console.log("Beep command successful:", result);
        })
        .catch(error => {
          console.error('Beep command failed:', error);
          if (error.message.includes('Failed to beep device with identifier')) {
            Toast.show('Please Unpair and start scanning first');
          }
        });
    }
  };

  //Stop Ring Device
  const stopRingDevice = async () => {
    setisRing(false);
    const isEnabled = await BluetoothStatus.state();
    if (!isEnabled) {
      Alert.alert(
        'Enable Bluetooth',
        'Please enable bluetooth in order to use Tracking Feature.',
      );
      dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
      setIsConnected(false);
      return;
    }
    if (Platform.OS === 'android') {
      NutSdkModule.stopRingingDevice(pairedSmartTagDevice?.address)
        .then(() => {
          // console.log("Stopped ringing device");
        })
        .catch(error => {
          console.error('Error stopping device ringing:', error);
        });
    } else {
      NutSdkModule.beepDevice(pairedSmartTagDevice?.address, false, 30)
        .then(result => {
          // console.log("Beep command successful:", result);
        })
        .catch(error => {
          console.error('Beep command failed:', error);
        });
    }
  };

  //read battery level
  const readBatteryLevel = async () => {
    try {
      const isEnabled = await BluetoothStatus.state();
      if (!isEnabled) {
        Alert.alert(
          'Enable Bluetooth',
          'Please enable bluetooth in order to use Tracking Feature.',
        );
        dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
        setIsConnected(false);
        return;
      }
      await NutSdkModule.readBattery(pairedSmartTagDevice?.address);
      // console.log("Battery level:");
    } catch (error) {
      console.error('Error reading battery level:', error);
    }
  };

  //disconnect from device
  const disconnectFromDevice = async () => {
    try {
      const resp = await NutSdkModule.disconnectFromDevice(
        pairedSmartTagDevice?.address,
      );
      // console.log("Disconnect response:", resp);
      setIsConnected(false);
      dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
      // console.log("Disconnected from device successfully");
    } catch (error) {
      console.error('Error disconnecting from device:', error);
    }
  };

  const disconnectFromDeviceRemoveFromRedux = async () => {
    try {
      // console.log(
      //   "Disconnecting from device...",
      //   pairedSmartTagDevice?.address
      // );
      if (pairedSmartTagDevice?.address) {
        const resp = await NutSdkModule.disconnectFromDevice(
          pairedSmartTagDevice?.address,
        );
        // console.log("Disconnect response:", resp);
        dispatch(bluetoothActions.setSmartTagDevice({}));
        dispatch(bluetoothActions.setSmartTagDeviceLocation({}));
        setScannedDeviceList([]);
        setIsConnected(false);
        dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
        // console.log("Disconnected from device successfully");
      } else {
        dispatch(bluetoothActions.setSmartTagDevice({}));
        setScannedDeviceList([]);
      }
    } catch (error) {
      console.error('Error disconnecting from device:', error);
      if (error.message.includes('Device not found with address')) {
        dispatch(bluetoothActions.setSmartTagDevice({}));
        setScannedDeviceList([]);
      }
    }
  };

  const handleDeviceDisconnected = event => {
    const {deviceName} = event;
    // console.log(
    //   `Device disconnected Event catch in react component===>>: ${deviceName}`
    // );
    setIsConnected(false);
    dispatch(bluetoothActions.setSmartTagDeviceConnectionState(false));
    getCurrentLocation(); //storing mobile location...
    NutSdkModule.startScanning();
    // Vibration.vibrate([1000, 1000, 1000, 1000]);
    // Vibration.vibrate(90);
  };

  const [isVibrating, setIsVibrating] = useState(false);

  // const handleVibrate = () => {
  //   // console.log("Vibrating...");
  //   setIsVibrating(true);
  //   Vibration.vibrate(90);
  // };

  // const handleStopVibrate = () => {
  //   setIsVibrating(false);
  //   Vibration.cancel();
  // };

  useEffect(() => {
    const deviceDisconnectedListener = DeviceEventEmitter.addListener(
      'DeviceDisconnected',
      handleDeviceDisconnected,
    );

    // Unsubscribe when the component unmounts
    return () => {
      // DeviceEventEmitter.removeListener(
      //   "DeviceDisconnected",
      //   handleDeviceDisconnected
      // );
      deviceDisconnectedListener.remove();
    };
  }, []);

  const handleDeviceConnected = deviceData => {
    // Update the state with the connected device data
    // console.log(
    //   "🚀 ~ handleDeviceConnected ~ deviceData event in react:",
    //   deviceData
    // );
    setIsConnected(true);
    dispatch(bluetoothActions.setSmartTagDeviceConnectionState(true));
  };

  useEffect(() => {
    // Subscribe to the DeviceConnected event
    const deviceConnectedListener = DeviceEventEmitter.addListener(
      'DeviceConnected',
      handleDeviceConnected,
    );

    // Cleanup function to unsubscribe when the component unmounts
    return () => {
      deviceConnectedListener.remove();
    };
  }, []);

  const handleBatteryChangeEvent = batteryInfo => {
    // console.log("Battery Info--->>>", batteryInfo);
    setBatteryLevel(batteryInfo?.battery);
  };

  useEffect(() => {
    // Subscribe to the DeviceConnected event
    const batteryChangeListener = DeviceEventEmitter.addListener(
      'onBatteryChanged',
      handleBatteryChangeEvent,
    );

    // Cleanup function to unsubscribe when the component unmounts
    return () => {
      batteryChangeListener.remove();
    };
  }, []);

  const antilostFeature = () => {
    if (enableAntiLost && !isEmpty(pairedSmartTagDevice)) {
      // To enable disconnect alert
      NutSdkModule.enableAntiLost(pairedSmartTagDevice?.address, true)
        .then(() => {
          // console.log("Disconnect alert enabled TRUE");
          setEnableAntiLost(false);
        })
        .catch(error =>
          console.error('Failed to enable disconnect alert:', error),
        );
    } else {
      // To disable disconnect alert
      NutSdkModule.enableAntiLost(pairedSmartTagDevice?.address, false)
        .then(() => {
          // console.log("Disconnect alert disabled fALSE");
          setEnableAntiLost(true);
        })
        .catch(error =>
          console.error('Failed to disable disconnect alert:', error),
        );
    }
  };

  // Save Tracker Details---
  const submitDevice = async () => {
    setLoader(true);
    try {
      const deviceData = {
        trailer_id: details,
        platform: Platform.OS === 'android' ? 'ANDROID' : 'IOS',
        invoice_doc: '',
        bike_model: '',
      };
      const response = await getApiData(
        BaseSetting.endpoints.addProductDevice,
        'POST',
        deviceData,
        '',
        true,
      );
      if (response.success) {
        navigation.navigate('DrawerNav');
        setLoader(false);
      } else {
        setLoader(false);
        navigation.navigate('DrawerNav');
        Toast.show(response?.message);
      }
    } catch (err) {
      setLoader(false);
      console.log('ERRR==', err);
    }
  };

  // console.log(
  //   "pairedSmartTagDevice--->>> ,",
  //   pairedSmartTagDevice,
  //   " isConnected--->>>",
  //   isConnected,
  //   "isSMartTagDeviceConnected" ,
  //   isSmartTagDeviceConnected
  // );

  return (
    <>
      <ScrollView
        contentContainerStyle={[
          {
            flexGrow: 1,
            backgroundColor: '#DAE7DC',
          },
        ]}
        bounces={false}
        showsVerticalScrollIndicator={false}>
        <View style={{flex: 1}}>
          <MapView
            style={{
              height: '100%',
              width: '100%',
            }}
            customMapStyle={smartTagMapStyle}
            provider={PROVIDER_GOOGLE}
            region={{
              latitude:
                loc?.latitude || lastKnownSmartTagLocation?.latitude || 0,
              longitude:
                loc?.longitude || lastKnownSmartTagLocation?.longitude || 0,
              latitudeDelta: 0.015,
              longitudeDelta: 0.0121,
            }}
            pitchEnabled={false}
            rotateEnabled={false}
            scrollEnabled={true}
            zoomEnabled={true}>
            {!isEmpty(lastKnownSmartTagLocation) &&
              lastKnownSmartTagLocation?.latitude &&
              !isEmpty(pairedSmartTagDevice) && (
                <MapMarker
                  coordinate={{
                    latitude:
                      loc?.latitude || lastKnownSmartTagLocation?.latitude || 0,
                    longitude:
                      loc?.longitude ||
                      lastKnownSmartTagLocation?.longitude ||
                      0,
                  }}>
                  <View>
                    {isSmartTagDeviceConnected ? (
                      <SvgFromXml
                        xml={commonSvg.SmartTagSvgOn}
                        width={40}
                        height={40}
                      />
                    ) : (
                      // <SmartTagSvg width={40} height={40} />
                      <SvgFromXml
                        xml={commonSvg.SmartTagSvg}
                        width={40}
                        height={40}
                      />
                    )}
                  </View>
                </MapMarker>
              )}
          </MapView>
        </View>
        {!isEmpty(pairedSmartTagDevice) ? (
          <View
            style={{
              flex: 1,
              backgroundColor: BaseColor.whiteColor,
              width: '100%',
              maxHeight: '45%',
              borderTopRightRadius: 24,
              borderTopLeftRadius: 24,
              paddingVertical: 20,
              paddingHorizontal: 20,
              overflow: 'hidden',
            }}>
            <View
              style={{
                paddingHorizontal: 10,
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View>
                <Text
                  style={[
                    styles.title,
                    {
                      fontSize: 20,
                      color: '#5E5F60',
                    },
                  ]}>
                  {isConnected || isSmartTagDeviceConnected
                    ? pairedSmartTagDevice.customName
                    : 'Qeridoo Smart Tag'}
                </Text>
                <Text
                  style={{
                    color: '#5E5F60',
                    fontFamily: FontFamily.regular,
                    fontSize: 14,
                    paddingVertical: 4,
                  }}>
                  {lastKnownSmartTagLocation?.latitude
                    ? lastKnownSmartTagLocation?.localAddress1
                      ? lastKnownSmartTagLocation?.localAddress1
                      : lastKnownSmartTagLocation?.localAddress2
                      ? lastKnownSmartTagLocation?.localAddress2
                      : lastKnownSmartTagLocation?.localAddress3
                      ? lastKnownSmartTagLocation?.localAddress3
                      : lastKnownSmartTagLocation?.localAddress1 &&
                        lastKnownSmartTagLocation?.localAddress3
                      ? `${lastKnownSmartTagLocation?.localAddress1}, ${lastKnownSmartTagLocation?.localAddress3}`
                      : 'N/A'
                    : 'N/A'}
                </Text>
                <Text
                  style={{
                    color: '#5E5F60',
                    fontFamily: FontFamily.regular,
                    fontSize: 14,
                    paddingVertical: 4,
                  }}>
                  Status :
                  <Text
                    style={{
                      color:
                        isConnectingLoading || isSmartTagConnectingLoading
                          ? BaseColor.textGrey1
                          : isConnected || isSmartTagDeviceConnected
                          ? BaseColor.primary
                          : BaseColor.error,
                      fontFamily: FontFamily.regular,
                    }}>
                    {isConnectingLoading || isSmartTagConnectingLoading
                      ? ' Connecting Please Wait...!'
                      : isConnected || isSmartTagDeviceConnected
                      ? ' Connected'
                      : ' Disconnected'}
                  </Text>
                </Text>
              </View>
              <View style={{alignSelf: 'center'}}>
                <CustomIcon
                  name="maps-global-thick"
                  size={24}
                  color={
                    isConnected || isSmartTagDeviceConnected
                      ? BaseColor.blackColor
                      : BaseColor.textGrey
                  }
                />
              </View>
            </View>
            <View
              style={{
                backgroundColor: BaseColor.whiteColor,
                borderColor: BaseColor.lightgray,
                borderRadius: 12,
                width: '100%',
                paddingHorizontal: 10,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    fontSize: 16,
                    fontFamily: FontFamily.bold,
                    paddingVertical: 10,
                  }}>
                  Controls
                </Text>
                {isRing && (
                  <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={() => stopRingDevice()}>
                    <Text
                      style={{
                        fontSize: 16,
                        fontFamily: FontFamily.regular,
                        color: '#B58787',
                      }}>
                      Stop <Icon name="bell-off-outline" size={16} />
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
              {!isConnected || !isSmartTagDeviceConnected ? (
                <>
                  <TouchableOpacity
                    style={styles.controllerDesign}
                    onPress={() => {
                      connectToDevice(pairedSmartTagDevice?.address);
                    }}>
                    <Text style={styles.controllerText}>Connect Device</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.controllerDesign}
                    onPress={() => {
                      disconnectFromDeviceRemoveFromRedux();
                    }}>
                    <Text style={styles.controllerText}>
                      Un-Pair & Remove Device
                    </Text>
                  </TouchableOpacity>
                </>
              ) : (
                <>
                  {connectedArr &&
                    connectedArr.map(li => {
                      return (
                        <TouchableOpacity
                          style={[styles.controllerDesign]}
                          onPress={() => {
                            if (li?.id === 1) {
                              ringDevice();
                            } else if (li?.id === 2) {
                              disconnectFromDeviceRemoveFromRedux();
                            } else if (li?.id === 3) {
                              console.log('connect device=-----');
                            }
                          }}>
                          <Text style={styles.controllerText}>{li.name}</Text>
                        </TouchableOpacity>
                      );
                    })}
                  {Platform.OS === 'android' && (
                    <TouchableOpacity
                      style={{
                        backgroundColor: BaseColor.whiteColor,
                        borderWidth: 0.5,
                        borderColor: BaseColor.lightgray,
                        borderRadius: 12,
                        padding: 10,
                        width: '100%',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                      }}
                      onPress={() => {
                        antilostFeature();
                      }}>
                      <Text
                        style={[
                          styles.title,
                          {
                            fontSize: 20,
                            color: BaseColor.blackColor,
                          },
                        ]}>
                        {!enableAntiLost ? 'Disable' : 'Enable'} Anti-Lost
                        Feature
                      </Text>

                      <Icon
                        name="link-variant-remove"
                        size={24}
                        color={BaseColor.blackColor}
                      />
                    </TouchableOpacity>
                  )}
                </>
              )}
            </View>
            <View style={{marginTop: 20}}>
              <CButton
                title={translate('GotoHomePage')}
                style={{width: '80%'}}
                loader={loader}
                onPress={() => {
                  if (details) {
                    submitDevice();
                  } else {
                    navigation.navigate('DrawerNav');
                  }
                }}
              />
            </View>
          </View>
        ) : (
          <Modal
            visible={!isConnected}
            transparent
            onRequestClose={() => navigation.goBack()}
            style={{
              flex: 1,
            }}>
            <View
              style={{
                flex: 1,
                backgroundColor: BaseColor.black80,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <View
                style={{
                  backgroundColor: BaseColor.whiteColor,
                  borderRadius: 24,
                  width: '90%',
                  paddingVertical: 50,
                  paddingHorizontal: 5,
                }}>
                <View style={{alignItems: 'center'}}>
                  <Text
                    style={[
                      styles.title,
                      {fontFamily: FontFamily.regular, color: '#261E27'},
                    ]}>
                    SMART TAG
                  </Text>
                  <SvgFromXml
                    xml={commonSvg.SmartTagIcon}
                    style={{
                      marginVertical: 20,
                    }}
                  />
                </View>
                <View style={{alignItems: 'center'}}>
                  {isScanning ? (
                    <ActivityIndicator
                      size={'small'}
                      color={BaseColor.primary2}
                      animating
                    />
                  ) : (
                    <>
                      {isEmpty(scannedDeviceList) ? (
                        <View style={styles.dropdownButtonStyle}>
                          <Text style={{color: BaseColor.greyColor}}>
                            {promtText}
                          </Text>
                        </View>
                      ) : (
                        <SelectDropdown
                          data={scannedDeviceList}
                          onSelect={(selectedItem, index) => {
                            setSelectedScannedDevice(selectedItem);
                          }}
                          renderButton={(selectedItem, isOpened) => {
                            return (
                              <View style={styles.dropdownButtonStyle}>
                                <Text style={styles.dropdownButtonTxtStyle}>
                                  {(selectedItem &&
                                    selectedItem.name === 'nut' &&
                                    'Qeridoo Smart Tag') ||
                                    'Select your device'}
                                </Text>
                                <Icon
                                  name={
                                    isOpened ? 'chevron-up' : 'chevron-down'
                                  }
                                  style={styles.dropdownButtonArrowStyle}
                                />
                              </View>
                            );
                          }}
                          renderItem={(item, index, isSelected) => {
                            return (
                              <View
                                style={{
                                  ...styles.dropdownItemStyle,
                                  ...(isSelected && {
                                    backgroundColor: '#D2D9DF',
                                  }),
                                }}>
                                <Text style={styles.dropdownItemTxtStyle}>
                                  {item.name === 'nut'
                                    ? 'Qeridoo Smart Tag'
                                    : item.name}
                                </Text>
                              </View>
                            );
                          }}
                          showsVerticalScrollIndicator={false}
                          dropdownStyle={styles.dropdownMenuStyle}
                        />
                      )}
                    </>
                  )}
                </View>
                <View style={{alignItems: 'center', marginTop: 10}}>
                  {selectedScannedDevice && !isEmpty(scannedDeviceList) ? (
                    <CButton
                      title={
                        selectedScannedDevice ? 'Connect' : 'No Device Selected'
                      }
                      iconBg={BaseColor.whiteColor}
                      style={[
                        {
                          width: '90%',
                          marginTop: 20,
                        },
                      ]}
                      onPress={() => {
                        connectToDevice(selectedScannedDevice?.address);
                        setSelectedScannedDevice(null);
                      }}
                    />
                  ) : (
                    <CButton
                      title={isScanning ? 'Stop Searching' : 'Search Devices'}
                      iconBg={BaseColor.whiteColor}
                      style={[
                        {
                          width: '90%',
                          marginTop: 20,
                        },
                      ]}
                      onPress={() => {
                        handleScanToggle();
                      }}
                    />
                  )}
                </View>
              </View>
            </View>
          </Modal>
        )}
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  title: {
    fontFamily: FontFamily.bold,
    color: '#5E5F60',
    fontSize: 22,
    flexShrink: 0,
  },
  dropdownButtonStyle: {
    width: '90%',
    height: 50,
    backgroundColor: '#E9ECEF',
    borderRadius: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
  },
  dropdownButtonTxtStyle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '500',
    color: '#151E26',
  },
  dropdownButtonArrowStyle: {
    fontSize: 28,
  },
  dropdownButtonIconStyle: {
    fontSize: 28,
    marginRight: 8,
  },
  dropdownMenuStyle: {
    backgroundColor: '#E9ECEF',
    borderRadius: 12,
  },
  dropdownItemStyle: {
    width: '100%',
    flexDirection: 'row',
    paddingHorizontal: 12,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
  },
  dropdownItemTxtStyle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '500',
    color: '#151E26',
  },
  dropdownItemIconStyle: {
    fontSize: 28,
    marginRight: 8,
  },
  controllerDesign: {
    backgroundColor: '#F5F8F8',
    borderWidth: 0.5,
    borderColor: '#E5ECEC',
    borderRadius: 5,
    marginBottom: 10,
  },
  controllerText: {
    fontSize: 14,
    color: '#5E5F60',
    padding: 10,
    fontFamily: FontFamily.regular,
  },
});

export default SmartTag;
