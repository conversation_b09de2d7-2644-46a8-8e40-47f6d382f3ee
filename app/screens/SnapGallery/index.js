/* eslint-disable quotes */
import {useTheme} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  Animated,
  Dimensions,
  FlatList,
  Image,
  Modal,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Toast from 'react-native-simple-toast';
import {flattenDeep} from 'lodash';
import CHeader from '../../components/CHeader';
import BaseSetting from '../../config/setting';
import {translate} from '../../lang/Translate';
import {getApiData} from '../../utils/apiHelper';
import styles from './styles';
import {sendErrorReport} from '../../utils/commonFunction';
import {FontFamily} from '../../config/typography';
import {Images} from '../../config/Images';
import {CustomIcon} from '../../config/LoadIcons';
import AIcon from 'react-native-vector-icons/AntDesign';

const {width, height} = Dimensions.get('window');

export default function SnapGallery({navigation}) {
  const subTabSize = width / 2.8;
  const colors = useTheme();
  const BaseColor = colors.colors;
  const [loader, setLoader] = useState(false);
  const [ecomCatalougeList, setEcomCatalougeList] = useState([]);
  const [selectedTab, setSelectedTab] = useState('repeat');
  const [modalVisible, setModalVisible] = useState(false);
  const [indexData, setIndexData] = useState(0);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [translateValue] = useState(new Animated.Value(indexData * subTabSize));
  const tabArr = [
    {type: 'images', name: translate('Images')},
    {type: 'video', name: translate('Videos')},
  ];

  const setspring = index => {
    Animated.spring(translateValue, {
      toValue: index * subTabSize,
      velocity: 10,
      useNativeDriver: true,
    }).start();
  };

  useEffect(() => {
    setspring(indexData);
  }, [selectedTab]);

  /** this function for get Product List
   * @function getImageList
   * @param {object} data token, per_page, page, category_id
   */
  const getImageList = type => {
    setLoader(true);
    getApiData(BaseSetting.endpoints.ecomCatalougeList, 'GET')
      .then(response => {
        if (response.success) {
          const tempPArr = flattenDeep([ecomCatalougeList, response.data]);
          setTimeout(() => {
            setEcomCatalougeList(tempPArr);
          }, 0);
        } else {
          Toast.show(response.message);
        }
        setLoader(false);
      })
      .catch(err => {
        setLoader(false);
        Toast.show('Something went wrong while getting ECOM Catalouge list');
        sendErrorReport(err, 'get_product_list');
      });
  };

  useEffect(() => {
    getImageList();
  }, []);

  const staticArray = [
    {
      id: 1,
      img: Images.babyImage,
    },
    {
      id: 2,
      img: Images.babyImage,
    },
    {
      id: 3,
      img: Images.babyImage,
    },
    {
      id: 4,
      img: Images.babyImage,
    },
    {
      id: 5,
      img: Images.babyImage,
    },
    {
      id: 6,
      img: Images.babyImage,
    },
    {
      id: 7,
      img: Images.babyImage,
    },
    {
      id: 8,
      img: Images.babyImage,
    },
    {
      id: 9,
      img: Images.babyImage,
    },
    {
      id: 10,
      img: Images.babyImage,
    },
  ];

  const handleShare = () => {
    // Implement share functionality
    setModalVisible(false);
  };

  const handleDelete = () => {
    // Implement delete functionality here
    setModalVisible(false);
  };

  const renderData = ({item, index}) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onLongPress={() => {
          setModalVisible(true);
          setSelectedIndex(index);
        }}
        style={{
          margin: 3,
          width: width / 3.5,
          height: width / 3.5,
          borderRadius: 8,
        }}>
        <Image
          source={item?.img}
          style={{width: '100%', height: '100%', borderRadius: 8}}
        />
        {selectedIndex === index && modalVisible && (
          <View style={styles.checkbox}>
            <AIcon name="checkcircle" color={BaseColor.primary} size={20} />
          </View>
        )}
        {selectedTab?.type === 'video' && (
          <View
            style={{
              position: 'absolute',
              bottom: 10,
              left: 10,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              borderRadius: 10,
            }}>
            <Text
              style={{
                color: BaseColor.whiteColor,
                padding: 7,
                fontSize: 10,
                fontFamily: FontFamily.regular,
              }}>
              {'00:50'}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.root}>
      <CHeader
        title={translate('Snapshot Gallery')}
        gallery
        leftIconName={'left-arrow-thin'}
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <View style={styles.tabBar}>
        <Animated.View
          style={[
            styles.slider,
            {
              transform: [
                {
                  translateX: translateValue,
                },
              ],
              width: width / 2.5,
            },
          ]}
        />
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            justifyContent: 'space-around',
          }}>
          {tabArr.map((obj, index) => (
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => {
                setSelectedTab(obj);
                setIndexData(index);
              }}
              style={{
                ...styles.tab,
              }}>
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: FontFamily.regular,
                  color: indexData === index ? BaseColor.whiteColor : '#666666',
                }}>
                {obj.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      <View style={{marginVertical: 20}}>
        <FlatList
          bounces={false}
          numColumns={3}
          data={staticArray}
          keyExtractor={(item, index) => index}
          renderItem={renderData}
        />
      </View>
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <TouchableOpacity
          style={[styles.modalOverlay]}
          activeOpacity={1}
          onPress={() => setModalVisible(false)}>
          <View style={styles.modalContent}>
            <TouchableOpacity
              style={[styles.option, {paddingBottom: 20}]}
              onPress={handleShare}>
              <CustomIcon name="save" size={20} color="#6A6A6A" />
              <Text style={styles.optionText}>{translate('Share')}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.option} onPress={handleDelete}>
              <CustomIcon name="delete" size={20} color="#6A6A6A" />
              <Text style={styles.optionText}>{translate('Delete')}</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}
