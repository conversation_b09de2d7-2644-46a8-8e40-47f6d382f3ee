/* eslint-disable quotes */
import {StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
    marginHorizontal: 20,
  },
  tab: {
    flexDirection: 'row',
  },
  slider: {
    position: 'absolute',
    alignItems: 'center',
    borderRadius: 8,
    paddingVertical: 28,
    backgroundColor: '#87B5B3',
  },
  tabButton: {
    borderWidth: 1,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: BaseColor.neyonWhite,
    borderColor: '#8AB0AE',
  },
  activeTabButton: {
    borderWidth: 0,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: BaseColor.primary,
  },
  tabButtonText: {
    padding: 10,
    fontFamily: FontFamily.regular,
    fontSize: 16,
    color: BaseColor.primary,
  },
  tabBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: 30,
    borderWidth: 1,
    borderColor: '#ADADAD',
    borderRadius: 8,
    paddingVertical: 20,
    marginBottom: 20,
  },
  modalOverlay: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  checkbox: {
    position: 'absolute',
    right: 5,
    top: 5,
    width: 20,
    height: 20,
    borderRadius: 20,
    backgroundColor: 'white',
  },
  modalContent: {
    backgroundColor: BaseColor.whiteColor,
    borderRadius: 10,
    padding: 15,
    width: '30%',
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
  },
  optionText: {
    fontSize: 16,
    fontFamily: FontFamily.regular,
    color: '#6A6A6A',
    textAlign: 'center',
  },
});

export default styles;
