import React, {useEffect, useRef, useState} from 'react';
import {
  BackHandler,
  StatusBar,
  View,
  Platform,
  Alert,
  Image,
  Dimensions,
  Animated,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Toast from 'react-native-simple-toast';
import BaseColor from '../../config/colors';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import AuthAction from '../../redux/reducers/auth/actions';
import {sendErrorReport} from '../../utils/commonFunction';
import {FontFamily} from '../../config/typography';
import commonSvg from '../../assets/commonSvg/commonnSvg';
import {SvgXml} from 'react-native-svg';
import bluetoothActions from '../../redux/reducers/bluetooth/actions';
import {Images} from '../../config/Images';

/**
 *
 *@module SplashScreen
 *
 */
const SplashScreen = ({navigation}) => {
  const dispatch = useDispatch();
  const locationDisclouser = useSelector(
    state => state.auth.locationDisclouser,
  );
  // const animation = useSharedValue({ width: 5000, height: 5000 });
  const walkthrough = useSelector(state => state.auth.walkthrough);
  const {setLocationDisclouser} = AuthAction;
  const accessToken = useSelector(state => state.auth.accessToken);
  const [dsettings, setDSettings] = useState(false);
  const {width, height} = Dimensions.get('window');
  const IOS = Platform.OS === 'ios';

  // Scale Value of Animation start at 0.5
  const scaleValue = useRef(new Animated.Value(0.5)).current;

  useEffect(() => {
    Animated.timing(scaleValue, {
      toValue: 1, // Target scale value
      duration: 1000, // Duration of animation in milliseconds
      useNativeDriver: true, // Use native driver for better performance
    }).start();
    sendErrorReport(true, 'timeoutspl');
    setTimeout(() => {
      if (walkthrough) {
        navigation.navigate('Walkthrough');
        return;
      }
      if (accessToken) {
        navigation.navigate('DrawerNav');
      } else {
        navigation.navigate('RedirectLS');
      }
    }, 2500);
  }, []);

  function handleBackButtonClick() {
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  /** this function for get LanguageList
   * @function getLanguageList
   * @param {object} data {}
   */
  const getLanguageList = () => {
    getApiData(BaseSetting.endpoints.getLanguageList, 'post', {})
      .then(response => {
        if (response?.success) {
          dispatch(AuthAction.setLanguageList(response.data));
        }
      })
      .catch(err => {
        console.log('.catch -> err', err);
        // Toast.show('Something went wrong while getting lanugage list');
        sendErrorReport(err, 'getting_language');
      });
  };
  const showAlert = () => {
    Alert.alert(
      'Qeridoo',
      'Qeridoo collects location data to enable sending emergency SMS, handling from smart safety cushion even when the app is closed or not in use.',
      [
        {
          text: 'Close',
          style: 'cancel',
          onPress: () => dispatch(setLocationDisclouser(false)),
        },
      ],
      {
        cancelable: true,
      },
    );
  };
  useEffect(() => {
    if (Platform.OS === 'android') {
      if (locationDisclouser) {
        showAlert();
      }
    }
    dispatch(bluetoothActions.setScannedSmartTagDevice({}));
    getLanguageList();
  }, []);

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#343434',
      }}>
      <StatusBar
        backgroundColor="transparent"
        barStyle="dark-content"
        translucent
      />
      <Animated.View
        style={{
          transform: [{scale: scaleValue}],
        }}>
        <View style={{marginBottom: 25}}>
          <Image
            source={Images.qeridoo}
            style={{
              width: width / 2,
              height: height / 4.5,
              resizeMode: 'contain',
            }}
          />
        </View>
      </Animated.View>
      <Animated.View
        style={{
          transform: [{scale: scaleValue}],
        }}>
        <View style={{marginBottom: 10}}>
          <Image
            source={Images.qeridoo_white}
            style={{
              height: height / 20,
              resizeMode: 'contain',
            }}
          />
        </View>
      </Animated.View>
      <Animated.View
        style={{
          transform: [{scale: scaleValue}],
        }}>
        <SvgXml xml={commonSvg.slogan_white} style={{marginTop: 20}} />
      </Animated.View>
    </View>
  );
};

export default SplashScreen;
