import React, {memo, useRef} from 'react';
import {useEffect} from 'react';
import {BackHandler, Dimensions, Text} from 'react-native';
import {View} from 'react-native-animatable';
import styles from './styles';
import Toast from 'react-native-simple-toast';
import commonSvg from '../../assets/commonSvg/commonnSvg';
import {SvgXml} from 'react-native-svg';
import {translate} from '../../lang/Translate';
let backPressed = 0;

const {width, height} = Dimensions.get('window');
function SucessTrailerSetup({navigation}) {
  useEffect(() => {
    setTimeout(() => {
      navigation.navigate('DrawerNav');
    }, 3000);
  }, []);

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={styles.root}>
      <SvgXml xml={commonSvg.trailerSetup} width={width} height={height} />
      <View
        style={[
          styles.textContainer,
          {
            top: height / 2,
          },
        ]}>
        <Text style={styles.regitserTxt}>{translate('Congratulations!')}</Text>
        <Text style={[styles.regitserTxt, {fontSize: 20, paddingTop: 15}]}>
          {translate('You completed Trailer Setup!')}
        </Text>
        <Text style={styles.childtxt}>{`${translate(
          'You can add camera, tracker and other devices you own by tapping on the “+” icon.',
        )} `}</Text>
      </View>
    </View>
  );
}

export default SucessTrailerSetup;
