import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

const {height: dHeight, width: dWidth} = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
    marginTop: getStatusBarHeight() + 20,
  },
  textContainer: {
    position: 'absolute',
    alignSelf: 'center',
    alignItems: 'center',
  },
  regitserTxt: {
    color: '#1E293B',
    fontSize: 24,
    fontFamily: FontFamily.bold,
  },
  childtxt: {
    marginTop: 8,
    color: BaseColor.blackColor,
    fontSize: 12,
    textAlign: 'center',
    marginHorizontal: 25,
    fontFamily: FontFamily.regular,
    lineHeight: 15,
  },
  btnContainer: {
    flex: 1,
    justifyContent: 'space-evenly',
    alignItems: 'center',
    flexDirection: 'row',
  },
  skiptxt: {
    color: BaseColor.primary,
    fontSize: 16,
    fontFamily: FontFamily.regular,
    textDecorationLine: 'underline',
  },
});

export default styles;
