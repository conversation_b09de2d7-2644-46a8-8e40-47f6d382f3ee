//import liraries
import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import CustomHeader from '../../components/CustomHeader/CustomHeader';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {translate} from '../../lang/Translate';
import TriphistoryComponent from '../../components/TripHistoryComponent';
import {CustomIcon} from '../../config/LoadIcons';
import HighLights from '../../components/HighLights';

// create a component
const TripHistory = ({navigation}) => {
  const [tripHistoryList, setTripHistoryList] = useState([]);
  const [distance, setDistance] = useState(0);

  const getAllTripData = async () => {
    try {
      const response = await getApiData(
        BaseSetting.endpoints.getTripList,
        'GET',
      );
      if (response?.success) {
        if (response.data) {
          const parsedData = response?.data.map(item => {
            if (item.location_json) {
              return {...item, location_json: JSON.parse(item.location_json)};
            }
            return item;
          });
          setTripHistoryList(parsedData);
        }
      }
    } catch (error) {
      console.log('error for get all trip data ===', error);
    }
  };

  useEffect(() => {
    getAllTripData();
  }, []);

  return (
    <>
      <CustomHeader
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
        leftIconColor={BaseColor.blackColor}
        backBtn
        title={'Trip History'}
      />
      <View
        style={{
          flex: 1,
          backgroundColor: BaseColor.whiteColor,
          marginHorizontal: 20,
        }}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Text style={styles.titleColor}>{translate('Highlights')}</Text>
          <CustomIcon name="staraward" size={20} />
        </View>
        {distance > 20 ? (
          <Text
            style={{
              marginBottom: 10,
              fontFamily: FontFamily.regular,
              fontSize: 14,
              marginVertical: 10,
              marginBottom: 15,
            }}>
            {`Amazing! ${distance} KM of trails this week!`}
          </Text>
        ) : null}
        <View style={[styles.titleTextContainer, {marginTop: 10}]}>
          <Text
            style={{
              fontFamily: FontFamily.bold,
              fontSize: 20,
              color: BaseColor.blackColor,
            }}>
            Recent
          </Text>
          <Text style={styles.viewAllText}>View All</Text>
        </View>
        <TriphistoryComponent />
        <View style={styles.titleTextContainer}>
          <Text
            style={{
              fontFamily: FontFamily.bold,
              fontSize: 20,
              color: BaseColor.blackColor,
            }}>
            Saved Trips
          </Text>
          <Text style={styles.viewAllText}>View All</Text>
        </View>
        <TriphistoryComponent />
        <Text
          style={{
            fontFamily: FontFamily.bold,
            fontSize: 20,
            color: BaseColor.blackColor,
          }}>
          Metrics
        </Text>
        <View
          style={{
            justifyContent: 'space-between',
            borderRadius: 9,
            marginVertical: 20,
            backgroundColor: '#F3F6F6',
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.1,
            shadowRadius: 2,
          }}>
          <HighLights metrix detanceVal={val => setDistance(val)} />
        </View>
      </View>
    </>
  );
};

// define your styles
const styles = StyleSheet.create({
  card: {
    padding: 15,
    marginVertical: 8,
    marginHorizontal: 16,
    backgroundColor: BaseColor.whiteColor,
    // borderWidth: 1,
    borderRadius: 10,
    elevation: 5,
    shadowColor: 'black',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  title: {
    fontSize: 15,
    fontFamily: FontFamily.bold,
    color: BaseColor.primary3,
    textAlign: 'center',
  },
  details: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  detailItem: {
    alignItems: 'center',
  },
  detailText: {
    fontFamily: FontFamily.regular,
    color: BaseColor.blackColor,
    textAlign: 'center',
  },
  review: {
    fontFamily: FontFamily.regular,
    fontSize: 14,
  },
  distance: {
    fontFamily: FontFamily.regular,
    fontSize: 14,
  },
  about: {
    fontFamily: FontFamily.regular,
    fontSize: 14,
  },
  image: {
    width: 100,
    height: 100,
  },
  coordinate: {
    fontFamily: FontFamily.regular,
    fontSize: 14,
  },
  rating: {
    fontFamily: FontFamily.regular,
    fontSize: 14,
  },
  reviewCount: {
    fontFamily: FontFamily.regular,
    fontSize: 14,
  },
  website: {
    fontFamily: FontFamily.regular,
    fontSize: 14,
  },
  titleColor: {
    fontSize: 20,
    fontFamily: FontFamily.bold,
    color: BaseColor.blackColor,
    paddingRight: 10,
  },
  titleTextContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  viewAllText: {
    textDecorationLine: 'underline',
    color: BaseColor.primary,
    fontFamily: FontFamily.robotoLight,
  },
});

//make this component available to the app
export default TripHistory;
