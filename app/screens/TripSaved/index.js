import React, {useEffect, useRef, useState} from 'react';
import {Dimensions, View, StyleSheet, Image, Text} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';
import {CustomIcon} from '../../config/LoadIcons';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {Images} from '../../config/Images';
import CButton from '../../components/CButton';
import {translate} from '../../lang/Translate';
import {useDispatch} from 'react-redux';
import PlaceAction from '../../redux/reducers/place/actions';

const {width, height} = Dimensions.get('window');
function TripSaved({navigation, route}) {
  const dispatch = useDispatch();
  const {setPlaceLocation, setTripPlaceName} = PlaceAction;

  useEffect(() => {
    dispatch(setPlaceLocation({}));
    dispatch(setTripPlaceName(''));
  }, []);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: BaseColor.whiteColor,
      }}>
      <CustomIcon
        name="left-arrow-thin"
        size={25}
        color={BaseColor.primary}
        style={{marginHorizontal: 20, marginTop: getStatusBarHeight() + 20}}
        onPress={() => {
          navigation.navigate(translate('dashboard'));
        }}
      />
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Image
          source={Images.tripQeridoo}
          style={{width: '100%', height: 200, marginBottom: 20}}
          resizeMode="contain"
        />
        <CustomIcon
          name="check-rounded"
          size={90}
          color={BaseColor.primary}
          style={{position: 'absolute', left: width / 2.8, top: height / 3.3}}
        />
        <Text
          style={{
            fontSize: 20,
            fontFamily: FontFamily.bold,
            color: BaseColor.primary,
          }}>
          Trip Saved
        </Text>
      </View>
      <View style={styles.buttonContainer}>
        <CButton
          title={translate('ToDashboard')}
          iconBg={BaseColor.whiteColor}
          titleStyle={{
            fontFamily: FontFamily.bold,
          }}
          onPress={() => {
            navigation.navigate(translate('dashboard'));
          }}
        />
        <CButton
          title="Share Achievement! "
          style={styles.buttonStyle}
          titleStyle={styles.btntitleStyle}
          rightCustomIcon
          rightIcon={'achivemetns'}
          rightIconColor={BaseColor.primary}
          rightIconSize={24}
          // onPress={() => setShareModal(true)}
          anim
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  customView: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 10,
    width: 200,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  description: {
    fontSize: 12,
  },
  titleText: {
    fontSize: 16,
    fontFamily: FontFamily.bold,
    textAlign: 'center',
    color: BaseColor.primary,
    paddingTop: 25,
  },
  timeText: {
    fontSize: 14,
    color: '#096761',
    fontFamily: FontFamily.default,
  },
  buttonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    marginHorizontal: 30,
  },
  buttonStyle: {
    marginTop: 20,
    borderWidth: 1.5,
    borderColor: BaseColor.primary,
  },
  btntitleStyle: {
    fontSize: 16,
    color: BaseColor.primary,
    fontFamily: FontFamily.bold,
  },
});

export default TripSaved;
