import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  BackHandler,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Keyboard,
} from 'react-native';
import {useSelector} from 'react-redux';
import Toast from 'react-native-simple-toast';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import styles from './styles';
import BaseColor from '../../config/colors';
import CButton from '../../components/CButton';
import CInput from '../../components/CInput';
import {translate} from '../../lang/Translate';
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from '../../utils/commonFunction';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import CustomHeader from '../../components/CustomHeader/CustomHeader';
import {FontFamily} from '../../config/typography';
let backPressed = 0;

/**
 *
 *@module UpdatePassword
 *
 */
function UpdatePassword({navigation, route}) {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const confirmPass = useRef();
  const [newPasswordError, setNewPasswordError] = useState(false);
  const [confirmPasswordError, setConfirmPasswordError] = useState(false);
  const [newPasswordErrorTxt, setNewPasswordErrorTxt] = useState('');
  const [confirmPasswordErrorTxt, setConfirmPasswordErrorTxt] = useState('');
  const [loader, setloader] = useState(false);
  const userId = useSelector(state => state.auth.user_id);
  const [isUpperCase, setIsUpperCase] = useState(false);
  const [isLowerCase, setIsLowerCase] = useState(false);
  const [isNu, setIsNumber] = useState(false);
  const [isSpecial, setIsSp] = useState(false);
  const [isTwelve, setIsTwelve] = useState(false);
  const [hidePwd, setHidePwd] = useState({pass: true, confirmPass: true});
  const [showCheckList, setShowCheckList] = useState(false);

  // Validation function
  const validation = () => {
    const passVal =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,15}$/;
    enableAnimateInEaseOut();

    if (newPassword == '') {
      allErrorFalse();
      setNewPasswordError(true);
      setNewPasswordErrorTxt('Please enter New Password');
    } else if (!passVal.test(String(newPassword))) {
      allErrorFalse();
      setNewPasswordError(true);
      setNewPasswordErrorTxt(
        'Password must contain 8-15 characters, 1 x Upper case, 1 x Lower case, 1 x number and 1 x special character such as !,?,&',
      );
    } else if (newPassword != confirmPassword) {
      allErrorFalse();
      setConfirmPasswordError(true);
      setConfirmPasswordErrorTxt(
        'Confirm Password and New Password cannot match',
      );
    } else {
      allErrorFalse();
      updatePassword();
    }
  };

  const allErrorFalse = () => {
    setNewPasswordError(false);
    setConfirmPasswordError(false);
  };

  /** this function for update Password
   * @function updatePassword
   * @param {object} data user_id, new_password
   */
  const updatePassword = () => {
    setloader(true);
    const data = {
      user_id: userId,
      new_password: newPassword,
    };

    getApiData(BaseSetting.endpoints.updatePassword, 'POST', data)
      .then(response => {
        if (response.success) {
          setTimeout(() => {
            setloader(false);
          }, 2000);
          setTimeout(() => {
            navigation.navigate('Login');
          }, 3000);
        } else {
          setloader(false);
        }
      })
      .catch(err => {
        Toast.show('Something went wrong while changing password');
        sendErrorReport(err, 'changing_pass');
        setloader(false);
        console.log('ERRR', err);
      });
  };

  function handleBackButtonClick() {
    if (route && route.params === 'from myAccout') {
      navigation.goBack();
      return true;
    } else {
      if (backPressed > 0) {
        BackHandler.exitApp();
        backPressed = 0;
      } else {
        backPressed++;
        Toast.show('Press Again To Exit');
        setTimeout(() => {
          backPressed = 0;
        }, 2000);
        return true;
      }
      return true;
    }
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);
  function isUpper(str) {
    return /^(?=.*?[A-Z])/.test(str);
  }
  function isLower(str) {
    return /^(?=.*?[a-z])/.test(str);
  }
  function isNumericCheck(str) {
    return /^(?=.*?[0-9])/.test(str);
  }
  function isSpCheck(str) {
    return /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(str);
  }
  function isTw(str) {
    if (str.length >= 8) {
      return true;
    } else {
      return false;
    }
  }

  return (
    <View style={styles.root}>
      <View style={{marginTop: '5%'}}>
        <CustomHeader
          transparentView
          leftIconName="left-arrow"
          borderCircular
          backgroundColor={'#F1F5F9'}
          onLeftPress={() => {
            navigation.goBack();
          }}
          backBtn
        />
      </View>
      <KeyboardAvoidingView
        style={{flex: 1, justifyContent: 'center'}}
        behavior={Platform.OS === 'ios' ? 'height' : null}>
        <ScrollView
          contentContainerStyle={styles.mainContainer}
          bounces={false}>
          <View style={styles.loginTextView}>
            <Text style={[styles.loginText]}>
              {translate('createNewPassword')}
            </Text>
            <Text
              style={{
                marginTop: 10,
                fontFamily: FontFamily.regular,
                fontSize: 14,
                color: BaseColor.primary,
                textAlign: 'center',
                lineHeight: 20,
              }}>
              {
                'Please enter and confirm your new password. \n You will need to login after you reset.'
              }
            </Text>
          </View>
          <View
            style={[styles.inputWrapper, {marginTop: '15%', marginBottom: 15}]}>
            <View>
              <CInput
                title={translate('loginPassword')}
                placeholder={translate('********')}
                secureTextEntry={hidePwd.pass}
                value={newPassword}
                onChangeText={val => {
                  setNewPassword(val);
                  const lower = isLower(val);
                  if (lower) {
                    setIsLowerCase(true);
                  } else {
                    setIsLowerCase(false);
                  }
                  const upper = isUpper(val);
                  if (upper) {
                    setIsUpperCase(true);
                  } else {
                    setIsUpperCase(false);
                  }
                  const num = isNumericCheck(val);
                  if (num) {
                    setIsNumber(true);
                  } else {
                    setIsNumber(false);
                  }

                  const sp = isSpCheck(val);
                  if (sp) {
                    setIsSp(true);
                  } else {
                    setIsSp(false);
                  }
                  const tw = isTw(val);
                  if (tw) {
                    setIsTwelve(true);
                  } else {
                    setIsTwelve(false);
                  }

                  if (val.length <= 0) {
                    setIsUpperCase(false);
                    setIsLowerCase(false);
                    setIsNumber(false);
                    setIsTwelve(false);
                    setIsSp(false);
                  }
                  if (
                    isUpper(val) &&
                    isLower(val) &&
                    isNumericCheck(val) &&
                    isSpCheck(val) &&
                    isTw(val)
                  ) {
                    console.log('treuuuuu');
                    setShowCheckList(false);
                  } else {
                    setShowCheckList(true);
                  }
                }}
                onFocus={() => {
                  setShowCheckList(true);
                }}
                onBlur={() => {
                  setShowCheckList(false);
                }}
                onSubmitEditing={() => {
                  confirmPass.current.focus();
                }}
                rightIcon
                iconName={hidePwd.pass ? 'eye-close' : 'eye'}
                iconColor={
                  hidePwd.pass ? BaseColor.primary : BaseColor.blackColor
                }
                iconSize={24}
                onRightIconPress={() =>
                  setHidePwd({...hidePwd, pass: !hidePwd.pass})
                }
                placeholderTextColor={BaseColor.textGrey}
                showError={newPasswordError}
                errorMsg={newPasswordErrorTxt}
              />
            </View>
          </View>
          {showCheckList && (
            <View
              style={{
                marginHorizontal: 10,
                padding: 20,
                borderRadius: 10,
                shadowColor: 'purple',
                height: 150,
                width: '90%',
                backgroundColor: 'white',
                justifyContent: 'center',
                elevation: 10,
                shadowOffset: {width: 1, height: 1},
                shadowRadius: 3,
                shadowOpacity: 0.5,
              }}>
              <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                {translate('pswdReq')}
              </Text>
              <View style={{flexDirection: 'row'}}>
                <FAIcon
                  name={isLowerCase ? 'check' : 'remove'}
                  size={18}
                  color={isLowerCase ? BaseColor.green : BaseColor.alertRed}
                />
                <Text style={{marginLeft: 5}}>{translate('oneLowerCase')}</Text>
              </View>

              <View style={{flexDirection: 'row'}}>
                <FAIcon
                  name={isUpperCase ? 'check' : 'remove'}
                  size={18}
                  color={isUpperCase ? BaseColor.green : BaseColor.alertRed}
                />
                <Text style={{marginLeft: 5}}>{translate('oneUpperCase')}</Text>
              </View>

              <View style={{flexDirection: 'row'}}>
                <FAIcon
                  name={isNu ? 'check' : 'remove'}
                  size={18}
                  color={isNu ? BaseColor.green : BaseColor.alertRed}
                />
                <Text style={{marginLeft: 5}}>{translate('oneNumber')}</Text>
              </View>

              <View style={{flexDirection: 'row'}}>
                <FAIcon
                  name={isSpecial ? 'check' : 'remove'}
                  size={18}
                  color={isSpecial ? BaseColor.green : BaseColor.alertRed}
                />
                <Text style={{marginLeft: 5}}>{translate('oneSp')}</Text>
              </View>
              <View style={{flexDirection: 'row'}}>
                <FAIcon
                  name={isTwelve ? 'check' : 'remove'}
                  size={18}
                  color={isTwelve ? BaseColor.green : BaseColor.alertRed}
                />
                <Text style={{marginLeft: 5}}>{translate('twLong')}</Text>
              </View>
            </View>
          )}
          <View style={{marginBottom: 15}}>
            <CInput
              title={translate('confirmPassword')}
              onSubmitEditing={() => {
                Keyboard.dismiss();
              }}
              ref={confirmPass}
              placeholder={translate('********')}
              secureTextEntry={hidePwd.confirmPass}
              value={confirmPassword}
              onChangeText={val => {
                setConfirmPassword(val);
              }}
              rightIcon
              iconName={hidePwd.confirmPass ? 'eye-close' : 'eye'}
              iconColor={
                hidePwd.confirmPass ? BaseColor.primary : BaseColor.blackColor
              }
              iconSize={24}
              onRightIconPress={() =>
                setHidePwd({...hidePwd, confirmPass: !hidePwd.confirmPass})
              }
              placeholderTextColor={BaseColor.textGrey}
              showError={confirmPasswordError}
              errorMsg={confirmPasswordErrorTxt}
            />
          </View>
          <View style={{marginTop: '10%'}}>
            <CButton
              style={styles.loginBtn}
              title={translate('resetPassword')}
              onPress={() => {
                validation();
              }}
              loader={loader}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

export default UpdatePassword;
