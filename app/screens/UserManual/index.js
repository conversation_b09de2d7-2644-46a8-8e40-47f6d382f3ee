/* eslint-disable global-require */
/* eslint-disable quotes */
import {useTheme} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  Platform,
  Text,
  TouchableOpacity,
  View,
  PermissionsAndroid,
} from 'react-native';
import {check, PERMISSIONS} from 'react-native-permissions';
import {useSelector} from 'react-redux';
import {isArray} from 'lodash';
import RNFetchBlob from 'react-native-blob-util';
import Toast from 'react-native-simple-toast';
import SimpleToast from 'react-native-simple-toast';
import CHeader from '../../components/CHeader';
import {translate} from '../../lang/Translate';
import styles from './styles';
import {
  enableAnimateInEaseOut,
  openInAppBrowser,
} from '../../utils/commonFunction';
import {CustomIcon} from '../../config/LoadIcons';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import CustomHeader from '../../components/CustomHeader/CustomHeader';

/**
 *
 *@module Product
 *
 */
export default function MyQRcode({navigation}) {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const token = useSelector(state => state.auth.accessToken);

  const [pageLoad, setPageLoad] = useState(true);

  const [deviceList, setDeviceList] = useState([]);
  console.log(
    '🚀 ~ file: index.js ~ line 42 ~ MyQRcode ~ deviceList',
    deviceList,
  );
  const [page, setPage] = useState(1);

  useEffect(() => {
    setPage(1);
    getDeviceList();
  }, []);

  async function getDeviceList() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    const data = {
      platform: Platform.OS,
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.userManual,
        'POST',
        data,
        headers,
      );
      if (response.success && isArray(response.data)) {
        const arr = response.data;
        setDeviceList(arr);
      } else {
        setDeviceList([]);
      }
    } catch (error) {
      setDeviceList([]);
      console.log('error for device list ===', error);
    }
  }
  const checkPermission = () => {
    if (Platform.OS === 'android' && Platform.Version >= 23) {
      PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      ).then(result => {
        if (result) {
          console.log('Permission is OK');
        } else {
          PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          ).then(res => {
            if (res) {
              console.log('User accept');
            } else {
              console.log('User refuse');
            }
          });
        }
      });
    } else {
      check(PERMISSIONS.IOS.WRITE_EXTERNAL_STORAGE).then(res => {
        if (res !== 'granted') {
          console.log('permission granted === ');
        }
      });
    }
  };
  const renderProducts = ({item}) => (
    <TouchableOpacity
      style={[styles.cardRoot, {backgroundColor: BaseColor.lightgray}]}
      onPress={() => {
        checkPermission();
        console.log('dddddddddddddd file', item?.device_file);
        if (item?.device_file) {
          // openInAppBrowser(item?.device_file);
          try {
            const {fs} = RNFetchBlob;
            const {DownloadDir, DocumentDir} = fs.dirs;
            const saveFilePath =
              Platform.OS === 'ios' ? DocumentDir : DownloadDir;
            RNFetchBlob.config({
              fileCache: true,
              addAndroidDownloads: {
                useDownloadManager: true,
                notification: true,
                path: `${saveFilePath}/${
                  item?.device_name || 'Linko'
                }-UserManual.pdf`,
                description: 'Downloading.',
              },
              path: `${saveFilePath}/${
                item?.device_name || 'Linko'
              }-UserManual.pdf`,
            })
              .fetch('GET', item?.device_file, {})
              .then(res => {
                // the temp file path
                if (Platform.OS === 'ios') {
                  RNFetchBlob.ios.openDocument(res.path());
                  Toast.show('Downloded to files');
                } else {
                  Toast.show('Downloded to files');
                  console.log(
                    '🚀 ~ file: index.js ~ line 138 ~ .then ~ saveFilePath',
                    saveFilePath,
                  );
                }
              });
          } catch (error) {
            console.log(
              '🚀 ~ file: index.js ~ line 145 ~ MyQRcode ~ error',
              error,
            );
          }
        }
      }}>
      <View
        style={{
          alignSelf: 'flex-end',
          backgroundColor: BaseColor.whiteColor,
          borderRadius: 50,
          padding: 5,
        }}>
        <CustomIcon name="info1" size={26} color={BaseColor.org} />
      </View>
      <View style={styles.rowStyle}>
        <View>
          <Image
            source={
              item?.device_image
                ? {uri: item?.device_image}
                : require('../../assets/images/logo.png')
            }
            style={[styles.imgStyle, {borderColor: BaseColor.black60}]}
          />
          {item?.device_name && (
            <View
              style={{
                position: 'absolute',
                flex: 1,
                flexDirection: 'row',
                alignItems: 'center',
                bottom: 10,
                left: Platform.OS === 'ios' ? 80 : 80,
                right: Platform.OS === 'ios' ? 80 : 80,
                backgroundColor: '#fff',
                borderRadius: 12,
              }}>
              <Text style={[styles.childNameStyle]} numberOfLines={2}>
                {item.device_name}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  enableAnimateInEaseOut();

  return (
    <View style={[styles.root, {backgroundColor: BaseColor.whiteColor}]}>
      <View style={styles.root}>
        <CustomHeader
          title={translate('userManual')}
          leftIconName="left-arrow"
          onLeftPress={() => {
            navigation.goBack();
          }}
          backBtn
          rightIconName="bell-thick"
          onRightPress={() => {
            navigation.navigate('Alerts');
          }}
        />

        <FlatList
          keyExtractor={(item, index) => index}
          data={deviceList}
          renderItem={renderProducts}
          contentContainerStyle={{flexGrow: 1, paddingBottom: 24}}
          onEndReachedThreshold={0.4}
          ListEmptyComponent={() => (
            <View style={styles.emptyComponent}>
              {pageLoad ? (
                <ActivityIndicator color={BaseColor.whiteColor} />
              ) : (
                <Text
                  style={{
                    fontSize: 16,
                    color: BaseColor.whiteColor,
                    textAlign: 'center',
                  }}>
                  {translate('dataNotFound')}
                </Text>
              )}
            </View>
          )}
        />
      </View>
    </View>
  );
}
