/* eslint-disable global-require */
/* eslint-disable quotes */
import {useTheme} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  Platform,
  Text,
  TouchableOpacity,
  View,
  Dimensions,
  ScrollView,
  Alert,
  Modal,
} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import {findIndex, isArray} from 'lodash';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import Toast from 'react-native-simple-toast';
import Share from 'react-native-share';
import RNFetchBlob from 'react-native-blob-util';
import VLCPlayer from 'react-native-vlc-media-player/VLCPlayer';
import CHeader from '../../components/CHeader';
import {translate} from '../../lang/Translate';
import styles from './style';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import {FontFamily} from '../../config/typography';
import CAlert from '../../components/CAlert';
import {sendErrorReport} from '../../utils/commonFunction';
import bluetoothActions from '../../redux/reducers/bluetooth/actions';

/**
 *
 *@module VideoView
 *
 */
export default function VideoView({navigation, route}) {
  const dispatch = useDispatch();
  const colors = useTheme();
  const {fs} = RNFetchBlob;
  const pathr = useSelector(state => state.bluetooth.path);
  const filename = route?.params?.filename;
  const temp = `${pathr}/${filename}`;
  const [videoPathDisplay, setVideoPath] = useState(temp);
  const [showPlayer, setShowPlayer] = useState(false);
  const BaseColor = colors.colors;
  const {height: dHeight, width: dWidth} = Dimensions.get('window');
  const [loader, setLoader] = useState(false);
  const [paused, setPaused] = useState(false);
  const imageUri = route?.params?.imageUri;
  const aviFiles = useSelector(state => state.bluetooth.aviFiles);
  // console.log("avi filesss----", aviFiles);
  // console.log("save image", imageUri);
  const [AlerModal1, setAlerModal1] = useState({
    visible: false,
    title: '',
    message: '',
  });
  const {isBleConnected, isConnectedNet} = useSelector(
    state => state.bluetooth,
  );

  // http://192.168.4.1:8080/c?dwn=//${name}
  const fetchVideo = async () => {
    try {
      const {DownloadDir, DocumentDir} = fs.dirs;
      const downloadsPath = Platform.OS === 'ios' ? DocumentDir : DownloadDir;
      const videoFilename = filename; // Replace with the actual filename of your video
      const videoPath = `${downloadsPath}/${videoFilename}`;

      const videoExists = await fs.exists(videoPath);
      console.log('videoPath============', videoPath);
      if (videoExists) {
        setVideoPath(videoPath);
        setTimeout(() => {
          setShowPlayer(true);
        }, 100);
        console.log('Video file  exist.============', videoPath);
      } else {
        console.log('Video file does not ex?????????');
      }
    } catch (error) {
      console.log('Error fetching video:', error);
    }
  };
  useEffect(() => {
    fetchVideo();
  }, []);
  const apiVideo = async () => {
    try {
      await fetch('http://192.168.4.1:8080/getfiles')
        .then(response => response.text())
        .then(htmlData => {
          // Process the HTML data here
          const regex = /(<([^>]+)>)/gi;
          const result = htmlData?.replace(regex, '');
          // Alert.alert(JSON.stringify(result));
          sendErrorReport(JSON.stringify(htmlData), 'htmlData');
          const aviFiles = result // mansi please change ===========================================
            .match(/\/([\w\d]+\.avi)/gi)
            .map(file => file.substring(1).toLowerCase()); // convert to lowercase and remove leading slash
          // const aviFiles = result
          //   .match(/"\w+\.avi"/g)
          //   .map((file) => file.slice(1, -1));
          // Alert.alert(JSON.stringify(aviFiles));
          const uniqueArray = aviFiles.filter(
            (value, index, self) => self.indexOf(value) === index,
          );
          const newArr = uniqueArray.slice(1);

          Alert.alert(JSON.stringify(newArr));
          dispatch(bluetoothActions.setAVIFiles(newArr));
        })
        .catch(error => {
          // Handle any errors that occurred during the fetch
          sendErrorReport(JSON.stringify(error), 'htmlData---error');
          Alert.alert('error getting list');
        });
    } catch (err) {
      console.log('err ===========2==== ', err);
    }
  };
  const deleteVideo = async () => {
    // http://192.168.4.1:8080/c?del=helloWorld.avi
    // Alert.alert(filename);
    setLoader(true);
    setPaused(true);
    try {
      await fetch(`http://192.168.4.1:8080/c?del=${filename}`)
        .then(response => {
          response.text();
          // Alert.alert("deleted");
          // navigation.goBack();
          setLoader(false);
        })
        .then(async htmlData => {
          // Process the HTML data here
          setLoader(false);
          Alert.alert('Video is deleted.');
          const {DownloadDir, DocumentDir} = fs.dirs;
          const downloadsPath =
            Platform.OS === 'ios' ? DocumentDir : DownloadDir;
          const videoFilename = filename;
          const videoPath = `${downloadsPath}/${videoFilename}`;

          const videoExists = await fs.exists(videoPath);
          if (videoExists) {
            fs.unlink(videoPath);
            Alert.alert('Video is deleted in local.');
            apiVideo();
          } else {
            Alert.alert('Video is not deleted in local.');
          }

          setPaused(false);
          // sendErrorReport(JSON.stringify(htmlData), "htmlData");
          navigation.goBack();
          // also delete from
        })
        .catch(error => {
          // Handle any errors that occurred during the fetch
          sendErrorReport(JSON.stringify(error), 'delete---error');
          Alert.alert('error while delete');
          setLoader(false);
          setPaused(false);
        });
    } catch (err) {
      setLoader(false);
      setPaused(false);
      console.log('err ===========2==== ', err);
    }
  };
  const shareImage = url => {
    if (
      imageUri &&
      (imageUri.includes('http://') || imageUri.includes('https://'))
    ) {
      const shareResponse = Share.open({
        url,
        message: `Shared snapshot`,
      })
        .then(res1 => {
          console.log(res1);
        })
        .catch(err => {
          console.log(err);
        });
      console.log(shareResponse);
    } else {
      Toast.show(translate('urlError'));
    }
  };
  const onShare = async type => {
    try {
      if (type === 'share') {
        const {fs} = RNFetchBlob;
        let imagePath = null;
        RNFetchBlob.config({
          fileCache: true,
        })
          .fetch('GET', imageUri)
          // the image is now dowloaded to device's storage
          .then(resp => {
            // the image path you can use it directly with Image component
            imagePath = resp.path();
            return resp.readFile('base64');
          })
          .then(base64Data => {
            // here's base64 encoded image
            const base64 = `data:image/png;base64,${base64Data}`;
            shareImage(base64);
            // remove the file from storage
            return fs.unlink(imagePath);
          });
      } else {
        const result = await Share.open({
          message: `Shared snapshot`,
          // url: shareText,
        });
        if (result.action === Share.sharedAction) {
          if (result.activityType) {
            // shared with activity type of result.activityType
          } else {
            // shared
          }
        } else if (result.action === Share.dismissedAction) {
          // dismissed
        }
      }
    } catch (error) {
      console.log('error: ==>', error);
    }
  };

  return (
    <View style={[styles.root, {backgroundColor: BaseColor.homeBgColor}]}>
      {/* <GradientBack /> */}
      <View style={styles.root}>
        <CHeader
          title={filename}
          backBtn
          onLeftPress={() => {
            navigation.goBack();
          }}
        />
        <ScrollView
          contentContainerStyle={{paddingBottom: 0, marginTop: 0}}
          bounces={false}>
          <View
            style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
            {showPlayer ? (
              <VLCPlayer
                source={{
                  uri: `file://${videoPathDisplay}`,
                  // uri: "file:///var/mobile/Containers/Data/Application/85505243-94CD-4131-BC98-D79CCD169927/Documents/Am.avi",
                  // uri: "https://chillbaby-space.ams3.digitaloceanspaces.com/qeridoo/5e1afded-91a3-4ba3-a5a9-4eca8d7f3cb2.avi", // videoPath, // "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                }}
                style={{
                  width: 400,
                  height: 400,
                }}
                onError={res => {
                  console.log('VLCPlayer error', res);
                }}
                paused={paused}
              />
            ) : (
              <View
                style={{
                  width: 400,
                  height: 400,
                }}
              />
            )}
          </View>
          <View
            style={{
              backgroundColor: '#5F5F5F',
              padding: 18,
              borderTopStartRadius: 40,
              borderTopEndRadius: 40,
              marginTop: 15,
              paddingBottom: 200,
            }}>
            <View
              style={{
                backgroundColor: BaseColor.infoView,
                height: 84,
                flexDirection: 'row',
                borderRadius: 35,
                justifyContent: 'space-around',
                alignContent: 'center',
                alignItems: 'center',
                paddingHorizontal: 30,
              }}>
              <TouchableOpacity
                style={{
                  alignItems: 'center',
                  backgroundColor: BaseColor.blueDark,
                  height: 44,
                  width: 44,
                  borderRadius: 22,
                  justifyContent: 'center',
                }}
                onPress={() => {
                  onShare('share');
                }}>
                <FAIcon
                  name="cloud-upload"
                  size={20}
                  color={BaseColor.whiteColor}
                />
              </TouchableOpacity>
              <View
                style={{
                  height: 70,
                  opacity: 0.2,
                  width: 1,
                  backgroundColor: BaseColor.blackColor,
                }}
              />
              <TouchableOpacity
                style={{
                  alignItems: 'center',
                  backgroundColor: BaseColor.whiteColor,
                  height: 44,
                  width: 44,
                  borderRadius: 22,
                  borderWidth: 2,
                  borderColor: BaseColor.blueDark,
                  justifyContent: 'center',
                }}
                onPress={() => {
                  setAlerModal1({
                    visible: true,
                    title: 'Delete',
                    message: 'Are you sure you want to delete?',
                  });
                }}>
                <FAIcon name="trash" size={20} color={BaseColor.blueDark} />
              </TouchableOpacity>
            </View>
            <View
              style={{
                backgroundColor: BaseColor.blackColor,
                height: 1,
                marginHorizontal: 20,
                marginVertical: 20,
                opacity: 0.2,
              }}
            />
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignContent: 'center',
                alignItems: 'center',
              }}>
              <Image
                source={{
                  uri: imageUri,
                }}
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: 30,
                  marginHorizontal: 20,
                }}
              />
              <View>
                <Text
                  style={{
                    fontFamily: FontFamily.regular,
                    size: 16,
                    lineHeight: 20,
                    color: BaseColor.whiteColor,
                    letterSpacing: 0.7,
                  }}>
                  {filename}
                </Text>
                <Text
                  style={{
                    size: 12,
                    lineHeight: 20,
                    color: BaseColor.whiteColor,
                  }}>
                  {route?.params?.date}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
      <CAlert
        visible={AlerModal1.visible}
        onRequestClose={() =>
          setAlerModal1({
            ...AlerModal1,
            visible: false,
          })
        }
        onCancelPress={() =>
          setAlerModal1({
            ...AlerModal1,
            visible: false,
          })
        }
        onOkPress={async () => {
          setAlerModal1({
            ...AlerModal1,
            visible: false,
          });
          const hasFalse = aviFiles.some(item => item.status === false);
          if (!hasFalse) {
            deleteVideo();
          } else {
            Alert.alert(
              'Sorry, You can not delete video while other videos are downloading.',
            );
          }
        }}
        alertTitle={AlerModal1.title}
        alertMessage={AlerModal1.message}
        agreeTxt={translate('delete')}
      />
      <Modal
        visible={loader}
        transparent
        style={{
          flex: 1,
        }}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: BaseColor.black30,
          }}>
          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              justifyContent: 'center',
              alignItems: 'center',
              padding: 24,
              paddingHorizontal: 32,
              borderRadius: 24,
            }}>
            <ActivityIndicator size={24} color={BaseColor.blueDark} />
            <Text
              style={{
                color: BaseColor.blackColor,
                fontWeight: 'bold',
                marginTop: 8,
              }}>
              Loading
            </Text>
          </View>
        </View>
      </Modal>
    </View>
  );
}
