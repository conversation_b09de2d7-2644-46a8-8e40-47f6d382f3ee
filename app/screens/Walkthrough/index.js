/* eslint-disable max-len */
/* eslint-disable react/no-array-index-key */
/* eslint-disable global-require */
import React, {useEffect, useState} from 'react';
import {
  StatusBar,
  View,
  BackHandler,
  TouchableOpacity,
  Text,
  Image,
} from 'react-native';
import {useDispatch} from 'react-redux';
// import Animated, {
//   useAnimatedStyle,
//   useSharedValue,
//   withTiming,
// } from "react-native-reanimated";
import BaseColor from '../../config/colors';
import styles from './styles';
import CButton from '../../components/CButton';
import authActions from '../../redux/reducers/auth/actions';
import {enableAnimateInEaseOut} from '../../utils/commonFunction';
import {translate} from '../../lang/Translate';
import QPrimarySVG from '../../assets/images/QPrimary.svg';
import LineImgSVG from '../../assets/images/lineImg.svg';
import {FontFamily} from '../../config/typography';
import {SvgXml} from 'react-native-svg';
import commonSvg from '../../assets/commonSvg/commonnSvg';
import Orientation from 'react-native-orientation-locker';

const Walkthrough = ({navigation}) => {
  const animTime = 200;
  const [currentIndex, setcurrentIndex] = useState(0);

  const dispatch = useDispatch();

  // const anim = useSharedValue(1);
  // const nextImgAnim = useSharedValue(0);

  const slides = [
    {
      key: 'one',
      image: require('../../assets/images/newIntroPic.png'),
      // title: 'Welcome to Babyauto',
      title: translate('introTitle1'),
      text: `${translate('introText11')}`,
      // \n${translate(
      //   "introText12"
      // )}\n${translate("introText13")} ${translate("introText14")}`,
    },
    {
      key: 'two',
      image: require('../../assets/images/intro2.png'),
      // title: 'Connecting your device',
      title: translate('introTitle2'),
      // text:
      //   'During set-up you will need to locate\nyour personal QR code to scan with\nyou phone which will link to your\nnew Babyauto smart device.',
      text: `${translate('introText21')}`,
      //  ${translate(
      //   "introText22"
      // )} ${translate("introText23")} ${translate("introText24")}`,
    },
    {
      key: 'three',
      image: require('../../assets/images/intro3.png'),
      // title: 'Your digital partner',
      title: translate('introTitle3'),
      text: `${translate('introText31')}`,
      // ${translate(
      //   "introText32"
      // )} ${translate("introText33")} ${translate("introText34")}`,
    },
    {
      key: 'four',
      image: require('../../assets/images/newIntroPic.png'),
      // title: 'Your digital partner',
      title: '',
      text: `${translate('introText41')}`,
      // ${translate(
      //   "introText32"
      // )} ${translate("introText33")} ${translate("introText34")}`,
    },
  ];

  // const animStyle = useAnimatedStyle(() => ({
  //   opacity: withTiming(
  //     anim.value,
  //     {
  //       duration: animTime,
  //     },
  //     () => {
  //       anim.value = 1;
  //     }
  //   ),
  // }));

  // const titleStyle = useAnimatedStyle(() => ({
  //   opacity: withTiming(
  //     anim.value,
  //     {
  //       duration: animTime,
  //     },
  //     () => {
  //       anim.value = 1;
  //     }
  //   ),
  // }));

  // const desStyle = useAnimatedStyle(() => ({
  //   opacity: withTiming(
  //     anim.value,
  //     {
  //       duration: animTime,
  //     },
  //     () => {
  //       anim.value = 1;
  //     }
  //   ),
  // }));

  const {setWalkthrough} = authActions;

  function handleBackButtonClick() {
    BackHandler.exitApp();
    return true;
  }

  useEffect(() => {
    Orientation.lockToPortrait();
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  const changeIndex = () => {
    if (currentIndex !== 3) {
      // nextImgAnim.value = 0;
      // anim.value = 0.2;
      setTimeout(() => {
        const nIndex = currentIndex + 1;
        setcurrentIndex(nIndex);
      }, animTime);
    } else {
      navigation.navigate('Campaigns');
    }
  };

  const preIndex = () => {
    if (currentIndex !== 0) {
      // anim.value = 0.2;
      setTimeout(() => {
        const nIndex = currentIndex - 1;
        setcurrentIndex(nIndex);
      }, animTime);
    }
  };

  enableAnimateInEaseOut();
  return (
    <>
      <View style={{flex: 1}}>
        <View style={{flex: 1}}>
          <StatusBar
            backgroundColor="transparent"
            barStyle="dark-content"
            translucent
          />

          <Image
            style={[styles.image]}
            source={slides[currentIndex].image}
            resizeMode="cover"
          />
          <View style={styles.opacityView} />
          {/* <View style={styles.topBigQStyle}> */}
          {/* <QPrimarySVG height={135} /> */}
          {/* <View style={styles.welcomeViewStyle}>
              <Image
                source={require("../../assets/images/whiteLine.png")}
                style={{ height: 3 }}
              />
              <Text
                style={{
                  fontSize: 17,
                  color: BaseColor.whiteColor,
                  fontFamily: FontFamily.regular,
                  lineHeight: 25,
                }}
              >
                {" "}
                Welcome
              </Text>
            </View> */}
          {/* </View> */}

          <View
            style={{
              position: 'absolute',
              bottom: currentIndex === 3 ? 200 : 120,
              zIndex: 9,
            }}>
            {/* {currentIndex !== 3 && (
              <View style={{ alignItems: "center" }}>
                <SvgXml xml={commonSvg.QPrimarySVGXML} height={135} />
              </View>
            )} */}
            <Text style={[styles.title]}>
              {translate(slides[currentIndex].title)}
            </Text>
            <Text style={[styles.desc]}>{slides[currentIndex].text}</Text>
            {currentIndex === 3 && (
              <Text
                style={{
                  color: BaseColor.greyColor,
                  textAlign: 'center',
                  fontSize: 14,
                  fontFamily: FontFamily.robotoLight,
                  marginHorizontal: 30,
                  lineHeight: 25,
                  marginTop: -45,
                }}>
                Set preferences choosing the perfect settings for your unique
                family adventures!
              </Text>
            )}
          </View>
        </View>

        <View style={styles.touchableIndexViewStyle}>
          <TouchableOpacity style={{flex: 1}} onPress={preIndex} />
          <TouchableOpacity style={{flex: 2}} onPress={changeIndex} />
        </View>
        {/* {currentIndex === 2 ? (
          <View style={styles.buttonCircle}>
            <CButton
              iconname="check"
              iconsize={18}
              iconColor={BaseColor.blueLight}
              style={styles.checkBtnStyle}
              onPress={() => {
                navigation.navigate("RedirectLS");
                dispatch(setWalkthrough(false));
              }}
            />
          </View> */}
        {/* ) : ( */}

        {/* {currentIndex !== 3 ? ( */}
        <View
          style={{
            position: 'absolute',
            width: '100%',
            // backgroundColor: "red",
            flexDirection: currentIndex !== 3 ? 'row' : 'column',
            bottom: 75,
            alignItems: 'center',
            paddingHorizontal: 34,
            justifyContent: currentIndex !== 3 ? 'center' : 'space-between',
            // marginBottom: "10%",
            // borderWidth: 1,
            height: '10%',
          }}>
          <CButton
            style={{
              borderWidth: 1,
              borderColor: BaseColor.primary,
              backgroundColor: BaseColor.primary,
              height: 44,
              borderRadius: 29,
              width: currentIndex === 3 ? '60%' : '50%',
            }}
            title={
              currentIndex === 3 ? translate('Sounds Good') : translate('Next')
            }
            titleStyle={{
              fontFamily: FontFamily.robotoLight,
              fontWeight: '100',
              color: BaseColor.whiteColor,
              fontSize: 20,
              lineHeight: 20,
              letterSpacing: 0.75,
            }}
            onPress={changeIndex}
          />
          {currentIndex === 3 && (
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('RedirectLS');
                dispatch(setWalkthrough(false));
              }}>
              <Text
                style={{
                  fontSize: 18,
                  fontFamily: FontFamily.bold,
                  fontWeight: '100',
                  color: BaseColor.primary,
                  lineHeight: 20,
                  letterSpacing: 0.75,
                }}>
                No Thanks
              </Text>
            </TouchableOpacity>
          )}
        </View>
        {/* ) : (
          <View
            style={{
              position: "absolute",
              width: "100%",
              // backgroundColor: "red",
              flexDirection: "column",
              bottom: 95,
              // alignSelf: "center",
              paddingHorizontal: 34,
              alignItems: "center",
              // marginBottom: "10%",
            }}
          >
            <CButton
              style={{
                borderWidth: 1,
                borderColor: BaseColor.primary,
                backgroundColor: BaseColor.primary,
                height: 44,
                borderRadius: 29,
                width: "65%",
              }}
              title={translate("Sounds Good")}
              titleStyle={{
                fontFamily: FontFamily.robotoLight,
                fontWeight: "100",
                color: BaseColor.whiteColor,
                fontSize: 20,
                lineHeight: 20,
                letterSpacing: 0.75,
              }}
              onPress={changeIndex}
            />
            <TouchableOpacity
              onPress={() => {
                navigation.navigate("RedirectLS");
                dispatch(setWalkthrough(false));
              }}
              style={{ marginTop: 20 }}
            >
              <Text
                style={{
                  fontSize: 20,
                  fontFamily: FontFamily.robotoLight,
                  color: BaseColor.primary3,
                  letterSpacing: 0.75,
                }}
              >
                No thanks
              </Text>
            </TouchableOpacity>
          </View>
        )} */}
        <View style={styles.animViewStyle}>
          {slides.map((item, index) => (
            <View
              key={`i${index}`}
              style={{
                backgroundColor:
                  index === currentIndex ? BaseColor.primary : '#bfd6d5',
                margin: 4,
                borderRadius: 5,
                height: 5,
                width: index === currentIndex ? 30 : 5,
              }}
            />
          ))}
        </View>
        {/* )} */}
      </View>
    </>
  );
};

export default Walkthrough;
