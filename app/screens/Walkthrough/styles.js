import { Dimensions, StyleSheet } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  image: {
    width: Dimensions.get("screen").width,
    height: Dimensions.get("screen").height / 2.2,
    resizeMode: "cover",
  },
  contentView: {
    flex: 1,
    position: "absolute",
    bottom: 0,
    alignItems: "flex-end",
    width: Dimensions.get("screen").width,
    height: 350,
  },
  buttonCircle: {
    width: 40,
    height: 40,
    backgroundColor: "rgba(0, 0, 0, .2)",
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    bottom: 20,
    right: 20,
  },
  opacityView: {
    // backgroundColor: "#2F2F2FCF", //"rgba(52, 52, 52, 0.8)",
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    position: "absolute",
    // zIndex: 9,
  },
  topBigQStyle: {
    position: "absolute",
    top: 460,
    right: "30%",
    alignItems: "flex-end",
    zIndex: 99999,
  },
  welcomeViewStyle: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 30,
  },
  title: {
    color: BaseColor.primary,
    alignSelf: "center",
    fontSize: 30,
    lineHeight: 39,
    // marginRight: 30,
    marginBottom: 25,
    zIndex: 9999,
    fontFamily: FontFamily.bold,
  },
  desc: {
    color: BaseColor.blackColor,
    marginBottom: 80,
    textAlign: "center",
    marginHorizontal: 30,
    fontSize: 17,
    lineHeight: 25,
    zIndex: 9999,
    fontFamily: FontFamily.regular,
  },
  animViewStyle: {
    position: "absolute",
    // backgroundColor: "red",
    flexDirection: "row",
    bottom: 24,
    alignSelf: "center",
    paddingHorizontal: 34,
    justifyContent: "center",
    // zIndex: 9999,
  },
  touchableIndexViewStyle: {
    flex: 1,
    position: "absolute",
    height: "100%",
    width: "100%",
    flexDirection: "row",
    backgroundColor: "rgba(52, 52, 52, 0.1)",
  },
  checkBtnStyle: {
    width: 50,
    height: 50,
    borderRadius: 50,
    marginRight: 30,
    borderColor: BaseColor.textGrey,
    borderWidth: 0.2,
  },
});

export default styles;
