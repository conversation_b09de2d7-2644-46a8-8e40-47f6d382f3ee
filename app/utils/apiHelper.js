import {isEmpty} from 'lodash';
import BaseSetting from '../config/setting';
import {store} from '../redux/store/configureStore';
import axios from 'axios';
import {sendErrorReport} from './commonFunction';
import AuthAction from '../redux/reducers/auth/actions';
import ModalAuth from '../redux/reducers/modal/actions';
import PlaceAuth from '../redux/reducers/place/actions';
import {navigationRef} from '../navigation/RootNavigation';

export async function removeToken() {
  try {
    store.dispatch(AuthAction.logOut(''));
    store.dispatch(ModalAuth.setClearModalRedux());
    store.dispatch(PlaceAuth.setPlaceLocation({}));
    navigationRef?.current?.reset({
      index: 0,
      routes: [
        {
          name: 'RedirectLS',
        },
      ],
    });
  } catch (err) {
    console.log('ERRR==', err);
    sendErrorReport(err, 'remove_token_api_helper');
  }
}
export async function getApiData(
  endpoint,
  method,
  data = {},
  headers,
  isFormData = false,
) {
  const authState = store?.getState() || {};
  const token = authState?.auth?.accessToken || '';
  let authHeaders = {
    'Content-Type': 'application/json',
    authorization: token ? `Bearer ${token}` : '',
  };
  if (headers) {
    authHeaders = headers;
  }
  if (isFormData) {
    authHeaders = {
      'Content-Type': 'multipart/form-data',
      authorization: token ? `Bearer ${token}` : '',
    };
    const query = new FormData();
    if (data && Object.keys(data).length > 0) {
      Object.keys(data).map(k => query.append(k, data[k]));
    }
    data = query;
  }
  try {
    const obj = {
      method: method,
      url: BaseSetting.api + endpoint,
      // timeout: BaseSetting.timeOut,
      headers: authHeaders,
      data: !isEmpty(data) ? data : undefined,
    };
    let response = await axios(obj);
    if (response?.data?.message === 'Unauthorized') {
      removeToken();
      return;
    }
    return response.data;
  } catch (error) {
    if (error.response) {
      if (error?.response?.data?.message === 'Unauthorized') {
        removeToken();
        return;
      }
      return (
        error?.response?.data || {
          success: false,
          message: 'Something went wrong',
        }
      );
    } else {
      console.error(error);
      return {
        success: false,
        message: error.message || 'Something went wrong',
      };
    }
  }
}
