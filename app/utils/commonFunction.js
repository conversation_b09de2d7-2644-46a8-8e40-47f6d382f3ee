/* eslint-disable import/no-cycle */
/* eslint-disable quotes */
import {
  Alert,
  LayoutAnimation,
  Linking,
  PermissionsAndroid,
  Platform,
  UIManager,
} from 'react-native'; // import InAppBrowser from "react-native-inappbrowser-reborn";
import GetLocation from 'react-native-get-location';
import {has, isArray, isEmpty, isObject} from 'lodash';
import BaseColor from '../config/colors';
import BaseSetting from '../config/setting';
import {store} from '../redux/store/configureStore';
import {getApiData} from './apiHelper';
import AuthActions from '../redux/reducers/auth/actions';
import ModalAuth from '../redux/reducers/modal/actions';
import ReactNativeBlobUtil from 'react-native-blob-util';
import BluetoothActions from '../redux/reducers/bluetooth/actions';
import {
  openSettings,
  check,
  PERMISSIONS,
  // requestMultiple,
  request,
} from 'react-native-permissions';
import Toast from 'react-native-simple-toast';

const IOS = Platform.OS === 'ios';

export const enableAnimateInEaseOut = () => {
  if (Platform.OS === 'android') {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
};

export const enableAnimateLinear = () => {
  if (Platform.OS === 'android') {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.linear);
};

export const enableAnimateSpring = () => {
  if (Platform.OS === 'android') {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.spring);
};

export const getSWValue = (bleData, deviceDetail) => {
  // const devName = deviceDetail.device_bluetooth_name?.toLowerCase();
  // let multipleSensor = false;
  // if (devName) {
  //   multipleSensor =
  //     isObject(deviceDetail) &&
  //     !isEmpty(deviceDetail) &&
  //     (devName === "babyauto-csa" ||
  //       devName === "babyauto-csb" ||
  //       devName === "reebaby-csa" ||
  //       devName === "maxi-seat");
  // }

  const value =
    isObject(bleData) && !isEmpty(bleData) && has(bleData, 'SW')
      ? bleData.SW
      : bleData.s1;

  // if (!multipleSensor) {
  //   return value ? 0 : 1;
  // }
  return value;
};

export const getSWVal = (bleData, deviceDetail) => {
  const devName = deviceDetail?.device_bluetooth_name?.toLowerCase();
  let multipleSensor = false;
  if (devName) {
    multipleSensor =
      isObject(deviceDetail) &&
      !isEmpty(deviceDetail) &&
      (devName === 'babyauto-csa' ||
        devName === 'babyauto-csb' ||
        devName === 'reebaby-csa');
  }

  const value =
    isObject(bleData) && !isEmpty(bleData) && has(bleData, 'SW')
      ? bleData.SW
      : bleData.s1;

  // console.log(
  //   "🚀 ~ file: commonFunction.js ~ line 38 ~ getSWValue ~ multipleSensor",
  //   multipleSensor,
  //   devName,
  //   value,
  // );
  if (!multipleSensor) {
    return value ? 1 : 0;
  }
  return value;
};

// this function for add campaign actions
export async function addAction(item, type, token) {
  const {
    auth: {accessToken},
  } = store.getState();

  const headers = {
    'Content-Type': 'application/json',
    authorization: accessToken ? `Bearer ${accessToken}` : '',
  };
  try {
    const response = await getApiData(
      BaseSetting.endpoints.addAction,
      'POST',
      {
        campaign_id: item.id,
        type,
        platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
      },
      headers,
    );
    console.log(
      '🚀 ~ file: index.js ~ line 198 ~ addAction ~ response',
      response,
    );
  } catch (error) {
    sendErrorReport(error, 'add_action');
    console.log('add action error ===', error);
  }
}

// this function for handle user log
export async function setUserLog(id, token) {
  const headers = {
    'Content-Type': 'application/json',
    authorization: token ? `Bearer ${token}` : '',
  };

  try {
    const response = await getApiData(
      BaseSetting.endpoints.setUserLogActiveTime,
      'POST',
      {socket_id: id},
      headers,
    );
  } catch (error) {
    console.log('user log error ===', error);
  }
}

export const openInAppBrowser = async url => {
  // Checking if the link is supported for links with custom URL scheme.
  const supported = await Linking.canOpenURL(url);

  if (supported) {
    // Opening the link with some app, if the URL scheme is "http" the web link should be opened
    // by some browser in the mobile
    await Linking.openURL(url);
  } else {
    Alert.alert(`Don't know how to open this URL: ${url}`);
  }
};

export const getLocationAndStore = () => {
  const myApiKey = 'AIzaSyCoDO-inDu4lc0k7iZeO9Y-ZzkFELknuwk';
  const {
    auth: {userLocation},
  } = store.getState();

  if (!isEmpty(userLocation)) {
    return;
  }

  GetLocation.getCurrentPosition({
    enableHighAccuracy: true,
  })
    .then(location => {
      fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${location.latitude},${location.longitude}&key=${myApiKey}`,
      )
        .then(response => response.json())
        .then(responseJson => {
          store.dispatch(AuthActions.setUserLocation(responseJson));
        });
    })
    .catch(error => {
      const {code, message} = error;
      console.warn(code, message);
    });
};

//! This Function send Errors Data to API
export const sendErrorReport = async (errorObj, type) => {
  const {
    auth: {userData, userLocation},
  } = store.getState();

  const headers = {
    'Content-Type': 'application/json',
  };

  const data = {
    type,
    error: errorObj,
    location: userLocation,
    user_id: !isEmpty(userData) && userData?.id ? userData.id : 0,
    platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
    app: 'qeridoo',
  };

  // console.log("+++++++++++++ Sending Error REPORT USERDATA", userData);
  // console.log("+++++++++++++ Sending Error REPORT userLocation", userLocation);
  // console.log('+++++++++++++ Sending Error REPORT', data);

  try {
    const res = await getApiData(
      BaseSetting.endpoints.saveErrorLog,
      'POST',
      data,
      headers,
    );
    // console.log('+++++++++++++ Sending Error === SUCCESS', res);
  } catch (err) {
    console.log('+++++++++++++ Sending Error === FAILED', err);
  }
};

//! This Function send Errors Data to API
export const getUpdatedList = async () => {
  const {
    auth: {accessToken},
  } = store.getState();
  const headers = {
    'Content-Type': 'application/json',
    authorization: accessToken ? `Bearer ${accessToken}` : '',
  };

  const data = {
    platform: Platform.OS,
  };
  let d = [];
  try {
    const response = await getApiData(
      BaseSetting.endpoints.connectedDevice,
      'POST',
      data,
      headers,
    );
    sendErrorReport(response, 'response__childinFoList');

    if (response.success && isArray(response.data)) {
      d = [...response.data];
      // store.dispatch(BluetoothActions.setConnectedDeviceDetails(flattenDeep(response.data)));
    }
  } catch (error) {
    console.log(
      '🚀 ~ file: commonFunction.js ~ line 280 ~ getUpdatedList ~ error',
      error,
    );
  }
  return d;
};

// Function to calculate distance between two coordinates
export const calculateDistance = (lat1, lon1, lat2, lon2) => {
  let R = 6371; // Radius of the earth in km
  let dLat = deg2rad(lat2 - lat1);
  let dLon = deg2rad(lon2 - lon1);
  let a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  let c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  let d = R * c; // Distance in km
  return d;
};

function deg2rad(deg) {
  return deg * (Math.PI / 180);
}

export const createUniqueKey = () => {
  const now = new Date();
  const day = String(now.getDate()).padStart(2, '0');
  const month = String(now.getMonth() + 1).padStart(2, '0'); // January is 0!
  const year = now.getFullYear();
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const milliseconds = String(now.getMilliseconds()).padStart(3, '0');

  return `${day}${month}${year}${hours}${minutes}${seconds}${milliseconds}`;
};

export async function requestLocationPermission() {
  try {
    console.log('Location Permission called');
    if (Platform.OS === 'android' && Platform.Version >= 23) {
      console.log('called---7');
      check(PERMISSIONS.ACCESS_FINE_LOCATION).then(result => {
        if (result === 'granted') {
          console.log('Permission is OK');
        } else if (result === 'unavailable') {
          console.log('Location is unavailable Tada...');
          request(PERMISSIONS.ACCESS_FINE_LOCATION).then(res => {
            if (res === 'granted') {
              console.log('User accept');
            } else {
              console.log('User refuse');
              Alert.alert(
                'Location Permission',
                'Please allow location permission to continue the tracking feature.If permission already granted then ignore it.',
              );
            }
          });
        } else {
          request(PERMISSIONS.ACCESS_FINE_LOCATION).then(res => {
            if (res === 'granted') {
              console.log('User accept');
            } else {
              console.log('User refuse');
              Alert.alert(
                'Location Permission',
                'Please allow location permission to continue the tracking feature.',
              );
            }
          });
        }
      });
    }
  } catch (err) {
    console.warn(err);
  }
}

// Common Func for Download PDF
export const downloadFile = async (fileUrl, formName) => {
  const a = formName;
  try {
    const {config, fs} = ReactNativeBlobUtil;
    const {DownloadDir, DocumentDir} = fs.dirs;
    const saveFilePath = IOS ? DocumentDir : DownloadDir;
    let options = {};
    if (IOS) {
      options = {
        fileCache: true,
        path: saveFilePath + `/${a}`,
        notification: true,
      };
    } else {
      options = {
        fileCache: true,
        timeout: 1000 * 15,
        addAndroidDownloads: {
          useDownloadManager: true,
          notification: true,
          path: `/storage/emulated/0/Download/${a}`, // path for direct show into Download folder
          description: 'downloading file...',
        },
      };
    }
    config(options)
      .fetch('GET', fileUrl)
      .then(res => {
        if (IOS) {
          ReactNativeBlobUtil.ios.openDocument(res.path());
        }
        if (!IOS) {
          Toast.show(`${'File downloaded'}`);
        }
      })
      .catch(e => {
        Toast.show('C2Something went wrong!');
      });
  } catch (e) {
    Toast.show('C3Something went wrong!');
  }
};
// End

// get Slider API Integration..
export const getSliderDetail = async () => {
  try {
    const response = await getApiData(BaseSetting.endpoints.stepProcess, 'GET');
    if (response?.success) {
      const data = response?.data;
      let progres = 0;
      if (data?.signup >= 1 && data?.childprofile >= 1 && data?.product >= 1) {
        progres = 1;
      } else if (data?.signup >= 1 && data?.childprofile >= 1) {
        progres = 0.6;
      } else if (data?.signup >= 1) {
        progres = 0.3;
      }
      store.dispatch(ModalAuth.setSliderProgress(progres));
    } else {
      store.dispatch(ModalAuth.setSliderProgress(0.3));
    }
  } catch (err) {
    console.log('ERRR==', err);
  }
};

export const fileTypes = {
  'image/jpeg': 'JPEG',
  'image/png': 'PNG',
  'image/heic': 'HEIC',
  'image/heif': 'HEIF',
};

// Check Image Size....
export function checkFileVal(type, size) {
  const fTypes = isObject(fileTypes) ? fileTypes : {};
  if (has(fTypes, type)) {
    if (size > 1024 * 1024 * 1) {
      return false;
    } else {
      return true;
    }
  }
}

// this function for page view status
/** this function for page view status
 * @function savePageViewData
 * @param {*} screen
 * @returns
 */
export async function savePageViewData(screen) {
  const data = {
    current_page: screen,
  };
  try {
    const response = await getApiData(
      BaseSetting.endpoints.pageViewData,
      'POST',
      data,
    );
    console.log('🚀 ~ savePageViewData ~ response:', response);

    if (response.success) {
      console.log('savePageViewData success');
    } else {
      console.log('savePageViewData error--');
    }
  } catch (error) {
    console.log('savePageViewData error', error);
  }
}
