const fetchWeatherData = async (lat, lon) => {
  const apiKey = "b79f1143b7f5e1e8bc3ffe186b5440eb";
  try {
    const response = await fetch(
      `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${apiKey}&units=metric`
    );
    const data = await response.json();
    const result = formatWeatherData(data);
    return result;
  } catch (error) {
    console.error(error);
  }
};

const formatWeatherData = (data) => {
  const { name, weather, main } = data;
  const { temp, humidity } = main;
  const { description } = weather[0];

  let message = `In ${name}, it's ${temp}°C.`;

  switch (true) {
    case description.includes("rain"):
      message += " Remember to take an umbrella! ☔";
      break;
    case description.includes("cloud"):
      message += " It might be a bit gloomy today. ☁️";
      break;
    case description.includes("sun"):
      message += " Enjoy the sunshine! ☀️";
      break;
    case description.includes("clear sky"):
      message += " The sky is clear today. Enjoy the beautiful day! 🌈";
      break;
    case description.includes("thunderstorm"):
      message += " There's a thunderstorm. Stay safe! ⛈️";
      break;
    case description.includes("mist"):
      message += " It's misty today. Drive safely! 🌫️";
      break;
    case description.includes("smoke"):
      message += " There's smoke in the air. Be careful! 🌫️";
      break;
    case description.includes("sand"):
      message += " There's a sandstorm. Stay indoors! 🏜️";
      break;
    case description.includes("tornado"):
      message += " There's a tornado. Seek shelter immediately! 🌪️";
      break;
    case description.includes("dust"):
      message += " It's dusty today. You might want to wear a mask! 😷";
      break;
    default:
      message += " 😊";
      break;
  }

  return message;
};

export default fetchWeatherData;
