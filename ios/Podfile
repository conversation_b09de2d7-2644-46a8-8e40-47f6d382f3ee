def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
      '#{script}',
      {paths: [process.argv[1]]},
    )", __dir__]).strip
end
node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

platform :ios, '14.0'
prepare_react_native_project!

# Add React Native Maps dependencies here
rn_maps_path = '../node_modules/react-native-maps'
pod 'react-native-google-maps', :path => rn_maps_path
pod 'GoogleMaps', '7.4.0'  # Add specific GoogleMaps version


# ⬇️ uncomment wanted permissions
setup_permissions([
  'Bluetooth',
  'Camera',
  'LocationWhenInUse',
  'Microphone',
  'Notifications',
  'PhotoLibrary',
  'Motion',
])


linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'qeridoo' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )


  # CodePush plugin dependency
  pod 'CodePush', :path => '../node_modules/react-native-code-push'
  # Required by RNFirebase
  pod 'FirebaseCore', :modular_headers => true
  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true

  target 'qeridooTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|

    ################### FOR REMOVING DUPLICASY #########################
    installer.pods_project.targets.each do |target|
     if target.name == "React-Core-RCTI18nStrings"
      target.remove_from_project
     end
    end
    ###############################################################

    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
  end
end
