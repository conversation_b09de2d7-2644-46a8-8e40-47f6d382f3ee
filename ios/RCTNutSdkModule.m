/* RCTNUtSdkModule.m*/

#import "RCTNutSdkModule.h"
#import <React/RCTLog.h>
#import <nutble/nutble.h>
#import <nutble/nutble-Swift.h>
#import <React/RCTEventEmitter.h>

@interface RCTNutSdkModule() <NTDeviceManagerDelegate, NTDeviceDelegate>
@property (nonatomic, strong) NSMutableDictionary *discoveredDevices;
@property (nonatomic,strong) NTDevice *selectedDevice;
@property (nonatomic, strong) CBCentralManager *centralManager;
@end

@implementation RCTNutSdkModule

// To export a module named NutSdkModule
RCT_EXPORT_MODULE(NutSdkModule);

- (instancetype)init {
  self = [super init];
  if (self) {
    [[NTDeviceManager sharedManager] setDelegate:self];
    // Initialize the SDK with your openID if required by the SDK
    [NTDeviceManager sharedManager].openID = @"e613fd6470a79fd4147f041810dce52f";
    // Initialize the discoveredDevices dictionary
      self.discoveredDevices = [NSMutableDictionary dictionary];
      self.centralManager = [[CBCentralManager alloc] initWithDelegate:nil queue:nil options:nil]; // Initialize CBCentralManager

  }
  return self;
}

RCT_EXPORT_METHOD(createEventInNativeIOS:(NSString *)name
                  location:(NSString *)location
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  RCTLogInfo(@"Pretending to create an event %@ at %@", name, location);

  // Example of error handling: reject if either parameter is empty
  if (name == nil || [name length] == 0 || location == nil || [location length] == 0) {
    NSError *error = [NSError errorWithDomain:@"InvalidParameters"
                                         code:400
                                     userInfo:@{NSLocalizedDescriptionKey:@"Name and location must be provided"}];
    reject(@"invalid_parameters", @"Name and location must be provided", error);
    return;
  }

  // Construct the response string
  NSString *response = [NSString stringWithFormat:@"Event created: %@ at %@ - I am coming from Native Objective C", name, location];

  // Resolve the promise with the response string
  resolve(response);
}

RCT_EXPORT_METHOD(startScanning) {
  RCTLogInfo(@"startScan function called");
  [[NTDeviceManager sharedManager] startScanWithOptions:@{NTCentralManagerScanOptionAllowDuplicatesKey: @YES}];
}

RCT_EXPORT_METHOD(stopScanning) {
  RCTLogInfo(@"stopScan function called");
  [[NTDeviceManager sharedManager] stopScan];
}

RCT_EXPORT_METHOD(connectToDevice:(NSString *)identifier
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  
  RCTLogInfo(@"Connecting to device with identifier: %@", identifier);
  // Log the device dictionary
   RCTLogInfo(@"Discovered Devices Dictionary: %@", self.discoveredDevices);
  // Retrieve the array containing the NTDevice object and device data dictionary
   NSArray *deviceInfoArray = self.discoveredDevices[identifier];
   
   if (deviceInfoArray) {
     NTDevice *device = deviceInfoArray[0];
     NSDictionary *deviceData = deviceInfoArray[1];
     
     RCTLogInfo(@"Device-->> %@", device);
     RCTLogInfo(@"Device-->> %@", deviceData);
     
     if (device) {
       [device connect];
       
       // Resolve with the device data dictionary
       resolve(deviceData);
       self.selectedDevice = device;
       [device setDelegate:self];
       device.autoReconnect = YES;
       [device setHardwareAlarmEnabled:YES];
     } else {
       RCTLogInfo(@"Device with identifier %@ not found", identifier);
       reject(@"Connection failed", [NSString stringWithFormat:@"Device with identifier %@ not found", identifier], nil);
     }
   } else {
     RCTLogInfo(@"No devices scanned. Please start scanning first.");
     reject(@"Connection failed", [NSString stringWithFormat:@"No devices scanned. Please start scanning first."], nil);
   }
 }


RCT_EXPORT_METHOD(disconnectFromDevice:(NSString *)identifier
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  // Retrieve the array containing the NTDevice object and device data dictionary
   NSArray *deviceInfoArray = self.discoveredDevices[identifier];
  if (deviceInfoArray) {
    NTDevice *device = deviceInfoArray[0];
    NSDictionary *deviceData = deviceInfoArray[1];
    
    RCTLogInfo(@"Device-->> %@", device);
    RCTLogInfo(@"Device-->> %@", deviceData);
    
    if (device) {
      [device cancelConnection];
      
      // Resolve with the device data dictionary
      resolve(deviceData);
    } else {
      RCTLogInfo(@"Device with identifier %@ not found", identifier);
      reject(@"DisConnection failed", [NSString stringWithFormat:@"Device with identifier %@ not found", identifier], nil);
    }
  } else {
    RCTLogInfo(@"Device with identifier %@ not found in discovered devices", identifier);
    reject(@"DisConnection failed", [NSString stringWithFormat:@"Device not found with address"], nil);
  }
 
}

RCT_EXPORT_METHOD(shutdownDevice:(NSString *)identifier resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
  NTDevice *device = self.discoveredDevices[identifier];
  if (device && [device shutdown]) {
    resolve(@(YES));
  } else {
    reject(@"shutdown_failed", @"Failed to shutdown device", nil);
  }
}

RCT_EXPORT_METHOD(beepDevice:(NSString *)identifier 
                  enabled:(BOOL)enabled timeout:(NSInteger)timeout
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  // Retrieve the array containing the NTDevice object and device data dictionary
  NSArray *deviceInfoArray = self.discoveredDevices[identifier];
  
  if (deviceInfoArray) {
    NTDevice *device = deviceInfoArray[0];
    
    if (device) {
      // Attempt to beep the device
      BOOL success = [device beep:enabled withTimeOutDuration:timeout];
      if (success) {
        resolve(@(YES));
      } else {
        RCTLogInfo(@"Failed to beep device with identifier %@", identifier);
        reject(@"beep_failed", [NSString stringWithFormat:@"Failed to beep device with identifier %@", identifier], nil);
      }
    } else {
      RCTLogInfo(@"Device object not found for identifier %@", identifier);
      reject(@"beep_failed", [NSString stringWithFormat:@"Device object not found for identifier %@", identifier], nil);
    }
  } else {
    RCTLogInfo(@"Device with identifier %@ not found in discovered devices", identifier);
    reject(@"beep_failed", [NSString stringWithFormat:@"Device with identifier %@ not found in discovered devices", identifier], nil);
  }
}



RCT_EXPORT_METHOD(setHardwareAlarmEnabled:(NSString *)identifier enabled:(BOOL)enabled resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
  NTDevice *device = self.discoveredDevices[identifier];
  if (device && [device setHardwareAlarmEnabled:enabled]) {
    resolve(@(YES));
  } else {
    reject(@"set_alarm_failed", @"Failed to set hardware alarm", nil);
  }
}

RCT_EXPORT_METHOD(readDeviceRSSI:(NSString *)identifier) {
  NTDevice *device = self.discoveredDevices[identifier];
  if (device) {
    [device readRSSI];
  } else {
    RCTLogError(@"Device with identifier %@ not found", identifier);
  }
}

RCT_EXPORT_METHOD(readBattery:(NSString *)identifier) {
  // Retrieve the array containing the NTDevice object and device data dictionary
  NSArray *deviceInfoArray = self.discoveredDevices[identifier]; // Retrieve the array containing the NTDevice
  if (deviceInfoArray) {
    NTDevice *device = deviceInfoArray[0];
    RCTLogInfo(@"selected device %@" , self.selectedDevice);
    if (device) {
      RCTLogInfo(@"Read BAttery in Native call %@",device);
      [device readBattery];
    } else {
      RCTLogInfo(@"Device with identifier %@ not found", identifier);
    }
  }
}

// List of supported events
- (NSArray<NSString *> *)supportedEvents {
  return @[@"discoveredDevice", @"didUpdateState", @"didClicked", @"didFailedToConnect", @"didUpdateBattery", @"didUpdateRSSI", @"deviceDidConnect", @"deviceDidDisconnected"];
}

// Handle device discovery
- (void)deviceManager:(NTDeviceManager *)manager didDiscoveredDevice:(NTDevice *)device RSSI:(NSNumber *)rssi {
  NSString *deviceInfo = [NSString stringWithFormat:@"Discovered Device:\n  - Identifier: %@\n  - RSSI: %@", device.identifier, rssi];
  RCTLogInfo(@"Discovered Devices :- %@", deviceInfo);
  NSDictionary *deviceData = @{ @"name": @"nut", @"address": device.identifier, @"rssi": rssi , @"id" : device.identifier , @"productId" : device.identifier};
  // Store both the NTDevice object and deviceData dictionary in an array
    NSArray *deviceInfoArray = @[device, deviceData];
  
  self.discoveredDevices[device.identifier] = deviceInfoArray;
  [self sendEventWithName:@"discoveredDevice" body:deviceData];
}

// Handle state updates
- (void)deviceManager:(NTDeviceManager *)manager didUpdateState:(CBCentralManagerState)state {
  RCTLogInfo(@"Central manager state updated to %ld", (long)state);
  [self sendEventWithName:@"didUpdateState" body:@{@"state": @(state)}];
}

// Handle device click events
- (void)device:(NTDevice *)device didClicked:(NSInteger)numberOfClick {
  RCTLogInfo(@"Device with identifier %@ clicked %ld times", device.identifier, (long)numberOfClick);
  [self sendEventWithName:@"didClicked" body:@{@"device": device.identifier, @"numberOfClick": @(numberOfClick)}];
}

// Handle failed connections
- (void)device:(NTDevice *)device didFailedToConnect:(NSError *)error {
  RCTLogError(@"Failed to connect to device with identifier %@, error: %@", device.identifier, error.localizedDescription);
  [self sendEventWithName:@"didFailedToConnect" body:@{@"device": device.identifier, @"error": error.localizedDescription}];
}

// Handle battery updates
- (void)device:(NTDevice *)device didUpdateBattery:(NSNumber *)battery {
  RCTLogInfo(@"Device with identifier %@ battery level updated to %@", device.identifier, battery);
  [self sendEventWithName:@"didUpdateBattery" body:@{@"device": device.identifier, @"battery": battery}];
}

// Handle RSSI updates
- (void)device:(NTDevice *)device didUpdateRSSI:(NSNumber *)RSSI {
  RCTLogInfo(@"Device with identifier %@ RSSI updated to %@", device.identifier, RSSI);
  [self sendEventWithName:@"didUpdateRSSI" body:@{@"device": device.identifier, @"rssi": RSSI}];
}

// Handle device connections
- (void)deviceDidConnect:(NTDevice *)device {
  RCTLogInfo(@"Device with identifier %@ connected", device.identifier);
  [self sendEventWithName:@"deviceDidConnect" body:@{@"device": device.identifier}];
}

// Handle device disconnections
- (void)deviceDidDisconnected:(NTDevice *)device {
  RCTLogInfo(@"Device with identifier %@ disconnected", device.identifier);
  [self sendEventWithName:@"deviceDidDisconnected" body:@{@"device": device.identifier}];
//  [self.discoveredDevices removeObjectForKey:device.identifier];
}

// Check if device is connected
RCT_EXPORT_METHOD(isConnected:(NSString *)identifier
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  NTDevice *device = self.discoveredDevices[identifier][0];
  
  if (device) {
    BOOL isConnected = device.state == NTDeviceStateConnected;
    resolve(@(isConnected));
  } else {
    NSError *error = [NSError errorWithDomain:@"DeviceNotFound"
                                         code:404
                                     userInfo:@{NSLocalizedDescriptionKey: @"Device not found"}];
    reject(@"device_not_found", @"Device not found", error);
  }
}

RCT_EXPORT_METHOD(checkBluetoothStatus:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  CBCentralManagerState state = self.centralManager.state;
  BOOL isBluetoothOn = (state == CBCentralManagerStatePoweredOn);
  
  resolve(@(isBluetoothOn));
}




@end
