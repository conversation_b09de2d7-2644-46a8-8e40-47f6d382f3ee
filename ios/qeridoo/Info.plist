<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>com.transistorsoft.fetch</string>
		<string>com.transistorsoft.customtask</string>
	</array>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Qeridoo</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIconName</key>
	<string>AppIcon</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>CodePushDeploymentKey</key>
	<string>yd9kQocOlRkYGAHYvTpJ0ebiC0YytA64OE_EI</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Qeridoo need to access your bluetooth to connect device</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Qeridoo need to access your bluetooth to connect device</string>
	<key>NSCameraUsageDescription</key>
	<string>Qeridoo need to access your camera to scan your device QRcode and send the attachment to other user in the chat.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Qeridoo needs access to your location even when the app is in the background for accurate trip tracking.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Qeridoo need to access your current location for tracking trip</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Qeridoo needs continuous access to location for accurate trip tracking even when the app is in background.</string>

	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>PreciseLocation</key>
		<string>Qeridoo needs precise location access for accurate trip tracking and navigation.</string>
	</dict>
	<key>NSMicrophoneUsageDescription</key>
	<string>Qeridoo need to access Microphone send the attachment to other user in the chat.</string>
	<key>NSMotionUsageDescription</key>
	<string>Qeridoo uses motion to detect trip activity and improve location tracking.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Qeridoo need to access your photos library for change child profile picture and send
			the attachment to other user in the chat.</string>
	<key>UIAppFonts</key>
	<array>
		<string>Magistral-Book.ttf</string>
		<string>Magistral-Regular.otf</string>
		<string>Magistral-W01-Bold.ttf</string>
		<string>MagistralBlack.otf</string>
		<string>icomoon.ttf</string>
		<string>icomoon.woff</string>
		<string>Roboto-Black.ttf</string>
		<string>Roboto-Bold.ttf</string>
		<string>Roboto-Light.ttf</string>
		<string>Roboto-Regular.ttf</string>
		<string>Roboto-Thin.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Fontisto.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>bluetooth-central</string>
		<string>remote-notification</string>
		<string>location</string>
		<string>fetch</string>
		<!-- <string>processing</string> -->
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
