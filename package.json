{"name": "qeridoo", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "debug": "ENV=qeridoo && cd android && ./gradlew clean && ENVFILE=.env  && ./gradlew assembleDebug", "apprelease": "ENV=qeridoo && cd android && ./gradlew clean && ENVFILE=.qeridoo.env ./gradlew assembleRelease", "release": "cd android && ./gradlew clean && ./gradlew assembleRelease", "postinstall": "patch-package"}, "dependencies": {"@kichiyaki/react-native-barcode-generator": "^0.6.7", "@mapbox/polyline": "^1.2.1", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-clipboard/clipboard": "^1.14.1", "@react-native-community/datetimepicker": "^8.0.1", "@react-native-community/netinfo": "^11.3.2", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-community/slider": "^4.5.2", "@react-native-firebase/app": "^20.0.0", "@react-native-firebase/messaging": "^20.0.0", "@react-native-masked-view/masked-view": "^0.3.1", "@react-navigation/bottom-tabs": "^5.11.8", "@react-navigation/drawer": "^6.6.15", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "@turf/turf": "^7.2.0", "@twotalltotems/react-native-otp-input": "^1.3.11", "axios": "^1.7.7", "geolib": "^3.3.4", "haversine-distance": "^1.2.3", "i18n-js": "3.9.2", "lodash": "^4.17.21", "moment": "^2.30.1", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "18.2.0", "react-native": "0.74.1", "react-native-action-button": "^2.8.5", "react-native-android-open-settings": "^1.3.0", "react-native-android-wifi": "^0.0.41", "react-native-animatable": "^1.4.0", "react-native-background-fetch": "^4.2.7", "react-native-background-geolocation": "^4.18.7", "react-native-background-timer": "^2.4.1", "react-native-ble-manager": "^11.5.3", "react-native-ble-plx": "^3.1.2", "react-native-blob-util": "^0.19.11", "react-native-bluetooth-state-manager": "^1.3.5", "react-native-bluetooth-status": "^1.5.1", "react-native-camera": "^4.2.1", "react-native-card-stack-swiper": "^1.2.5", "react-native-circular-progress": "^1.4.0", "react-native-code-push": "^8.2.2", "react-native-config": "^1.5.1", "react-native-countdown-circle-timer": "^3.2.1", "react-native-country-picker-modal": "^2.0.0", "react-native-date-picker": "^5.0.7", "react-native-deck-swiper": "^2.0.17", "react-native-device-info": "^11.1.0", "react-native-document-picker": "^9.2.0", "react-native-element-dropdown": "^2.12.2", "react-native-event-listeners": "^1.0.7", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.16.2", "react-native-get-location": "2.2.1", "react-native-image-crop-picker": "^0.41.1", "react-native-in-app-notification": "^3.2.0", "react-native-iphone-x-helper": "^1.3.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "1.7.1", "react-native-maps-directions": "^1.9.0", "react-native-mmkv": "2.12.2", "react-native-orientation-locker": "^1.7.0", "react-native-permissions": "^4.1.5", "react-native-progress": "^5.0.1", "react-native-push-notification": "^8.1.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-qrcode-svg": "^6.3.1", "react-native-render-html": "^6.3.4", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^4.10.1", "react-native-screens": "^3.31.1", "react-native-select-dropdown": "^4.0.1", "react-native-share": "^10.2.1", "react-native-simple-toast": "^3.3.1", "react-native-splash-screen": "^3.3.0", "react-native-status-bar-height": "^2.6.0", "react-native-svg": "^15.3.0", "react-native-swiper": "^1.6.0", "react-native-switch": "^1.5.1", "react-native-tooltip-progress-bar": "^1.1.5", "react-native-vector-icons": "^10.1.0", "react-native-view-shot": "^3.6.0", "react-native-vlc-media-player": "^1.0.67", "react-native-webview": "^13.10.2", "react-native-wifi-reborn": "^4.12.1", "react-native-xml2js": "^1.0.3", "react-redux": "^8.0.5", "redux": "^4.2.0", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "sails.io.js": "^1.2.1", "socket.io-client": "^2.0.3", "toggle-switch-react-native": "^3.3.0", "yarn": "^1.22.22"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.83", "@react-native/eslint-config": "0.74.83", "@react-native/metro-config": "0.74.83", "@react-native/typescript-config": "0.74.83", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^9.5.0", "eslint-plugin-react": "^7.34.2", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}